import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';

// Theme state class
class ThemeState {
  final int themeIndex;
  final bool isDark;
  final ThemeData themeData;

  const ThemeState({
    required this.themeIndex,
    required this.isDark,
    required this.themeData,
  });

  ThemeState copyWith({int? themeIndex, bool? isDark, ThemeData? themeData}) {
    return ThemeState(
      themeIndex: themeIndex ?? this.themeIndex,
      isDark: isDark ?? this.isDark,
      themeData: themeData ?? this.themeData,
    );
  }
}

// Theme notifier
class ThemeNotifier extends StateNotifier<ThemeState> {
  static const String _themeIndexKey = 'theme_index';
  static const String _isDarkKey = 'is_dark';

  ThemeNotifier()
    : super(
        ThemeState(
          themeIndex: 0,
          isDark: false,
          themeData: AppTheme.getTheme(0, isDark: false),
        ),
      ) {
    _loadThemePreferences();
  }

  // Load theme preferences from SharedPreferences
  Future<void> _loadThemePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeIndex = prefs.getInt(_themeIndexKey) ?? 0;
      final isDark = prefs.getBool(_isDarkKey) ?? false;

      state = ThemeState(
        themeIndex: themeIndex,
        isDark: isDark,
        themeData: AppTheme.getTheme(themeIndex, isDark: isDark),
      );
    } catch (e) {
      // If there's an error loading preferences, use defaults
      debugPrint('Error loading theme preferences: $e');
    }
  }

  // Save theme preferences to SharedPreferences
  Future<void> _saveThemePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeIndexKey, state.themeIndex);
      await prefs.setBool(_isDarkKey, state.isDark);
    } catch (e) {
      debugPrint('Error saving theme preferences: $e');
    }
  }

  // Change theme color scheme
  Future<void> changeTheme(int themeIndex) async {
    state = ThemeState(
      themeIndex: themeIndex,
      isDark: state.isDark,
      themeData: AppTheme.getTheme(themeIndex, isDark: state.isDark),
    );
    await _saveThemePreferences();
  }

  // Toggle dark mode
  Future<void> toggleDarkMode() async {
    final isDark = !state.isDark;
    state = ThemeState(
      themeIndex: state.themeIndex,
      isDark: isDark,
      themeData: AppTheme.getTheme(state.themeIndex, isDark: isDark),
    );
    await _saveThemePreferences();
  }

  // Set dark mode explicitly
  Future<void> setDarkMode(bool isDark) async {
    if (state.isDark != isDark) {
      state = ThemeState(
        themeIndex: state.themeIndex,
        isDark: isDark,
        themeData: AppTheme.getTheme(state.themeIndex, isDark: isDark),
      );
      await _saveThemePreferences();
    }
  }

  // Get current color scheme
  ColorScheme get colorScheme => state.themeData.colorScheme;

  // Get theme name
  String get themeName => AppTheme.themeNames[state.themeIndex];
}

// Theme provider
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeState>((ref) {
  return ThemeNotifier();
});

// Convenience providers
final currentThemeProvider = Provider<ThemeData>((ref) {
  return ref.watch(themeProvider).themeData;
});

final colorSchemeProvider = Provider<ColorScheme>((ref) {
  return ref.watch(themeProvider).themeData.colorScheme;
});

final isDarkModeProvider = Provider<bool>((ref) {
  return ref.watch(themeProvider).isDark;
});

final themeIndexProvider = Provider<int>((ref) {
  return ref.watch(themeProvider).themeIndex;
});

final themeNameProvider = Provider<String>((ref) {
  final index = ref.watch(themeProvider).themeIndex;
  return AppTheme.themeNames[index];
});

// Legacy providers for backward compatibility
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((
  ref,
) {
  return ThemeModeNotifier();
});

final lightThemeProvider = Provider<ThemeData>((ref) {
  final themeIndex = ref.watch(themeProvider).themeIndex;
  return AppTheme.getTheme(themeIndex, isDark: false);
});

final darkThemeProvider = Provider<ThemeData>((ref) {
  final themeIndex = ref.watch(themeProvider).themeIndex;
  return AppTheme.getTheme(themeIndex, isDark: true);
});

// Legacy theme mode notifier for backward compatibility
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system);

  void setThemeMode(ThemeMode mode) {
    state = mode;
  }

  void toggleTheme() {
    state = state == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
  }
}
