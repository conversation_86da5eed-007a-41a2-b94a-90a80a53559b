import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/money_flow_provider.dart';
import '../models/category_model.dart';
import 'create_category_screen.dart';

class CategoriesTab extends ConsumerStatefulWidget {
  const CategoriesTab({super.key});

  @override
  ConsumerState<CategoriesTab> createState() => _CategoriesTabState();
}

class _CategoriesTabState extends ConsumerState<CategoriesTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Categories'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Expense', icon: Icon(Icons.remove_circle_outline)),
            Tab(text: 'Income', icon: Icon(Icons.add_circle_outline)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToCreateCategory(context),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          categoriesAsync.when(
            data: (categories) =>
                _buildCategoryList(context, categories, CategoryType.expense),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
          ),
          categoriesAsync.when(
            data: (categories) =>
                _buildCategoryList(context, categories, CategoryType.income),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryList(
    BuildContext context,
    List<dynamic> categories,
    CategoryType type,
  ) {
    final filteredCategories = categories
        .where((category) => category.type == type && category.isActive)
        .toList();

    if (filteredCategories.isEmpty) {
      return _buildEmptyState(context, type);
    }

    // Group categories by parent/child relationship
    final parentCategories = filteredCategories
        .where((category) => category.parentCategoryId == null)
        .toList();

    final subcategoriesMap = <String, List<dynamic>>{};
    for (final category in filteredCategories) {
      if (category.parentCategoryId != null) {
        subcategoriesMap
            .putIfAbsent(category.parentCategoryId!, () => [])
            .add(category);
      }
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: parentCategories.length,
      itemBuilder: (context, index) {
        final category = parentCategories[index];
        final subcategories = subcategoriesMap[category.id] ?? [];

        return _buildCategoryCard(context, category, subcategories);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, CategoryType type) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            type == CategoryType.expense
                ? Icons.remove_circle_outline
                : Icons.add_circle_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No ${type.displayName.toLowerCase()} categories yet',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first ${type.displayName.toLowerCase()} category to get started',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToCreateCategory(context, type),
            icon: const Icon(Icons.add),
            label: Text('Add ${type.displayName} Category'),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(
    BuildContext context,
    dynamic category,
    List<dynamic> subcategories,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Color(category.colorValue).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  category.icon,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
            title: Text(
              category.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: category.description != null
                ? Text(category.description!)
                : null,
            trailing: PopupMenuButton<String>(
              onSelected: (value) =>
                  _handleCategoryAction(context, value, category),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('Edit'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'add_subcategory',
                  child: ListTile(
                    leading: Icon(Icons.add),
                    title: Text('Add Subcategory'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('Delete', style: TextStyle(color: Colors.red)),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),

          // Subcategories
          if (subcategories.isNotEmpty) ...[
            const Divider(height: 1),
            ...subcategories.map(
              (subcategory) => ListTile(
                leading: const SizedBox(width: 24),
                title: Row(
                  children: [
                    Text(
                      subcategory.icon,
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        subcategory.name,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
                trailing: PopupMenuButton<String>(
                  onSelected: (value) =>
                      _handleCategoryAction(context, value, subcategory),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit),
                        title: Text('Edit'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: ListTile(
                        leading: Icon(Icons.delete, color: Colors.red),
                        title: Text(
                          'Delete',
                          style: TextStyle(color: Colors.red),
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _handleCategoryAction(
    BuildContext context,
    String action,
    dynamic category,
  ) {
    switch (action) {
      case 'edit':
        _navigateToEditCategory(context, category);
        break;
      case 'add_subcategory':
        _navigateToCreateCategory(context, category.type, category.id);
        break;
      case 'delete':
        _showDeleteConfirmation(context, category);
        break;
    }
  }

  void _navigateToCreateCategory(
    BuildContext context, [
    CategoryType? type,
    String? parentCategoryId,
  ]) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateCategoryScreen(
          initialType: type,
          parentCategoryId: parentCategoryId,
        ),
      ),
    );
  }

  void _navigateToEditCategory(BuildContext context, dynamic category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateCategoryScreen(editingCategory: category),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, dynamic category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text(
          'Are you sure you want to delete "${category.name}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(categoriesProvider.notifier).deleteCategory(category.id);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${category.name} deleted')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
