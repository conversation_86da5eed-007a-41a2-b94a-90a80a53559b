// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'memo_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MemoModel _$MemoModelFromJson(Map<String, dynamic> json) => MemoModel(
  id: json['id'] as String,
  userId: json['userId'] as String,
  title: json['title'] as String,
  description: json['description'] as String?,
  richContent: json['richContent'] as String?,
  transcription: json['transcription'] as String?,
  audioFilePath: json['audioFilePath'] as String?,
  audioUrl: json['audioUrl'] as String?,
  duration: (json['duration'] as num?)?.toInt() ?? 0,
  type: $enumDecodeNullable(_$MemoTypeEnumMap, json['type']) ?? MemoType.text,
  todoItems:
      (json['todoItems'] as List<dynamic>?)
          ?.map((e) => TodoItemModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  attachments:
      (json['attachments'] as List<dynamic>?)
          ?.map((e) => FileAttachmentModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isSynced: json['isSynced'] as bool? ?? false,
  tags:
      (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  category: json['category'] as String?,
  isPinned: json['isPinned'] as bool? ?? false,
  isArchived: json['isArchived'] as bool? ?? false,
);

Map<String, dynamic> _$MemoModelToJson(MemoModel instance) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'title': instance.title,
  'description': instance.description,
  'richContent': instance.richContent,
  'transcription': instance.transcription,
  'audioFilePath': instance.audioFilePath,
  'audioUrl': instance.audioUrl,
  'duration': instance.duration,
  'type': _$MemoTypeEnumMap[instance.type]!,
  'todoItems': instance.todoItems,
  'attachments': instance.attachments,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'isSynced': instance.isSynced,
  'tags': instance.tags,
  'category': instance.category,
  'isPinned': instance.isPinned,
  'isArchived': instance.isArchived,
};

const _$MemoTypeEnumMap = {
  MemoType.text: 'text',
  MemoType.voice: 'voice',
  MemoType.todo: 'todo',
  MemoType.mixed: 'mixed',
};
