import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/category_model.dart';
import '../providers/money_flow_provider.dart';

class CreateCategoryScreen extends ConsumerStatefulWidget {
  final CategoryModel? editingCategory;
  final CategoryType? initialType;
  final String? parentCategoryId;

  const CreateCategoryScreen({
    super.key,
    this.editingCategory,
    this.initialType,
    this.parentCategoryId,
  });

  @override
  ConsumerState<CreateCategoryScreen> createState() => _CreateCategoryScreenState();
}

class _CreateCategoryScreenState extends ConsumerState<CreateCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  CategoryType _selectedType = CategoryType.expense;
  String _selectedIcon = '📦';
  Color _selectedColor = Colors.blue;

  final List<String> _expenseIcons = [
    '🍽️', '🚗', '🛍️', '🎬', '💡', '🏥', '📚', '🏠', '👕', '⛽',
    '📱', '💊', '🎮', '🍕', '☕', '🚌', '🎵', '💄', '🧽', '📦',
  ];

  final List<String> _incomeIcons = [
    '💼', '💻', '📈', '🏢', '💰', '🎯', '🏆', '💎', '🎁', '📊',
    '💳', '🏦', '📋', '💡', '🔧', '🎨', '📸', '🎤', '✍️', '🌟',
  ];

  final List<Color> _availableColors = [
    Colors.blue, Colors.green, Colors.red, Colors.purple, Colors.orange,
    Colors.teal, Colors.pink, Colors.indigo, Colors.brown, Colors.cyan,
    Colors.amber, Colors.deepOrange, Colors.lightGreen, Colors.deepPurple,
    Colors.blueGrey,
  ];

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.editingCategory != null) {
      final category = widget.editingCategory!;
      _nameController.text = category.name;
      _descriptionController.text = category.description ?? '';
      _selectedType = category.type;
      _selectedIcon = category.icon;
      _selectedColor = Color(category.colorValue);
    } else if (widget.initialType != null) {
      _selectedType = widget.initialType!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _saveCategory() {
    if (_formKey.currentState!.validate()) {
      final category = CategoryModel(
        id: widget.editingCategory?.id ?? 
            DateTime.now().millisecondsSinceEpoch.toString(),
        userId: 'demo-user-id',
        name: _nameController.text.trim(),
        type: _selectedType,
        icon: _selectedIcon,
        colorValue: _selectedColor.value,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        parentCategoryId: widget.parentCategoryId,
        createdAt: widget.editingCategory?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.editingCategory != null) {
        ref.read(categoriesProvider.notifier).updateCategory(category);
      } else {
        ref.read(categoriesProvider.notifier).addCategory(category);
      }

      Navigator.of(context).pop();
    }
  }

  Widget _buildTypeSelector() {
    if (widget.parentCategoryId != null) {
      // For subcategories, type is inherited from parent
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Category Type',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ChoiceChip(
                    label: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.remove_circle_outline, size: 16),
                        SizedBox(width: 4),
                        Text('Expense'),
                      ],
                    ),
                    selected: _selectedType == CategoryType.expense,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedType = CategoryType.expense;
                          _selectedIcon = _expenseIcons.first;
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ChoiceChip(
                    label: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.add_circle_outline, size: 16),
                        SizedBox(width: 4),
                        Text('Income'),
                      ],
                    ),
                    selected: _selectedType == CategoryType.income,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedType = CategoryType.income;
                          _selectedIcon = _incomeIcons.first;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconSelector() {
    final icons = _selectedType == CategoryType.expense 
        ? _expenseIcons 
        : _incomeIcons;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Category Icon',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: icons.map((icon) {
                final isSelected = _selectedIcon == icon;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIcon = icon;
                    });
                  },
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? _selectedColor.withValues(alpha: 0.2)
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: isSelected
                          ? Border.all(color: _selectedColor, width: 2)
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        icon,
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Category Color',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _availableColors.map((color) {
                final isSelected = _selectedColor == color;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedColor = color;
                    });
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: isSelected
                          ? Border.all(color: Colors.black, width: 3)
                          : null,
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: Colors.white)
                        : null,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isSubcategory = widget.parentCategoryId != null;
    final title = widget.editingCategory != null
        ? 'Edit Category'
        : isSubcategory
            ? 'Add Subcategory'
            : 'Add Category';

    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveCategory,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Category type selector (only for main categories)
              _buildTypeSelector(),
              if (!isSubcategory) const SizedBox(height: 16),

              // Category name
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: '${isSubcategory ? 'Subcategory' : 'Category'} Name *',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.label),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter category name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Icon selector
              _buildIconSelector(),
              const SizedBox(height: 16),

              // Color selector
              _buildColorSelector(),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
