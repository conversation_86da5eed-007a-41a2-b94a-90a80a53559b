import 'dart:async';
import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';

class SupabaseService {
  static SupabaseService? _instance;
  late final SupabaseClient _client;

  SupabaseService._internal();

  factory SupabaseService() {
    _instance ??= SupabaseService._internal();
    return _instance!;
  }

  Future<void> initialize() async {
    await Supabase.initialize(
      url: AppConstants.supabaseUrl,
      anonKey: AppConstants.supabaseAnonKey,
    );
    _client = Supabase.instance.client;
  }

  SupabaseClient get client => _client;

  // Authentication methods
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    final response = await _client.auth.signUp(
      email: email,
      password: password,
      data: displayName != null ? {'display_name': displayName} : null,
    );
    return response;
  }

  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    final response = await _client.auth.signInWithPassword(
      email: email,
      password: password,
    );
    return response;
  }

  Future<void> signOut() async {
    await _client.auth.signOut();
  }

  Future<void> resetPassword(String email) async {
    await _client.auth.resetPasswordForEmail(email);
  }

  User? get currentUser => _client.auth.currentUser;

  bool get isAuthenticated => currentUser != null;

  Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;

  // User profile methods
  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final response = await _client
          .from('users')
          .select()
          .eq('id', userId)
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  Future<void> updateUserProfile(UserModel user) async {
    await _client.from('users').upsert(user.toJson()).eq('id', user.id);
  }

  // File upload methods
  Future<String> uploadFile({
    required String bucket,
    required String path,
    required File file,
  }) async {
    await _client.storage.from(bucket).upload(path, file);
    return _client.storage.from(bucket).getPublicUrl(path);
  }

  Future<void> deleteFile({
    required String bucket,
    required String path,
  }) async {
    await _client.storage.from(bucket).remove([path]);
  }

  // Real-time subscriptions
  RealtimeChannel subscribeToTable({
    required String table,
    String? filter,
    required void Function(PostgresChangePayload) onInsert,
    required void Function(PostgresChangePayload) onUpdate,
    required void Function(PostgresChangePayload) onDelete,
  }) {
    final channel = _client.channel('public:$table');

    if (filter != null) {
      channel.onPostgresChanges(
        event: PostgresChangeEvent.insert,
        schema: 'public',
        table: table,
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'user_id',
          value: filter,
        ),
        callback: onInsert,
      );

      channel.onPostgresChanges(
        event: PostgresChangeEvent.update,
        schema: 'public',
        table: table,
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'user_id',
          value: filter,
        ),
        callback: onUpdate,
      );

      channel.onPostgresChanges(
        event: PostgresChangeEvent.delete,
        schema: 'public',
        table: table,
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'user_id',
          value: filter,
        ),
        callback: onDelete,
      );
    } else {
      channel.onPostgresChanges(
        event: PostgresChangeEvent.insert,
        schema: 'public',
        table: table,
        callback: onInsert,
      );

      channel.onPostgresChanges(
        event: PostgresChangeEvent.update,
        schema: 'public',
        table: table,
        callback: onUpdate,
      );

      channel.onPostgresChanges(
        event: PostgresChangeEvent.delete,
        schema: 'public',
        table: table,
        callback: onDelete,
      );
    }

    channel.subscribe();
    return channel;
  }

  // Generic CRUD operations
  Future<List<Map<String, dynamic>>> select({
    required String table,
    String columns = '*',
    String? where,
    String? orderBy,
    int? limit,
  }) async {
    dynamic query = _client.from(table).select(columns);

    if (where != null) {
      // Parse simple where conditions
      final parts = where.split('=');
      if (parts.length == 2) {
        query = query.eq(parts[0].trim(), parts[1].trim());
      }
    }

    if (orderBy != null) {
      query = query.order(orderBy);
    }

    if (limit != null) {
      query = query.limit(limit);
    }

    return await query;
  }

  Future<void> insert({
    required String table,
    required Map<String, dynamic> data,
  }) async {
    await _client.from(table).insert(data);
  }

  Future<void> update({
    required String table,
    required Map<String, dynamic> data,
    required String id,
  }) async {
    await _client.from(table).update(data).eq('id', id);
  }

  Future<void> delete({required String table, required String id}) async {
    await _client.from(table).delete().eq('id', id);
  }

  // Batch operations for sync
  Future<void> batchInsert({
    required String table,
    required List<Map<String, dynamic>> data,
  }) async {
    if (data.isNotEmpty) {
      await _client.from(table).insert(data);
    }
  }

  Future<void> batchUpdate({
    required String table,
    required List<Map<String, dynamic>> data,
  }) async {
    for (final item in data) {
      if (item.containsKey('id')) {
        final id = item['id'];
        final updateData = Map<String, dynamic>.from(item)..remove('id');
        await _client.from(table).update(updateData).eq('id', id);
      }
    }
  }
}
