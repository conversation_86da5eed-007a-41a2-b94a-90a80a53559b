enum CategoryType {
  income,
  expense,
}

extension CategoryTypeExtension on CategoryType {
  String get displayName {
    switch (this) {
      case CategoryType.income:
        return 'Income';
      case CategoryType.expense:
        return 'Expense';
    }
  }
}

class CategoryModel {
  final String id;
  final String userId;
  final String name;
  final CategoryType type;
  final String icon;
  final int colorValue;
  final String? description;
  final String? parentCategoryId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const CategoryModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.type,
    required this.icon,
    required this.colorValue,
    this.description,
    this.parentCategoryId,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  CategoryModel copyWith({
    String? id,
    String? userId,
    String? name,
    CategoryType? type,
    String? icon,
    int? colorValue,
    String? description,
    String? parentCategoryId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      type: type ?? this.type,
      icon: icon ?? this.icon,
      colorValue: colorValue ?? this.colorValue,
      description: description ?? this.description,
      parentCategoryId: parentCategoryId ?? this.parentCategoryId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'type': type.name,
      'icon': icon,
      'color_value': colorValue,
      'description': description,
      'parent_category_id': parentCategoryId,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_synced': isSynced,
    };
  }

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'],
      userId: json['user_id'],
      name: json['name'],
      type: CategoryType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CategoryType.expense,
      ),
      icon: json['icon'],
      colorValue: json['color_value'],
      description: json['description'],
      parentCategoryId: json['parent_category_id'],
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      isSynced: json['is_synced'] ?? false,
    );
  }

  @override
  String toString() {
    return 'CategoryModel(id: $id, name: $name, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel &&
        other.id == id &&
        other.name == name &&
        other.type == type;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ type.hashCode;
  }
}

// Default categories
class DefaultCategories {
  static List<CategoryModel> getDefaultExpenseCategories(String userId) {
    final now = DateTime.now();
    
    return [
      CategoryModel(
        id: 'exp_food',
        userId: userId,
        name: 'Food & Dining',
        type: CategoryType.expense,
        icon: '🍽️',
        colorValue: 0xFFFF5722,
        description: 'Restaurants, groceries, and food expenses',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'exp_transport',
        userId: userId,
        name: 'Transportation',
        type: CategoryType.expense,
        icon: '🚗',
        colorValue: 0xFF2196F3,
        description: 'Gas, public transport, car maintenance',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'exp_shopping',
        userId: userId,
        name: 'Shopping',
        type: CategoryType.expense,
        icon: '🛍️',
        colorValue: 0xFFE91E63,
        description: 'Clothing, electronics, general shopping',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'exp_entertainment',
        userId: userId,
        name: 'Entertainment',
        type: CategoryType.expense,
        icon: '🎬',
        colorValue: 0xFF9C27B0,
        description: 'Movies, games, subscriptions',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'exp_bills',
        userId: userId,
        name: 'Bills & Utilities',
        type: CategoryType.expense,
        icon: '💡',
        colorValue: 0xFFFFC107,
        description: 'Electricity, water, internet, phone',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'exp_healthcare',
        userId: userId,
        name: 'Healthcare',
        type: CategoryType.expense,
        icon: '🏥',
        colorValue: 0xFF4CAF50,
        description: 'Medical expenses, pharmacy, insurance',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'exp_education',
        userId: userId,
        name: 'Education',
        type: CategoryType.expense,
        icon: '📚',
        colorValue: 0xFF607D8B,
        description: 'Books, courses, tuition',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'exp_other',
        userId: userId,
        name: 'Other',
        type: CategoryType.expense,
        icon: '📦',
        colorValue: 0xFF795548,
        description: 'Miscellaneous expenses',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  static List<CategoryModel> getDefaultIncomeCategories(String userId) {
    final now = DateTime.now();
    
    return [
      CategoryModel(
        id: 'inc_salary',
        userId: userId,
        name: 'Salary',
        type: CategoryType.income,
        icon: '💼',
        colorValue: 0xFF4CAF50,
        description: 'Monthly salary and wages',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'inc_freelance',
        userId: userId,
        name: 'Freelance',
        type: CategoryType.income,
        icon: '💻',
        colorValue: 0xFF2196F3,
        description: 'Freelance work and projects',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'inc_investment',
        userId: userId,
        name: 'Investment',
        type: CategoryType.income,
        icon: '📈',
        colorValue: 0xFFFF9800,
        description: 'Dividends, interest, capital gains',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'inc_business',
        userId: userId,
        name: 'Business',
        type: CategoryType.income,
        icon: '🏢',
        colorValue: 0xFF9C27B0,
        description: 'Business income and profits',
        createdAt: now,
        updatedAt: now,
      ),
      CategoryModel(
        id: 'inc_other',
        userId: userId,
        name: 'Other Income',
        type: CategoryType.income,
        icon: '💰',
        colorValue: 0xFF607D8B,
        description: 'Miscellaneous income',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}
