import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/prayer_time_model.dart';
import '../providers/prayer_provider.dart';

class PrayerTimeSettingsScreen extends ConsumerStatefulWidget {
  const PrayerTimeSettingsScreen({super.key});

  @override
  ConsumerState<PrayerTimeSettingsScreen> createState() =>
      _PrayerTimeSettingsScreenState();
}

class _PrayerTimeSettingsScreenState
    extends ConsumerState<PrayerTimeSettingsScreen> {
  CalculationMethod _selectedMethod = CalculationMethod.muslimWorldLeague;
  LocationModel? _selectedLocation;
  bool _autoLocation = true;
  bool _notificationsEnabled = true;
  List<int> _reminderMinutes = [10, 5];
  Map<String, int> _prayerAdjustments = {
    'Fajr': 0,
    'Dhuhr': 0,
    'Asr': 0,
    'Maghrib': 0,
    'Isha': 0,
  };

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  void _loadCurrentSettings() {
    // Load current settings from provider
    // This would typically come from shared preferences or database
    setState(() {
      _selectedMethod = CalculationMethod.muslimWorldLeague;
      _autoLocation = true;
      _notificationsEnabled = true;
      _reminderMinutes = [10, 5];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Prayer Time Settings'),
        actions: [
          TextButton(onPressed: _saveSettings, child: const Text('Save')),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Location settings
            _buildLocationSection(),
            const SizedBox(height: 24),

            // Calculation method
            _buildCalculationMethodSection(),
            const SizedBox(height: 24),

            // Manual adjustments
            _buildManualAdjustmentsSection(),
            const SizedBox(height: 24),

            // Notification settings
            _buildNotificationSection(),
            const SizedBox(height: 24),

            // Qibla direction
            _buildQiblaSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Location',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Auto-detect location'),
              subtitle: const Text(
                'Use GPS to automatically detect your location',
              ),
              value: _autoLocation,
              onChanged: (value) {
                setState(() {
                  _autoLocation = value;
                });
              },
              contentPadding: EdgeInsets.zero,
            ),

            if (!_autoLocation) ...[
              const SizedBox(height: 16),
              ListTile(
                title: Text(_selectedLocation?.name ?? 'Select Location'),
                subtitle: _selectedLocation != null
                    ? Text(
                        '${_selectedLocation!.city}, ${_selectedLocation!.country}',
                      )
                    : const Text('Tap to choose your location'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: _selectLocation,
                contentPadding: EdgeInsets.zero,
              ),
            ],

            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _getCurrentLocation,
                    icon: const Icon(Icons.my_location),
                    label: const Text('Get Current Location'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _showQiblaDirection,
                    icon: const Icon(Icons.explore),
                    label: const Text('Qibla Direction'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationMethodSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.calculate, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Calculation Method',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            DropdownButtonFormField<CalculationMethod>(
              value: _selectedMethod,
              decoration: const InputDecoration(
                labelText: 'Method',
                border: OutlineInputBorder(),
                helperText:
                    'Different methods may result in slightly different prayer times',
              ),
              items: CalculationMethod.values.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Text(
                    method.displayName,
                    style: const TextStyle(fontSize: 14),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedMethod = value;
                  });
                }
              },
            ),

            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Method Parameters:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...(_selectedMethod.parameters.entries.map(
                    (entry) => Text(
                      '${entry.key}: ${entry.value}°',
                      style: TextStyle(color: Colors.blue[600]),
                    ),
                  )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManualAdjustmentsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.tune, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Manual Adjustments',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              'Adjust individual prayer times (in minutes)',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),

            ...['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'].map(
              (prayerName) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text(
                        prayerName,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Row(
                        children: [
                          IconButton(
                            onPressed: () => _adjustPrayerTime(prayerName, -1),
                            icon: const Icon(Icons.remove),
                            style: IconButton.styleFrom(
                              backgroundColor: Colors.grey[200],
                            ),
                          ),
                          const SizedBox(width: 12),
                          Container(
                            width: 60,
                            alignment: Alignment.center,
                            child: Text(
                              '${_getPrayerAdjustment(prayerName)}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          IconButton(
                            onPressed: () => _adjustPrayerTime(prayerName, 1),
                            icon: const Icon(Icons.add),
                            style: IconButton.styleFrom(
                              backgroundColor: Colors.grey[200],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'min',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.notifications, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Notifications',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Prayer time notifications'),
              subtitle: const Text('Receive notifications for prayer times'),
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
              contentPadding: EdgeInsets.zero,
            ),

            if (_notificationsEnabled) ...[
              const SizedBox(height: 16),
              Text(
                'Reminder times (minutes before prayer)',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: _reminderMinutes
                    .map(
                      (minutes) => Chip(
                        label: Text('$minutes min'),
                        onDeleted: () => _removeReminder(minutes),
                      ),
                    )
                    .toList(),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _addReminder,
                icon: const Icon(Icons.add),
                label: const Text('Add Reminder'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQiblaSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.explore, color: Colors.teal),
                const SizedBox(width: 8),
                Text(
                  'Qibla Direction',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Direction to Kaaba',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '45° Northeast', // This would be calculated
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              color: Colors.teal[700],
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _showQiblaCompass,
                  icon: const Icon(Icons.compass_calibration),
                  label: const Text('Compass'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _getCurrentLocation() async {
    // TODO: Implement GPS location detection
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Getting current location...')),
    );
  }

  void _selectLocation() {
    // TODO: Implement location selection dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Location'),
        content: const Text('Location selection coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showQiblaDirection() {
    // TODO: Implement Qibla direction screen
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const QiblaDirectionScreen()),
    );
  }

  void _showQiblaCompass() {
    // TODO: Implement Qibla compass
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Qibla Compass'),
        content: const Text('Qibla compass coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _addReminder() {
    showDialog(
      context: context,
      builder: (context) {
        int minutes = 15;
        return AlertDialog(
          title: const Text('Add Reminder'),
          content: StatefulBuilder(
            builder: (context, setState) => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Remind me $minutes minutes before prayer time'),
                Slider(
                  value: minutes.toDouble(),
                  min: 1,
                  max: 60,
                  divisions: 59,
                  onChanged: (value) {
                    setState(() {
                      minutes = value.round();
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (!_reminderMinutes.contains(minutes)) {
                  setState(() {
                    _reminderMinutes.add(minutes);
                    _reminderMinutes.sort();
                  });
                }
                Navigator.of(context).pop();
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _removeReminder(int minutes) {
    setState(() {
      _reminderMinutes.remove(minutes);
    });
  }

  void _adjustPrayerTime(String prayerName, int minutes) {
    setState(() {
      _prayerAdjustments[prayerName] =
          (_prayerAdjustments[prayerName]! + minutes).clamp(-30, 30);
    });
  }

  int _getPrayerAdjustment(String prayerName) {
    return _prayerAdjustments[prayerName] ?? 0;
  }

  void _saveSettings() {
    // TODO: Save settings to provider/database
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Settings saved!')));
    Navigator.of(context).pop();
  }
}

// Placeholder for Qibla Direction Screen
class QiblaDirectionScreen extends StatelessWidget {
  const QiblaDirectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Qibla Direction')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.explore, size: 64, color: Colors.teal),
            SizedBox(height: 16),
            Text(
              'Qibla Direction Finder',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Compass and direction finder coming soon!'),
          ],
        ),
      ),
    );
  }
}
