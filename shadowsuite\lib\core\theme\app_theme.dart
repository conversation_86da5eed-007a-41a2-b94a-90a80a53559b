import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppTheme {
  // Predefined color schemes
  static const List<ColorScheme> colorSchemes = [
    // Deep Purple (Default)
    ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFF6750A4),
      onPrimary: Color(0xFFFFFFFF),
      primaryContainer: Color(0xFFEADDFF),
      onPrimaryContainer: Color(0xFF21005D),
      secondary: Color(0xFF625B71),
      onSecondary: Color(0xFFFFFFFF),
      secondaryContainer: Color(0xFFE8DEF8),
      onSecondaryContainer: Color(0xFF1D192B),
      tertiary: Color(0xFF7D5260),
      onTertiary: Color(0xFFFFFFFF),
      tertiaryContainer: Color(0xFFFFD8E4),
      onTertiaryContainer: Color(0xFF31111D),
      error: Color(0xFFBA1A1A),
      onError: Color(0xFFFFFFFF),
      errorContainer: Color(0xFFFFDAD6),
      onErrorContainer: Color(0xFF410002),
      background: Color(0xFFFFFBFE),
      onBackground: Color(0xFF1C1B1F),
      surface: Color(0xFFFFFBFE),
      onSurface: Color(0xFF1C1B1F),
      surfaceVariant: Color(0xFFE7E0EC),
      onSurfaceVariant: Color(0xFF49454F),
      outline: Color(0xFF79747E),
      outlineVariant: Color(0xFFCAC4D0),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFF313033),
      onInverseSurface: Color(0xFFF4EFF4),
      inversePrimary: Color(0xFFD0BCFF),
      surfaceTint: Color(0xFF6750A4),
    ),

    // Ocean Blue
    ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFF0077BE),
      onPrimary: Color(0xFFFFFFFF),
      primaryContainer: Color(0xFFD0E7FF),
      onPrimaryContainer: Color(0xFF001D35),
      secondary: Color(0xFF526070),
      onSecondary: Color(0xFFFFFFFF),
      secondaryContainer: Color(0xFFD5E4F7),
      onSecondaryContainer: Color(0xFF0E1D2A),
      tertiary: Color(0xFF68587A),
      onTertiary: Color(0xFFFFFFFF),
      tertiaryContainer: Color(0xFFEFDBFF),
      onTertiaryContainer: Color(0xFF231533),
      error: Color(0xFFBA1A1A),
      onError: Color(0xFFFFFFFF),
      errorContainer: Color(0xFFFFDAD6),
      onErrorContainer: Color(0xFF410002),
      background: Color(0xFFFCFCFF),
      onBackground: Color(0xFF1A1C1E),
      surface: Color(0xFFFCFCFF),
      onSurface: Color(0xFF1A1C1E),
      surfaceVariant: Color(0xFFDFE2EB),
      onSurfaceVariant: Color(0xFF43474E),
      outline: Color(0xFF73777F),
      outlineVariant: Color(0xFFC3C6CF),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFF2F3033),
      onInverseSurface: Color(0xFFF1F0F4),
      inversePrimary: Color(0xFF9CCEFF),
      surfaceTint: Color(0xFF0077BE),
    ),

    // Forest Green
    ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFF006A60),
      onPrimary: Color(0xFFFFFFFF),
      primaryContainer: Color(0xFF9CF2E4),
      onPrimaryContainer: Color(0xFF00201C),
      secondary: Color(0xFF4A635F),
      onSecondary: Color(0xFFFFFFFF),
      secondaryContainer: Color(0xFFCCE8E2),
      onSecondaryContainer: Color(0xFF05201C),
      tertiary: Color(0xFF456179),
      onTertiary: Color(0xFFFFFFFF),
      tertiaryContainer: Color(0xFFCCE5FF),
      onTertiaryContainer: Color(0xFF001E30),
      error: Color(0xFFBA1A1A),
      onError: Color(0xFFFFFFFF),
      errorContainer: Color(0xFFFFDAD6),
      onErrorContainer: Color(0xFF410002),
      background: Color(0xFFFAFDFA),
      onBackground: Color(0xFF191C1B),
      surface: Color(0xFFFAFDFA),
      onSurface: Color(0xFF191C1B),
      surfaceVariant: Color(0xFFDAE5E1),
      onSurfaceVariant: Color(0xFF3F4946),
      outline: Color(0xFF6F7976),
      outlineVariant: Color(0xFFBEC9C5),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFF2E3130),
      onInverseSurface: Color(0xFFEFF1EF),
      inversePrimary: Color(0xFF80D5C8),
      surfaceTint: Color(0xFF006A60),
    ),

    // Sunset Orange
    ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFFBF360C),
      onPrimary: Color(0xFFFFFFFF),
      primaryContainer: Color(0xFFFFDBCF),
      onPrimaryContainer: Color(0xFF3E0A00),
      secondary: Color(0xFF77574A),
      onSecondary: Color(0xFFFFFFFF),
      secondaryContainer: Color(0xFFFFDBCF),
      onSecondaryContainer: Color(0xFF2C160C),
      tertiary: Color(0xFF6B5E2F),
      onTertiary: Color(0xFFFFFFFF),
      tertiaryContainer: Color(0xFFF4E2A7),
      onTertiaryContainer: Color(0xFF241A00),
      error: Color(0xFFBA1A1A),
      onError: Color(0xFFFFFFFF),
      errorContainer: Color(0xFFFFDAD6),
      onErrorContainer: Color(0xFF410002),
      background: Color(0xFFFFFBFF),
      onBackground: Color(0xFF201A18),
      surface: Color(0xFFFFFBFF),
      onSurface: Color(0xFF201A18),
      surfaceVariant: Color(0xFFF5DDD6),
      onSurfaceVariant: Color(0xFF53433E),
      outline: Color(0xFF85736D),
      outlineVariant: Color(0xFFD8C2BA),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFF362F2C),
      onInverseSurface: Color(0xFFFBEEE9),
      inversePrimary: Color(0xFFFFB59A),
      surfaceTint: Color(0xFFBF360C),
    ),

    // Rose Pink
    ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFFB91372),
      onPrimary: Color(0xFFFFFFFF),
      primaryContainer: Color(0xFFFFD9E2),
      onPrimaryContainer: Color(0xFF3E0020),
      secondary: Color(0xFF74565F),
      onSecondary: Color(0xFFFFFFFF),
      secondaryContainer: Color(0xFFFFD9E2),
      onSecondaryContainer: Color(0xFF2B151C),
      tertiary: Color(0xFF7C5635),
      onTertiary: Color(0xFFFFFFFF),
      tertiaryContainer: Color(0xFFFFDCC1),
      onTertiaryContainer: Color(0xFF2E1500),
      error: Color(0xFFBA1A1A),
      onError: Color(0xFFFFFFFF),
      errorContainer: Color(0xFFFFDAD6),
      onErrorContainer: Color(0xFF410002),
      background: Color(0xFFFFFBFF),
      onBackground: Color(0xFF201A1B),
      surface: Color(0xFFFFFBFF),
      onSurface: Color(0xFF201A1B),
      surfaceVariant: Color(0xFFF2DDE1),
      onSurfaceVariant: Color(0xFF514347),
      outline: Color(0xFF837377),
      outlineVariant: Color(0xFFD5C2C6),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFF352F30),
      onInverseSurface: Color(0xFFFAEEEF),
      inversePrimary: Color(0xFFFFB0C8),
      surfaceTint: Color(0xFFB91372),
    ),
  ];

  // Dark color schemes
  static const List<ColorScheme> darkColorSchemes = [
    // Deep Purple Dark
    ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xFFD0BCFF),
      onPrimary: Color(0xFF381E72),
      primaryContainer: Color(0xFF4F378B),
      onPrimaryContainer: Color(0xFFEADDFF),
      secondary: Color(0xFFCCC2DC),
      onSecondary: Color(0xFF332D41),
      secondaryContainer: Color(0xFF4A4458),
      onSecondaryContainer: Color(0xFFE8DEF8),
      tertiary: Color(0xFFEFB8C8),
      onTertiary: Color(0xFF492532),
      tertiaryContainer: Color(0xFF633B48),
      onTertiaryContainer: Color(0xFFFFD8E4),
      error: Color(0xFFFFB4AB),
      onError: Color(0xFF690005),
      errorContainer: Color(0xFF93000A),
      onErrorContainer: Color(0xFFFFDAD6),
      background: Color(0xFF10131C),
      onBackground: Color(0xFFE6E1E5),
      surface: Color(0xFF10131C),
      onSurface: Color(0xFFE6E1E5),
      surfaceVariant: Color(0xFF49454F),
      onSurfaceVariant: Color(0xFFCAC4D0),
      outline: Color(0xFF938F99),
      outlineVariant: Color(0xFF49454F),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFFE6E1E5),
      onInverseSurface: Color(0xFF313033),
      inversePrimary: Color(0xFF6750A4),
      surfaceTint: Color(0xFFD0BCFF),
    ),

    // Ocean Blue Dark
    ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xFF9CCEFF),
      onPrimary: Color(0xFF003355),
      primaryContainer: Color(0xFF004A77),
      onPrimaryContainer: Color(0xFFD0E7FF),
      secondary: Color(0xFFB9C8DA),
      onSecondary: Color(0xFF243240),
      secondaryContainer: Color(0xFF3A4857),
      onSecondaryContainer: Color(0xFFD5E4F7),
      tertiary: Color(0xFFD3BFE6),
      onTertiary: Color(0xFF392A49),
      tertiaryContainer: Color(0xFF504060),
      onTertiaryContainer: Color(0xFFEFDBFF),
      error: Color(0xFFFFB4AB),
      onError: Color(0xFF690005),
      errorContainer: Color(0xFF93000A),
      onErrorContainer: Color(0xFFFFDAD6),
      background: Color(0xFF101418),
      onBackground: Color(0xFFE1E2E8),
      surface: Color(0xFF101418),
      onSurface: Color(0xFFE1E2E8),
      surfaceVariant: Color(0xFF43474E),
      onSurfaceVariant: Color(0xFFC3C6CF),
      outline: Color(0xFF8D9199),
      outlineVariant: Color(0xFF43474E),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFFE1E2E8),
      onInverseSurface: Color(0xFF2F3033),
      inversePrimary: Color(0xFF0077BE),
      surfaceTint: Color(0xFF9CCEFF),
    ),

    // Forest Green Dark
    ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xFF80D5C8),
      onPrimary: Color(0xFF003732),
      primaryContainer: Color(0xFF005049),
      onPrimaryContainer: Color(0xFF9CF2E4),
      secondary: Color(0xFFB0CCC6),
      onSecondary: Color(0xFF1B3531),
      secondaryContainer: Color(0xFF324B47),
      onSecondaryContainer: Color(0xFFCCE8E2),
      tertiary: Color(0xFFB0C9E8),
      onTertiary: Color(0xFF173347),
      tertiaryContainer: Color(0xFF2E4A60),
      onTertiaryContainer: Color(0xFFCCE5FF),
      error: Color(0xFFFFB4AB),
      onError: Color(0xFF690005),
      errorContainer: Color(0xFF93000A),
      onErrorContainer: Color(0xFFFFDAD6),
      background: Color(0xFF0F1513),
      onBackground: Color(0xFFE0E3E0),
      surface: Color(0xFF0F1513),
      onSurface: Color(0xFFE0E3E0),
      surfaceVariant: Color(0xFF3F4946),
      onSurfaceVariant: Color(0xFFBEC9C5),
      outline: Color(0xFF889390),
      outlineVariant: Color(0xFF3F4946),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFFE0E3E0),
      onInverseSurface: Color(0xFF2E3130),
      inversePrimary: Color(0xFF006A60),
      surfaceTint: Color(0xFF80D5C8),
    ),

    // Sunset Orange Dark
    ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xFFFFB59A),
      onPrimary: Color(0xFF5F1600),
      primaryContainer: Color(0xFF8B2500),
      onPrimaryContainer: Color(0xFFFFDBCF),
      secondary: Color(0xFFE7BDB0),
      onSecondary: Color(0xFF442B20),
      secondaryContainer: Color(0xFF5D4135),
      onSecondaryContainer: Color(0xFFFFDBCF),
      tertiary: Color(0xFFD7C68D),
      onTertiary: Color(0xFF3B2F05),
      tertiaryContainer: Color(0xFF52461A),
      onTertiaryContainer: Color(0xFFF4E2A7),
      error: Color(0xFFFFB4AB),
      onError: Color(0xFF690005),
      errorContainer: Color(0xFF93000A),
      onErrorContainer: Color(0xFFFFDAD6),
      background: Color(0xFF17120F),
      onBackground: Color(0xFFF0E6E1),
      surface: Color(0xFF17120F),
      onSurface: Color(0xFFF0E6E1),
      surfaceVariant: Color(0xFF53433E),
      onSurfaceVariant: Color(0xFFD8C2BA),
      outline: Color(0xFFA08D87),
      outlineVariant: Color(0xFF53433E),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFFF0E6E1),
      onInverseSurface: Color(0xFF362F2C),
      inversePrimary: Color(0xFFBF360C),
      surfaceTint: Color(0xFFFFB59A),
    ),

    // Rose Pink Dark
    ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xFFFFB0C8),
      onPrimary: Color(0xFF5E0037),
      primaryContainer: Color(0xFF83004F),
      onPrimaryContainer: Color(0xFFFFD9E2),
      secondary: Color(0xFFE2BDC6),
      onSecondary: Color(0xFF422931),
      secondaryContainer: Color(0xFF5A3F47),
      onSecondaryContainer: Color(0xFFFFD9E2),
      tertiary: Color(0xFFE8C08A),
      onTertiary: Color(0xFF452B08),
      tertiaryContainer: Color(0xFF5E4120),
      onTertiaryContainer: Color(0xFFFFDCC1),
      error: Color(0xFFFFB4AB),
      onError: Color(0xFF690005),
      errorContainer: Color(0xFF93000A),
      onErrorContainer: Color(0xFFFFDAD6),
      background: Color(0xFF17111213),
      onBackground: Color(0xFFECE0E1),
      surface: Color(0xFF17111213),
      onSurface: Color(0xFFECE0E1),
      surfaceVariant: Color(0xFF514347),
      onSurfaceVariant: Color(0xFFD5C2C6),
      outline: Color(0xFF9E8C90),
      outlineVariant: Color(0xFF514347),
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFFECE0E1),
      onInverseSurface: Color(0xFF352F30),
      inversePrimary: Color(0xFFB91372),
      surfaceTint: Color(0xFFFFB0C8),
    ),
  ];

  // Theme names for display
  static const List<String> themeNames = [
    'Deep Purple',
    'Ocean Blue',
    'Forest Green',
    'Sunset Orange',
    'Rose Pink',
  ];

  // Get theme data for a specific color scheme
  static ThemeData getTheme(int index, {bool isDark = false}) {
    final colorScheme = isDark ? darkColorSchemes[index] : colorSchemes[index];

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,

      // App Bar Theme
      appBarTheme: AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 1,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
          systemNavigationBarColor: colorScheme.surface,
          systemNavigationBarIconBrightness: isDark
              ? Brightness.light
              : Brightness.dark,
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        color: colorScheme.surface,
        surfaceTintColor: colorScheme.surfaceTint,
      ),

      // Navigation Rail Theme
      navigationRailTheme: NavigationRailThemeData(
        selectedIconTheme: IconThemeData(size: 28, color: colorScheme.primary),
        unselectedIconTheme: IconThemeData(
          size: 24,
          color: colorScheme.onSurfaceVariant,
        ),
        selectedLabelTextStyle: TextStyle(
          color: colorScheme.primary,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelTextStyle: TextStyle(
          color: colorScheme.onSurfaceVariant,
        ),
        backgroundColor: colorScheme.surface,
        labelType: NavigationRailLabelType.all,
        useIndicator: true,
        indicatorColor: colorScheme.primaryContainer,
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primaryContainer,
        foregroundColor: colorScheme.onPrimaryContainer,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceVariant.withOpacity(0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.error),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceVariant,
        selectedColor: colorScheme.primaryContainer,
        labelStyle: TextStyle(color: colorScheme.onSurfaceVariant),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),

      // Dialog Theme
      dialogTheme: DialogThemeData(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 8,
      ),

      // Bottom Sheet Theme
      bottomSheetTheme: BottomSheetThemeData(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        backgroundColor: colorScheme.surface,
      ),

      // Snack Bar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: TextStyle(color: colorScheme.onInverseSurface),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Get current theme index from preferences
  static int getCurrentThemeIndex() {
    // This would typically come from SharedPreferences
    return 0; // Default to Deep Purple
  }

  // Save theme preference
  static Future<void> saveThemePreference(int index, bool isDark) async {
    // This would typically save to SharedPreferences
    // For now, we'll just print the selection
    print('Theme saved: ${themeNames[index]} (${isDark ? 'Dark' : 'Light'})');
  }
}
