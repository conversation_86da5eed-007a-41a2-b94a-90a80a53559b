import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/database/database_helper.dart';
import '../models/account_model.dart';
import '../models/category_model.dart';
import '../models/transaction_model.dart';
import '../models/budget_model.dart';
import '../models/recurring_transaction_model.dart';

class MoneyFlowSyncService {
  static MoneyFlowSyncService? _instance;
  final SupabaseService _supabaseService = SupabaseService();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  List<RealtimeChannel> _subscriptions = [];
  Timer? _syncTimer;
  bool _isOnline = true;

  MoneyFlowSyncService._internal();

  factory MoneyFlowSyncService() {
    _instance ??= MoneyFlowSyncService._internal();
    return _instance!;
  }

  // Initialize sync service
  Future<void> initialize() async {
    await _setupRealtimeSubscriptions();
    _startPeriodicSync();
  }

  // Setup real-time subscriptions for all Money Flow tables
  Future<void> _setupRealtimeSubscriptions() async {
    final userId = _supabaseService.currentUser?.id;
    if (userId == null) return;

    // Subscribe to accounts changes
    final accountsChannel = _supabaseService.subscribeToTable(
      table: 'accounts',
      filter: userId,
      onInsert: (payload) => _handleAccountInsert(payload),
      onUpdate: (payload) => _handleAccountUpdate(payload),
      onDelete: (payload) => _handleAccountDelete(payload),
    );

    // Subscribe to categories changes
    final categoriesChannel = _supabaseService.subscribeToTable(
      table: 'categories',
      filter: userId,
      onInsert: (payload) => _handleCategoryInsert(payload),
      onUpdate: (payload) => _handleCategoryUpdate(payload),
      onDelete: (payload) => _handleCategoryDelete(payload),
    );

    // Subscribe to transactions changes
    final transactionsChannel = _supabaseService.subscribeToTable(
      table: 'transactions',
      filter: userId,
      onInsert: (payload) => _handleTransactionInsert(payload),
      onUpdate: (payload) => _handleTransactionUpdate(payload),
      onDelete: (payload) => _handleTransactionDelete(payload),
    );

    // Subscribe to budgets changes
    final budgetsChannel = _supabaseService.subscribeToTable(
      table: 'budgets',
      filter: userId,
      onInsert: (payload) => _handleBudgetInsert(payload),
      onUpdate: (payload) => _handleBudgetUpdate(payload),
      onDelete: (payload) => _handleBudgetDelete(payload),
    );

    // Subscribe to recurring transactions changes
    final recurringChannel = _supabaseService.subscribeToTable(
      table: 'recurring_transactions',
      filter: userId,
      onInsert: (payload) => _handleRecurringInsert(payload),
      onUpdate: (payload) => _handleRecurringUpdate(payload),
      onDelete: (payload) => _handleRecurringDelete(payload),
    );

    _subscriptions = [
      accountsChannel,
      categoriesChannel,
      transactionsChannel,
      budgetsChannel,
      recurringChannel,
    ];
  }

  // Start periodic sync every 5 minutes
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      if (_isOnline) {
        syncAllData();
      }
    });
  }

  // Sync all Money Flow data
  Future<void> syncAllData() async {
    try {
      await Future.wait([
        syncAccounts(),
        syncCategories(),
        syncTransactions(),
        syncBudgets(),
        syncRecurringTransactions(),
      ]);
    } catch (e) {
      print('Sync error: $e');
    }
  }

  // Sync accounts
  Future<void> syncAccounts() async {
    final userId = _supabaseService.currentUser?.id;
    if (userId == null) return;

    try {
      // Get unsynced local accounts
      final localAccounts = await _databaseHelper.getUnsyncedAccounts();
      
      // Upload unsynced accounts to Supabase
      for (final account in localAccounts) {
        final accountData = account.toJson();
        accountData['user_id'] = userId;
        
        await _supabaseService.insert(
          table: 'accounts',
          data: accountData,
        );
        
        // Mark as synced
        await _databaseHelper.markAccountAsSynced(account.id);
      }

      // Download latest accounts from Supabase
      final remoteAccounts = await _supabaseService.select(
        table: 'accounts',
        where: 'user_id=$userId',
      );

      // Update local database
      for (final accountData in remoteAccounts) {
        final account = AccountModel.fromJson(accountData);
        await _databaseHelper.insertOrUpdateAccount(account);
      }
    } catch (e) {
      print('Account sync error: $e');
    }
  }

  // Sync categories
  Future<void> syncCategories() async {
    final userId = _supabaseService.currentUser?.id;
    if (userId == null) return;

    try {
      // Get unsynced local categories
      final localCategories = await _databaseHelper.getUnsyncedCategories();
      
      // Upload unsynced categories to Supabase
      for (final category in localCategories) {
        final categoryData = category.toJson();
        categoryData['user_id'] = userId;
        
        await _supabaseService.insert(
          table: 'categories',
          data: categoryData,
        );
        
        // Mark as synced
        await _databaseHelper.markCategoryAsSynced(category.id);
      }

      // Download latest categories from Supabase
      final remoteCategories = await _supabaseService.select(
        table: 'categories',
        where: 'user_id=$userId',
      );

      // Update local database
      for (final categoryData in remoteCategories) {
        final category = CategoryModel.fromJson(categoryData);
        await _databaseHelper.insertOrUpdateCategory(category);
      }
    } catch (e) {
      print('Category sync error: $e');
    }
  }

  // Sync transactions
  Future<void> syncTransactions() async {
    final userId = _supabaseService.currentUser?.id;
    if (userId == null) return;

    try {
      // Get unsynced local transactions
      final localTransactions = await _databaseHelper.getUnsyncedTransactions();
      
      // Upload unsynced transactions to Supabase
      for (final transaction in localTransactions) {
        final transactionData = transaction.toJson();
        transactionData['user_id'] = userId;
        
        await _supabaseService.insert(
          table: 'transactions',
          data: transactionData,
        );
        
        // Mark as synced
        await _databaseHelper.markTransactionAsSynced(transaction.id);
      }

      // Download latest transactions from Supabase
      final remoteTransactions = await _supabaseService.select(
        table: 'transactions',
        where: 'user_id=$userId',
        orderBy: 'date',
        limit: 1000, // Limit to recent transactions
      );

      // Update local database
      for (final transactionData in remoteTransactions) {
        final transaction = TransactionModel.fromJson(transactionData);
        await _databaseHelper.insertOrUpdateTransaction(transaction);
      }
    } catch (e) {
      print('Transaction sync error: $e');
    }
  }

  // Sync budgets
  Future<void> syncBudgets() async {
    final userId = _supabaseService.currentUser?.id;
    if (userId == null) return;

    try {
      // Get unsynced local budgets
      final localBudgets = await _databaseHelper.getUnsyncedBudgets();
      
      // Upload unsynced budgets to Supabase
      for (final budget in localBudgets) {
        final budgetData = budget.toJson();
        budgetData['user_id'] = userId;
        
        await _supabaseService.insert(
          table: 'budgets',
          data: budgetData,
        );
        
        // Mark as synced
        await _databaseHelper.markBudgetAsSynced(budget.id);
      }

      // Download latest budgets from Supabase
      final remoteBudgets = await _supabaseService.select(
        table: 'budgets',
        where: 'user_id=$userId',
      );

      // Update local database
      for (final budgetData in remoteBudgets) {
        final budget = BudgetModel.fromJson(budgetData);
        await _databaseHelper.insertOrUpdateBudget(budget);
      }
    } catch (e) {
      print('Budget sync error: $e');
    }
  }

  // Sync recurring transactions
  Future<void> syncRecurringTransactions() async {
    final userId = _supabaseService.currentUser?.id;
    if (userId == null) return;

    try {
      // Get unsynced local recurring transactions
      final localRecurring = await _databaseHelper.getUnsyncedRecurringTransactions();
      
      // Upload unsynced recurring transactions to Supabase
      for (final recurring in localRecurring) {
        final recurringData = recurring.toJson();
        recurringData['user_id'] = userId;
        
        await _supabaseService.insert(
          table: 'recurring_transactions',
          data: recurringData,
        );
        
        // Mark as synced
        await _databaseHelper.markRecurringTransactionAsSynced(recurring.id);
      }

      // Download latest recurring transactions from Supabase
      final remoteRecurring = await _supabaseService.select(
        table: 'recurring_transactions',
        where: 'user_id=$userId',
      );

      // Update local database
      for (final recurringData in remoteRecurring) {
        final recurring = RecurringTransactionModel.fromJson(recurringData);
        await _databaseHelper.insertOrUpdateRecurringTransaction(recurring);
      }
    } catch (e) {
      print('Recurring transaction sync error: $e');
    }
  }

  // Real-time event handlers
  void _handleAccountInsert(PostgresChangePayload payload) async {
    final account = AccountModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateAccount(account);
  }

  void _handleAccountUpdate(PostgresChangePayload payload) async {
    final account = AccountModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateAccount(account);
  }

  void _handleAccountDelete(PostgresChangePayload payload) async {
    final accountId = payload.oldRecord['id'] as String;
    await _databaseHelper.deleteAccount(accountId);
  }

  void _handleCategoryInsert(PostgresChangePayload payload) async {
    final category = CategoryModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateCategory(category);
  }

  void _handleCategoryUpdate(PostgresChangePayload payload) async {
    final category = CategoryModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateCategory(category);
  }

  void _handleCategoryDelete(PostgresChangePayload payload) async {
    final categoryId = payload.oldRecord['id'] as String;
    await _databaseHelper.deleteCategory(categoryId);
  }

  void _handleTransactionInsert(PostgresChangePayload payload) async {
    final transaction = TransactionModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateTransaction(transaction);
  }

  void _handleTransactionUpdate(PostgresChangePayload payload) async {
    final transaction = TransactionModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateTransaction(transaction);
  }

  void _handleTransactionDelete(PostgresChangePayload payload) async {
    final transactionId = payload.oldRecord['id'] as String;
    await _databaseHelper.deleteTransaction(transactionId);
  }

  void _handleBudgetInsert(PostgresChangePayload payload) async {
    final budget = BudgetModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateBudget(budget);
  }

  void _handleBudgetUpdate(PostgresChangePayload payload) async {
    final budget = BudgetModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateBudget(budget);
  }

  void _handleBudgetDelete(PostgresChangePayload payload) async {
    final budgetId = payload.oldRecord['id'] as String;
    await _databaseHelper.deleteBudget(budgetId);
  }

  void _handleRecurringInsert(PostgresChangePayload payload) async {
    final recurring = RecurringTransactionModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateRecurringTransaction(recurring);
  }

  void _handleRecurringUpdate(PostgresChangePayload payload) async {
    final recurring = RecurringTransactionModel.fromJson(payload.newRecord);
    await _databaseHelper.insertOrUpdateRecurringTransaction(recurring);
  }

  void _handleRecurringDelete(PostgresChangePayload payload) async {
    final recurringId = payload.oldRecord['id'] as String;
    await _databaseHelper.deleteRecurringTransaction(recurringId);
  }

  // Connection status management
  void setOnlineStatus(bool isOnline) {
    _isOnline = isOnline;
    if (isOnline) {
      syncAllData();
    }
  }

  // Cleanup
  void dispose() {
    _syncTimer?.cancel();
    for (final subscription in _subscriptions) {
      subscription.unsubscribe();
    }
    _subscriptions.clear();
  }
}

// Provider for the sync service
final moneyFlowSyncServiceProvider = Provider<MoneyFlowSyncService>((ref) {
  return MoneyFlowSyncService();
});
