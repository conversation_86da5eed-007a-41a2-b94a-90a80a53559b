import 'dhikr_model.dart';

enum RoutineType {
  preset,
  custom,
  template,
}

enum RoutineScheduleType {
  daily,
  weekly,
  monthly,
  custom,
}

class RoutineStep {
  final String id;
  final String arabicText;
  final String transliteration;
  final String translation;
  final int targetRepetitions;
  final int currentRepetitions;
  final String? source;
  final List<String> tags;
  final int order;

  const RoutineStep({
    required this.id,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.targetRepetitions,
    this.currentRepetitions = 0,
    this.source,
    this.tags = const [],
    required this.order,
  });

  RoutineStep copyWith({
    String? id,
    String? arabicText,
    String? transliteration,
    String? translation,
    int? targetRepetitions,
    int? currentRepetitions,
    String? source,
    List<String>? tags,
    int? order,
  }) {
    return RoutineStep(
      id: id ?? this.id,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      targetRepetitions: targetRepetitions ?? this.targetRepetitions,
      currentRepetitions: currentRepetitions ?? this.currentRepetitions,
      source: source ?? this.source,
      tags: tags ?? this.tags,
      order: order ?? this.order,
    );
  }

  double get progress => targetRepetitions > 0 ? currentRepetitions / targetRepetitions : 0.0;
  bool get isCompleted => currentRepetitions >= targetRepetitions;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabic_text': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'target_repetitions': targetRepetitions,
      'current_repetitions': currentRepetitions,
      'source': source,
      'tags': tags,
      'order': order,
    };
  }

  factory RoutineStep.fromJson(Map<String, dynamic> json) {
    return RoutineStep(
      id: json['id'],
      arabicText: json['arabic_text'],
      transliteration: json['transliteration'],
      translation: json['translation'],
      targetRepetitions: json['target_repetitions'],
      currentRepetitions: json['current_repetitions'] ?? 0,
      source: json['source'],
      tags: List<String>.from(json['tags'] ?? []),
      order: json['order'],
    );
  }
}

class RoutineSchedule {
  final RoutineScheduleType type;
  final List<int> weekdays; // 1-7 for Monday-Sunday
  final List<int> monthDays; // 1-31 for days of month
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String> reminderTimes; // HH:MM format
  final bool isActive;

  const RoutineSchedule({
    required this.type,
    this.weekdays = const [],
    this.monthDays = const [],
    this.startDate,
    this.endDate,
    this.reminderTimes = const [],
    this.isActive = true,
  });

  RoutineSchedule copyWith({
    RoutineScheduleType? type,
    List<int>? weekdays,
    List<int>? monthDays,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? reminderTimes,
    bool? isActive,
  }) {
    return RoutineSchedule(
      type: type ?? this.type,
      weekdays: weekdays ?? this.weekdays,
      monthDays: monthDays ?? this.monthDays,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      reminderTimes: reminderTimes ?? this.reminderTimes,
      isActive: isActive ?? this.isActive,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'weekdays': weekdays,
      'month_days': monthDays,
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'reminder_times': reminderTimes,
      'is_active': isActive,
    };
  }

  factory RoutineSchedule.fromJson(Map<String, dynamic> json) {
    return RoutineSchedule(
      type: RoutineScheduleType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RoutineScheduleType.daily,
      ),
      weekdays: List<int>.from(json['weekdays'] ?? []),
      monthDays: List<int>.from(json['month_days'] ?? []),
      startDate: json['start_date'] != null ? DateTime.parse(json['start_date']) : null,
      endDate: json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
      reminderTimes: List<String>.from(json['reminder_times'] ?? []),
      isActive: json['is_active'] ?? true,
    );
  }
}

class RoutineModel {
  final String id;
  final String userId;
  final String name;
  final String? description;
  final RoutineType type;
  final DhikrCategory category;
  final List<RoutineStep> steps;
  final RoutineSchedule? schedule;
  final int totalRepetitions;
  final int currentRepetitions;
  final bool isActive;
  final bool isFavorite;
  final List<String> tags;
  final String? templateId; // If created from template
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastCompletedAt;
  final bool isSynced;

  const RoutineModel({
    required this.id,
    required this.userId,
    required this.name,
    this.description,
    required this.type,
    required this.category,
    required this.steps,
    this.schedule,
    required this.totalRepetitions,
    this.currentRepetitions = 0,
    this.isActive = true,
    this.isFavorite = false,
    this.tags = const [],
    this.templateId,
    required this.createdAt,
    required this.updatedAt,
    this.lastCompletedAt,
    this.isSynced = false,
  });

  RoutineModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? description,
    RoutineType? type,
    DhikrCategory? category,
    List<RoutineStep>? steps,
    RoutineSchedule? schedule,
    int? totalRepetitions,
    int? currentRepetitions,
    bool? isActive,
    bool? isFavorite,
    List<String>? tags,
    String? templateId,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastCompletedAt,
    bool? isSynced,
  }) {
    return RoutineModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      category: category ?? this.category,
      steps: steps ?? this.steps,
      schedule: schedule ?? this.schedule,
      totalRepetitions: totalRepetitions ?? this.totalRepetitions,
      currentRepetitions: currentRepetitions ?? this.currentRepetitions,
      isActive: isActive ?? this.isActive,
      isFavorite: isFavorite ?? this.isFavorite,
      tags: tags ?? this.tags,
      templateId: templateId ?? this.templateId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastCompletedAt: lastCompletedAt ?? this.lastCompletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  double get progress => totalRepetitions > 0 ? currentRepetitions / totalRepetitions : 0.0;
  bool get isCompleted => currentRepetitions >= totalRepetitions;
  int get completedSteps => steps.where((step) => step.isCompleted).length;
  double get stepsProgress => steps.isNotEmpty ? completedSteps / steps.length : 0.0;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'description': description,
      'type': type.name,
      'category': category.name,
      'steps': steps.map((step) => step.toJson()).toList(),
      'schedule': schedule?.toJson(),
      'total_repetitions': totalRepetitions,
      'current_repetitions': currentRepetitions,
      'is_active': isActive,
      'is_favorite': isFavorite,
      'tags': tags,
      'template_id': templateId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_completed_at': lastCompletedAt?.toIso8601String(),
      'is_synced': isSynced,
    };
  }

  factory RoutineModel.fromJson(Map<String, dynamic> json) {
    return RoutineModel(
      id: json['id'],
      userId: json['user_id'],
      name: json['name'],
      description: json['description'],
      type: RoutineType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RoutineType.custom,
      ),
      category: DhikrCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => DhikrCategory.general,
      ),
      steps: (json['steps'] as List<dynamic>)
          .map((stepJson) => RoutineStep.fromJson(stepJson))
          .toList(),
      schedule: json['schedule'] != null ? RoutineSchedule.fromJson(json['schedule']) : null,
      totalRepetitions: json['total_repetitions'],
      currentRepetitions: json['current_repetitions'] ?? 0,
      isActive: json['is_active'] ?? true,
      isFavorite: json['is_favorite'] ?? false,
      tags: List<String>.from(json['tags'] ?? []),
      templateId: json['template_id'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      lastCompletedAt: json['last_completed_at'] != null 
          ? DateTime.parse(json['last_completed_at']) 
          : null,
      isSynced: json['is_synced'] ?? false,
    );
  }

  @override
  String toString() {
    return 'RoutineModel(id: $id, name: $name, type: $type, steps: ${steps.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RoutineModel &&
        other.id == id &&
        other.name == name &&
        other.type == type;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ type.hashCode;
  }
}
