import 'package:json_annotation/json_annotation.dart';
import 'finance_models.dart';

part 'transaction_model.g.dart';

@JsonSerializable()
class TransactionModel {
  final String id;
  final String userId;
  final String accountId;
  final String? categoryId;
  final String? toAccountId; // For transfers
  final TransactionType type;
  final double amount;
  final String currency;
  final String? description;
  final String? notes;
  final DateTime date;
  final String? receiptPath;
  final String? receiptUrl;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;
  final String? recurringId;

  const TransactionModel({
    required this.id,
    required this.userId,
    required this.accountId,
    this.categoryId,
    this.toAccountId,
    required this.type,
    required this.amount,
    this.currency = 'USD',
    this.description,
    this.notes,
    required this.date,
    this.receiptPath,
    this.receiptUrl,
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
    this.recurringId,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) =>
      _$TransactionModelFromJson(json);

  Map<String, dynamic> toJson() => _$TransactionModelToJson(this);

  TransactionModel copyWith({
    String? id,
    String? userId,
    String? accountId,
    String? categoryId,
    String? toAccountId,
    TransactionType? type,
    double? amount,
    String? currency,
    String? description,
    String? notes,
    DateTime? date,
    String? receiptPath,
    String? receiptUrl,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
    String? recurringId,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      accountId: accountId ?? this.accountId,
      categoryId: categoryId ?? this.categoryId,
      toAccountId: toAccountId ?? this.toAccountId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      date: date ?? this.date,
      receiptPath: receiptPath ?? this.receiptPath,
      receiptUrl: receiptUrl ?? this.receiptUrl,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
      recurringId: recurringId ?? this.recurringId,
    );
  }

  bool get isTransfer => type == TransactionType.transfer && toAccountId != null;
  bool get isRecurring => recurringId != null;
  
  String get formattedAmount {
    final sign = type == TransactionType.expense ? '-' : '+';
    return '$sign\$${amount.toStringAsFixed(2)}';
  }
}

@JsonSerializable()
class BudgetModel {
  final String id;
  final String userId;
  final String categoryId;
  final String name;
  final double amount;
  final double spent;
  final String period; // monthly, weekly, yearly
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const BudgetModel({
    required this.id,
    required this.userId,
    required this.categoryId,
    required this.name,
    required this.amount,
    this.spent = 0.0,
    this.period = 'monthly',
    required this.startDate,
    required this.endDate,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  factory BudgetModel.fromJson(Map<String, dynamic> json) =>
      _$BudgetModelFromJson(json);

  Map<String, dynamic> toJson() => _$BudgetModelToJson(this);

  BudgetModel copyWith({
    String? id,
    String? userId,
    String? categoryId,
    String? name,
    double? amount,
    double? spent,
    String? period,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return BudgetModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      categoryId: categoryId ?? this.categoryId,
      name: name ?? this.name,
      amount: amount ?? this.amount,
      spent: spent ?? this.spent,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  double get progressPercentage {
    if (amount == 0) return 0.0;
    return (spent / amount).clamp(0.0, 1.0);
  }

  double get remaining => (amount - spent).clamp(0.0, double.infinity);
  bool get isOverBudget => spent > amount;
}
