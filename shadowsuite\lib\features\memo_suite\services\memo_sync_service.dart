import 'dart:async';
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/models/memo_model.dart';
import '../../../core/models/todo_item_model.dart';
import '../../../core/models/file_attachment_model.dart';
import '../../../core/services/supabase_service.dart';
import 'memo_database_service.dart';

class MemoSyncService {
  final SupabaseService _supabaseService = SupabaseService();
  final MemoDatabaseService _databaseService = MemoDatabaseService();

  RealtimeChannel? _memosChannel;
  RealtimeChannel? _todosChannel;
  RealtimeChannel? _attachmentsChannel;

  StreamController<SyncStatus>? _syncStatusController;
  Stream<SyncStatus>? _syncStatusStream;

  bool _isInitialized = false;
  String? _currentUserId;

  /// Initialize sync service with user authentication
  Future<void> initialize(String userId) async {
    if (_isInitialized) return;

    _currentUserId = userId;
    _syncStatusController = StreamController<SyncStatus>.broadcast();
    _syncStatusStream = _syncStatusController!.stream;

    await _setupRealtimeSubscriptions();
    _isInitialized = true;
  }

  /// Get sync status stream
  Stream<SyncStatus> get syncStatusStream => _syncStatusStream!;

  /// Setup real-time subscriptions for all memo-related tables
  Future<void> _setupRealtimeSubscriptions() async {
    if (_currentUserId == null) return;

    // Subscribe to memos table changes
    _memosChannel = _supabaseService.subscribeToTable(
      table: 'memos',
      filter: _currentUserId!,
      onInsert: _handleMemoInsert,
      onUpdate: _handleMemoUpdate,
      onDelete: _handleMemoDelete,
    );

    // Subscribe to todo_items table changes
    _todosChannel = _supabaseService.subscribeToTable(
      table: 'todo_items',
      filter: _currentUserId!,
      onInsert: _handleTodoInsert,
      onUpdate: _handleTodoUpdate,
      onDelete: _handleTodoDelete,
    );

    // Subscribe to file_attachments table changes
    _attachmentsChannel = _supabaseService.subscribeToTable(
      table: 'file_attachments',
      filter: _currentUserId!,
      onInsert: _handleAttachmentInsert,
      onUpdate: _handleAttachmentUpdate,
      onDelete: _handleAttachmentDelete,
    );
  }

  /// Sync all unsynced local data to Supabase
  Future<void> syncToCloud() async {
    if (_currentUserId == null) return;

    _emitSyncStatus(SyncStatus.syncing);

    try {
      // Get all unsynced memos
      final unsyncedMemos = await _databaseService.getUnsyncedMemos(
        _currentUserId!,
      );

      for (final memo in unsyncedMemos) {
        await _syncMemoToCloud(memo);
      }

      _emitSyncStatus(SyncStatus.completed);
    } catch (e) {
      print('Error syncing to cloud: $e');
      _emitSyncStatus(SyncStatus.error);
    }
  }

  /// Sync all cloud data to local database
  Future<void> syncFromCloud() async {
    if (_currentUserId == null) return;

    _emitSyncStatus(SyncStatus.syncing);

    try {
      // Fetch all memos from Supabase
      final cloudMemos = await _supabaseService.select(
        table: 'memos',
        where: 'user_id=$_currentUserId',
        orderBy: 'created_at',
      );

      for (final memoData in cloudMemos) {
        await _syncMemoFromCloud(memoData);
      }

      _emitSyncStatus(SyncStatus.completed);
    } catch (e) {
      print('Error syncing from cloud: $e');
      _emitSyncStatus(SyncStatus.error);
    }
  }

  /// Perform bidirectional sync
  Future<void> performFullSync() async {
    if (_currentUserId == null) return;

    _emitSyncStatus(SyncStatus.syncing);

    try {
      // First sync local changes to cloud
      await syncToCloud();

      // Then sync cloud changes to local
      await syncFromCloud();

      _emitSyncStatus(SyncStatus.completed);
    } catch (e) {
      print('Error performing full sync: $e');
      _emitSyncStatus(SyncStatus.error);
    }
  }

  /// Sync individual memo to cloud
  Future<void> _syncMemoToCloud(MemoModel memo) async {
    try {
      // Convert memo to Supabase format
      final memoData = _memoToSupabaseMap(memo);

      // Check if memo exists in cloud
      final existing = await _supabaseService.select(
        table: 'memos',
        where: 'id=${memo.id}',
        limit: 1,
      );

      if (existing.isEmpty) {
        // Insert new memo
        await _supabaseService.insert(table: 'memos', data: memoData);
      } else {
        // Update existing memo
        await _supabaseService.update(
          table: 'memos',
          data: memoData,
          id: memo.id,
        );
      }

      // Sync todos
      for (final todo in memo.todoItems) {
        await _syncTodoToCloud(todo, memo.id);
      }

      // Sync attachments
      for (final attachment in memo.attachments) {
        await _syncAttachmentToCloud(attachment, memo.id);
      }

      // Mark as synced in local database
      await _databaseService.markMemoAsSynced(memo.id);
    } catch (e) {
      print('Error syncing memo ${memo.id} to cloud: $e');
      rethrow;
    }
  }

  /// Sync individual memo from cloud
  Future<void> _syncMemoFromCloud(Map<String, dynamic> memoData) async {
    try {
      // Get todos and attachments
      final todos = await _supabaseService.select(
        table: 'todo_items',
        where: 'memo_id=${memoData['id']}',
      );

      final attachments = await _supabaseService.select(
        table: 'file_attachments',
        where: 'memo_id=${memoData['id']}',
      );

      // Convert to local models
      final memo = _memoFromSupabaseMap(memoData, todos, attachments);

      // Check if memo exists locally
      final existingMemo = await _databaseService.getMemoById(memo.id);

      if (existingMemo == null) {
        // Create new memo
        await _databaseService.createMemo(memo);
      } else {
        // Update existing memo (conflict resolution: cloud wins)
        await _databaseService.updateMemo(memo);
      }
    } catch (e) {
      print('Error syncing memo from cloud: $e');
      rethrow;
    }
  }

  /// Sync todo to cloud
  Future<void> _syncTodoToCloud(TodoItemModel todo, String memoId) async {
    try {
      final todoData = _todoToSupabaseMap(todo, memoId);

      final existing = await _supabaseService.select(
        table: 'todo_items',
        where: 'id=${todo.id}',
        limit: 1,
      );

      if (existing.isEmpty) {
        await _supabaseService.insert(table: 'todo_items', data: todoData);
      } else {
        await _supabaseService.update(
          table: 'todo_items',
          data: todoData,
          id: todo.id,
        );
      }
    } catch (e) {
      print('Error syncing todo ${todo.id} to cloud: $e');
    }
  }

  /// Sync attachment to cloud
  Future<void> _syncAttachmentToCloud(
    FileAttachmentModel attachment,
    String memoId,
  ) async {
    try {
      final attachmentData = _attachmentToSupabaseMap(attachment, memoId);

      final existing = await _supabaseService.select(
        table: 'file_attachments',
        where: 'id=${attachment.id}',
        limit: 1,
      );

      if (existing.isEmpty) {
        await _supabaseService.insert(
          table: 'file_attachments',
          data: attachmentData,
        );
      } else {
        await _supabaseService.update(
          table: 'file_attachments',
          data: attachmentData,
          id: attachment.id,
        );
      }
    } catch (e) {
      print('Error syncing attachment ${attachment.id} to cloud: $e');
    }
  }

  // Real-time event handlers
  void _handleMemoInsert(PostgresChangePayload payload) {
    print('Memo inserted: ${payload.newRecord}');
    // Handle real-time memo insertion
    _emitSyncStatus(SyncStatus.realtimeUpdate);
  }

  void _handleMemoUpdate(PostgresChangePayload payload) {
    print('Memo updated: ${payload.newRecord}');
    // Handle real-time memo update
    _emitSyncStatus(SyncStatus.realtimeUpdate);
  }

  void _handleMemoDelete(PostgresChangePayload payload) {
    print('Memo deleted: ${payload.oldRecord}');
    // Handle real-time memo deletion
    _emitSyncStatus(SyncStatus.realtimeUpdate);
  }

  void _handleTodoInsert(PostgresChangePayload payload) {
    print('Todo inserted: ${payload.newRecord}');
    _emitSyncStatus(SyncStatus.realtimeUpdate);
  }

  void _handleTodoUpdate(PostgresChangePayload payload) {
    print('Todo updated: ${payload.newRecord}');
    _emitSyncStatus(SyncStatus.realtimeUpdate);
  }

  void _handleTodoDelete(PostgresChangePayload payload) {
    print('Todo deleted: ${payload.oldRecord}');
    _emitSyncStatus(SyncStatus.realtimeUpdate);
  }

  void _handleAttachmentInsert(PostgresChangePayload payload) {
    print('Attachment inserted: ${payload.newRecord}');
    _emitSyncStatus(SyncStatus.realtimeUpdate);
  }

  void _handleAttachmentUpdate(PostgresChangePayload payload) {
    print('Attachment updated: ${payload.newRecord}');
    _emitSyncStatus(SyncStatus.realtimeUpdate);
  }

  void _handleAttachmentDelete(PostgresChangePayload payload) {
    print('Attachment deleted: ${payload.oldRecord}');
    _emitSyncStatus(SyncStatus.realtimeUpdate);
  }

  /// Emit sync status
  void _emitSyncStatus(SyncStatus status) {
    _syncStatusController?.add(status);
  }

  /// Convert MemoModel to Supabase map
  Map<String, dynamic> _memoToSupabaseMap(MemoModel memo) {
    return {
      'id': memo.id,
      'user_id': memo.userId,
      'title': memo.title,
      'description': memo.description,
      'rich_content': memo.richContent,
      'transcription': memo.transcription,
      'audio_file_path': memo.audioFilePath,
      'audio_url': memo.audioUrl,
      'duration': memo.duration,
      'type': memo.type.name,
      'category': memo.category,
      'is_pinned': memo.isPinned,
      'is_archived': memo.isArchived,
      'created_at': memo.createdAt.toIso8601String(),
      'updated_at': memo.updatedAt.toIso8601String(),
      'is_synced': true,
      'tags': jsonEncode(memo.tags),
    };
  }

  /// Convert TodoItemModel to Supabase map
  Map<String, dynamic> _todoToSupabaseMap(TodoItemModel todo, String memoId) {
    return {
      'id': todo.id,
      'memo_id': memoId,
      'text': todo.text,
      'is_completed': todo.isCompleted,
      'due_date': todo.dueDate?.toIso8601String(),
      'priority': todo.priority.name,
      'created_at': todo.createdAt.toIso8601String(),
      'updated_at': todo.updatedAt.toIso8601String(),
    };
  }

  /// Convert FileAttachmentModel to Supabase map
  Map<String, dynamic> _attachmentToSupabaseMap(
    FileAttachmentModel attachment,
    String memoId,
  ) {
    return {
      'id': attachment.id,
      'memo_id': memoId,
      'name': attachment.name,
      'path': attachment.path,
      'url': attachment.url,
      'mime_type': attachment.mimeType,
      'size': attachment.size,
      'uploaded_at': attachment.uploadedAt.toIso8601String(),
    };
  }

  /// Convert Supabase map to MemoModel
  MemoModel _memoFromSupabaseMap(
    Map<String, dynamic> memoData,
    List<Map<String, dynamic>> todosData,
    List<Map<String, dynamic>> attachmentsData,
  ) {
    // Convert todos
    final todos = todosData
        .map(
          (todoData) => TodoItemModel(
            id: todoData['id'] as String,
            text: todoData['text'] as String,
            isCompleted: todoData['is_completed'] as bool,
            dueDate: todoData['due_date'] != null
                ? DateTime.parse(todoData['due_date'] as String)
                : null,
            priority: Priority.values.firstWhere(
              (p) => p.name == todoData['priority'],
              orElse: () => Priority.medium,
            ),
            createdAt: DateTime.parse(todoData['created_at'] as String),
            updatedAt: DateTime.parse(todoData['updated_at'] as String),
          ),
        )
        .toList();

    // Convert attachments
    final attachments = attachmentsData
        .map(
          (attachmentData) => FileAttachmentModel(
            id: attachmentData['id'] as String,
            name: attachmentData['name'] as String,
            path: attachmentData['path'] as String,
            url: attachmentData['url'] as String?,
            mimeType: attachmentData['mime_type'] as String,
            size: attachmentData['size'] as int,
            uploadedAt: DateTime.parse(attachmentData['uploaded_at'] as String),
          ),
        )
        .toList();

    // Parse tags
    final tagsJson = memoData['tags'] as String?;
    final tags = tagsJson != null && tagsJson.isNotEmpty
        ? List<String>.from(jsonDecode(tagsJson))
        : <String>[];

    return MemoModel(
      id: memoData['id'] as String,
      userId: memoData['user_id'] as String,
      title: memoData['title'] as String,
      description: memoData['description'] as String?,
      richContent: memoData['rich_content'] as String?,
      transcription: memoData['transcription'] as String?,
      audioFilePath: memoData['audio_file_path'] as String?,
      audioUrl: memoData['audio_url'] as String?,
      duration: memoData['duration'] as int,
      type: MemoType.values.firstWhere(
        (t) => t.name == memoData['type'],
        orElse: () => MemoType.text,
      ),
      todoItems: todos,
      attachments: attachments,
      createdAt: DateTime.parse(memoData['created_at'] as String),
      updatedAt: DateTime.parse(memoData['updated_at'] as String),
      isSynced: true, // Always true for cloud data
      tags: tags,
      category: memoData['category'] as String?,
      isPinned: memoData['is_pinned'] as bool? ?? false,
      isArchived: memoData['is_archived'] as bool? ?? false,
    );
  }

  /// Dispose resources
  void dispose() {
    _memosChannel?.unsubscribe();
    _todosChannel?.unsubscribe();
    _attachmentsChannel?.unsubscribe();
    _syncStatusController?.close();
    _isInitialized = false;
  }
}

/// Sync status enumeration
enum SyncStatus { idle, syncing, completed, error, realtimeUpdate }
