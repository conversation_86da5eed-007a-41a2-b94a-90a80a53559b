import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/memo_model.dart';

import '../../../core/services/supabase_service.dart';
import 'memo_database_service.dart';
import 'memo_sync_service.dart';

class MemoService {
  final MemoDatabaseService _memoDatabaseService = MemoDatabaseService();
  final SupabaseService _supabaseService = SupabaseService();
  final MemoSyncService _syncService = MemoSyncService();
  final Uuid _uuid = const Uuid();

  // Mock user ID for now - will be replaced with actual auth
  String get _currentUserId => 'demo-user-id';

  Future<List<MemoModel>> getAllMemos() async {
    try {
      return await _memoDatabaseService.getAllMemos(_currentUserId);
    } catch (e) {
      print('Error getting all memos: $e');
      return [];
    }
  }

  Future<MemoModel?> getMemoById(String id) async {
    try {
      return await _memoDatabaseService.getMemoById(id);
    } catch (e) {
      print('Error getting memo by ID: $e');
      return null;
    }
  }

  Future<void> createMemo(MemoModel memo) async {
    try {
      await _memoDatabaseService.createMemo(memo);
    } catch (e) {
      print('Error creating memo: $e');
      rethrow;
    }
  }

  Future<void> updateMemo(MemoModel memo) async {
    try {
      final updatedMemo = memo.copyWith(updatedAt: DateTime.now());
      await _memoDatabaseService.updateMemo(updatedMemo);
    } catch (e) {
      print('Error updating memo: $e');
      rethrow;
    }
  }

  Future<void> deleteMemo(String id) async {
    try {
      // Get memo to delete audio file and handle Supabase cleanup
      final memo = await getMemoById(id);

      // Delete from local database first
      await _memoDatabaseService.deleteMemo(id);

      // Clean up local audio file
      if (memo?.audioFilePath != null) {
        try {
          final file = File(memo!.audioFilePath!);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          print('Error deleting audio file: $e');
        }
      }

      // Delete from Supabase if synced
      if (memo?.isSynced == true) {
        try {
          await _supabaseService.delete(table: 'memos', id: id);

          // Delete audio file from storage
          if (memo?.audioUrl != null) {
            final fileName = path.basename(memo!.audioUrl!);
            await _supabaseService.deleteFile(
              bucket: 'audio-files',
              path: '$_currentUserId/$fileName',
            );
          }
        } catch (e) {
          print('Error deleting from Supabase: $e');
        }
      }
    } catch (e) {
      print('Error deleting memo: $e');
      rethrow;
    }
  }

  Future<List<MemoModel>> searchMemos(String query) async {
    try {
      return await _memoDatabaseService.searchMemos(_currentUserId, query);
    } catch (e) {
      print('Error searching memos: $e');
      return [];
    }
  }

  Future<String> saveAudioFile(String tempPath) async {
    final appDir = await getApplicationDocumentsDirectory();
    final audioDir = Directory(path.join(appDir.path, 'audio'));

    if (!await audioDir.exists()) {
      await audioDir.create(recursive: true);
    }

    final fileName = '${_uuid.v4()}.m4a';
    final finalPath = path.join(audioDir.path, fileName);

    final tempFile = File(tempPath);
    await tempFile.copy(finalPath);
    await tempFile.delete();

    return finalPath;
  }

  Future<String?> uploadAudioFile(String localPath) async {
    try {
      final userId = _currentUserId;

      final file = File(localPath);
      if (!await file.exists()) return null;

      final fileName = path.basename(localPath);
      final remotePath = '$userId/$fileName';

      final url = await _supabaseService.uploadFile(
        bucket: 'audio-files',
        path: remotePath,
        file: file,
      );

      return url;
    } catch (e) {
      print('Error uploading audio file: $e');
      return null;
    }
  }

  Future<void> syncMemos() async {
    try {
      // Initialize sync service if not already done
      await _syncService.initialize(_currentUserId);

      // Perform full bidirectional sync
      await _syncService.performFullSync();
    } catch (e) {
      print('Error syncing memos: $e');
      rethrow;
    }
  }

  /// Get sync status stream
  Stream<SyncStatus> get syncStatusStream => _syncService.syncStatusStream;

  /// Initialize sync service
  Future<void> initializeSync() async {
    try {
      await _syncService.initialize(_currentUserId);
    } catch (e) {
      print('Error initializing sync: $e');
    }
  }

  /// Dispose sync service
  void disposeSync() {
    _syncService.dispose();
  }
}
