import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/memo_model.dart';
import '../../../core/services/database_service.dart';
import '../../../core/services/supabase_service.dart';

class MemoService {
  final DatabaseService _databaseService = DatabaseService();
  final SupabaseService _supabaseService = SupabaseService();
  final Uuid _uuid = const Uuid();

  // Mock user ID for now - will be replaced with actual auth
  String get _currentUserId => 'demo-user-id';

  Future<List<MemoModel>> getAllMemos() async {
    final db = await _databaseService.database;
    final userId = _currentUserId;

    final maps = await db.query(
      'memos',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => _memoFromDatabase(map)).toList();
  }

  Future<MemoModel?> getMemoById(String id) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'memos',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isEmpty) return null;
    return _memoFromDatabase(maps.first);
  }

  Future<void> createMemo(MemoModel memo) async {
    final db = await _databaseService.database;

    await db.insert('memos', _memoToDatabase(memo));

    // TODO: Mark for sync when sync service is implemented
  }

  Future<void> updateMemo(MemoModel memo) async {
    final db = await _databaseService.database;

    final updatedMemo = memo.copyWith(updatedAt: DateTime.now());

    await db.update(
      'memos',
      _memoToDatabase(updatedMemo),
      where: 'id = ?',
      whereArgs: [memo.id],
    );

    // TODO: Mark for sync when sync service is implemented
  }

  Future<void> deleteMemo(String id) async {
    final db = await _databaseService.database;

    // Get memo to delete audio file
    final memo = await getMemoById(id);
    if (memo?.audioFilePath != null) {
      try {
        final file = File(memo!.audioFilePath!);
        if (await file.exists()) {
          await file.delete();
        }
      } catch (e) {
        print('Error deleting audio file: $e');
      }
    }

    await db.delete('memos', where: 'id = ?', whereArgs: [id]);

    // Delete from Supabase if synced
    if (memo?.isSynced == true) {
      try {
        await _supabaseService.delete(table: 'memos', id: id);

        // Delete audio file from storage
        if (memo?.audioUrl != null) {
          final fileName = path.basename(memo!.audioUrl!);
          await _supabaseService.deleteFile(
            bucket: 'audio-files',
            path: '$_currentUserId/$fileName',
          );
        }
      } catch (e) {
        print('Error deleting from Supabase: $e');
      }
    }
  }

  Future<List<MemoModel>> searchMemos(String query) async {
    final db = await _databaseService.database;
    final userId = _currentUserId;

    final maps = await db.query(
      'memos',
      where: '''
        user_id = ? AND (
          title LIKE ? OR
          description LIKE ? OR
          transcription LIKE ? OR
          tags LIKE ?
        )
      ''',
      whereArgs: [userId, '%$query%', '%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => _memoFromDatabase(map)).toList();
  }

  Future<String> saveAudioFile(String tempPath) async {
    final appDir = await getApplicationDocumentsDirectory();
    final audioDir = Directory(path.join(appDir.path, 'audio'));

    if (!await audioDir.exists()) {
      await audioDir.create(recursive: true);
    }

    final fileName = '${_uuid.v4()}.m4a';
    final finalPath = path.join(audioDir.path, fileName);

    final tempFile = File(tempPath);
    await tempFile.copy(finalPath);
    await tempFile.delete();

    return finalPath;
  }

  Future<String?> uploadAudioFile(String localPath) async {
    try {
      final userId = _currentUserId;

      final file = File(localPath);
      if (!await file.exists()) return null;

      final fileName = path.basename(localPath);
      final remotePath = '$userId/$fileName';

      final url = await _supabaseService.uploadFile(
        bucket: 'audio-files',
        path: remotePath,
        file: file,
      );

      return url;
    } catch (e) {
      print('Error uploading audio file: $e');
      return null;
    }
  }

  Future<void> syncMemos() async {
    // TODO: Implement sync when sync service is available
  }

  MemoModel _memoFromDatabase(Map<String, dynamic> data) {
    return MemoModel(
      id: data['id'],
      userId: data['user_id'],
      title: data['title'],
      description: data['description'],
      transcription: data['transcription'],
      audioFilePath: data['audio_file_path'],
      audioUrl: data['audio_url'],
      duration: data['duration'] ?? 0,
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      isSynced: data['is_synced'] == 1,
      tags: data['tags'] != null
          ? List<String>.from(jsonDecode(data['tags']))
          : [],
    );
  }

  Map<String, dynamic> _memoToDatabase(MemoModel memo) {
    return {
      'id': memo.id,
      'user_id': memo.userId,
      'title': memo.title,
      'description': memo.description,
      'transcription': memo.transcription,
      'audio_file_path': memo.audioFilePath,
      'audio_url': memo.audioUrl,
      'duration': memo.duration,
      'created_at': memo.createdAt.toIso8601String(),
      'updated_at': memo.updatedAt.toIso8601String(),
      'is_synced': memo.isSynced ? 1 : 0,
      'tags': jsonEncode(memo.tags),
    };
  }
}
