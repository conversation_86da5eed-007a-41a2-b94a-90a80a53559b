import 'package:json_annotation/json_annotation.dart';

part 'memo_model.g.dart';

@JsonSerializable()
class MemoModel {
  final String id;
  final String userId;
  final String title;
  final String? description;
  final String? transcription;
  final String? audioFilePath;
  final String? audioUrl;
  final int duration; // in seconds
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;
  final List<String> tags;

  const MemoModel({
    required this.id,
    required this.userId,
    required this.title,
    this.description,
    this.transcription,
    this.audioFilePath,
    this.audioUrl,
    this.duration = 0,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
    this.tags = const [],
  });

  factory MemoModel.fromJson(Map<String, dynamic> json) =>
      _$MemoModelFromJson(json);

  Map<String, dynamic> toJson() => _$MemoModelToJson(this);

  MemoModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    String? transcription,
    String? audioFilePath,
    String? audioUrl,
    int? duration,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
    List<String>? tags,
  }) {
    return MemoModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      transcription: transcription ?? this.transcription,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      audioUrl: audioUrl ?? this.audioUrl,
      duration: duration ?? this.duration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
      tags: tags ?? this.tags,
    );
  }

  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MemoModel &&
        other.id == id &&
        other.userId == userId &&
        other.title == title &&
        other.description == description &&
        other.transcription == transcription &&
        other.audioFilePath == audioFilePath &&
        other.audioUrl == audioUrl &&
        other.duration == duration &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.isSynced == isSynced;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        title.hashCode ^
        description.hashCode ^
        transcription.hashCode ^
        audioFilePath.hashCode ^
        audioUrl.hashCode ^
        duration.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        isSynced.hashCode;
  }

  @override
  String toString() {
    return 'MemoModel(id: $id, userId: $userId, title: $title, description: $description, transcription: $transcription, audioFilePath: $audioFilePath, audioUrl: $audioUrl, duration: $duration, createdAt: $createdAt, updatedAt: $updatedAt, isSynced: $isSynced, tags: $tags)';
  }
}
