import 'package:json_annotation/json_annotation.dart';
import 'todo_item_model.dart';
import 'file_attachment_model.dart';

part 'memo_model.g.dart';

enum MemoType { text, voice, todo, mixed }

@JsonSerializable()
class MemoModel {
  final String id;
  final String userId;
  final String title;
  final String? description;
  final String? richContent; // HTML or markdown content
  final String? transcription;
  final String? audioFilePath;
  final String? audioUrl;
  final int duration; // in seconds
  final MemoType type;
  final List<TodoItemModel> todoItems;
  final List<FileAttachmentModel> attachments;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;
  final List<String> tags;
  final String? category;
  final bool isPinned;
  final bool isArchived;

  const MemoModel({
    required this.id,
    required this.userId,
    required this.title,
    this.description,
    this.richContent,
    this.transcription,
    this.audioFilePath,
    this.audioUrl,
    this.duration = 0,
    this.type = MemoType.text,
    this.todoItems = const [],
    this.attachments = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
    this.tags = const [],
    this.category,
    this.isPinned = false,
    this.isArchived = false,
  });

  factory MemoModel.fromJson(Map<String, dynamic> json) =>
      _$MemoModelFromJson(json);

  Map<String, dynamic> toJson() => _$MemoModelToJson(this);

  MemoModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    String? richContent,
    String? transcription,
    String? audioFilePath,
    String? audioUrl,
    int? duration,
    MemoType? type,
    List<TodoItemModel>? todoItems,
    List<FileAttachmentModel>? attachments,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
    List<String>? tags,
    String? category,
    bool? isPinned,
    bool? isArchived,
  }) {
    return MemoModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      richContent: richContent ?? this.richContent,
      transcription: transcription ?? this.transcription,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      audioUrl: audioUrl ?? this.audioUrl,
      duration: duration ?? this.duration,
      type: type ?? this.type,
      todoItems: todoItems ?? this.todoItems,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
      tags: tags ?? this.tags,
      category: category ?? this.category,
      isPinned: isPinned ?? this.isPinned,
      isArchived: isArchived ?? this.isArchived,
    );
  }

  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MemoModel &&
        other.id == id &&
        other.userId == userId &&
        other.title == title &&
        other.description == description &&
        other.richContent == richContent &&
        other.transcription == transcription &&
        other.audioFilePath == audioFilePath &&
        other.audioUrl == audioUrl &&
        other.duration == duration &&
        other.type == type &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.isSynced == isSynced &&
        other.category == category &&
        other.isPinned == isPinned &&
        other.isArchived == isArchived;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        title.hashCode ^
        description.hashCode ^
        transcription.hashCode ^
        audioFilePath.hashCode ^
        audioUrl.hashCode ^
        duration.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        isSynced.hashCode;
  }

  @override
  String toString() {
    return 'MemoModel(id: $id, userId: $userId, title: $title, description: $description, transcription: $transcription, audioFilePath: $audioFilePath, audioUrl: $audioUrl, duration: $duration, createdAt: $createdAt, updatedAt: $updatedAt, isSynced: $isSynced, tags: $tags)';
  }
}
