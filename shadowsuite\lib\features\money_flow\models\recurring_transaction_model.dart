import 'transaction_model.dart';

enum RecurrenceFrequency { daily, weekly, biweekly, monthly, quarterly, yearly }

extension RecurrenceFrequencyExtension on RecurrenceFrequency {
  String get displayName {
    switch (this) {
      case RecurrenceFrequency.daily:
        return 'Daily';
      case RecurrenceFrequency.weekly:
        return 'Weekly';
      case RecurrenceFrequency.biweekly:
        return 'Bi-weekly';
      case RecurrenceFrequency.monthly:
        return 'Monthly';
      case RecurrenceFrequency.quarterly:
        return 'Quarterly';
      case RecurrenceFrequency.yearly:
        return 'Yearly';
    }
  }

  int get daysInterval {
    switch (this) {
      case RecurrenceFrequency.daily:
        return 1;
      case RecurrenceFrequency.weekly:
        return 7;
      case RecurrenceFrequency.biweekly:
        return 14;
      case RecurrenceFrequency.monthly:
        return 30; // Approximate
      case RecurrenceFrequency.quarterly:
        return 90; // Approximate
      case RecurrenceFrequency.yearly:
        return 365; // Approximate
    }
  }
}

class RecurringTransactionModel {
  final String id;
  final String userId;
  final String accountId;
  final TransactionType type;
  final double amount;
  final String? categoryId;
  final String title;
  final String? description;
  final List<String> tags;
  final String currency;
  final RecurrenceFrequency frequency;
  final DateTime startDate;
  final DateTime? endDate;
  final int? maxOccurrences;
  final DateTime? lastExecuted;
  final DateTime nextDue;
  final bool isActive;
  final bool autoExecute;
  final int executionCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const RecurringTransactionModel({
    required this.id,
    required this.userId,
    required this.accountId,
    required this.type,
    required this.amount,
    this.categoryId,
    required this.title,
    this.description,
    this.tags = const [],
    this.currency = 'USD',
    required this.frequency,
    required this.startDate,
    this.endDate,
    this.maxOccurrences,
    this.lastExecuted,
    required this.nextDue,
    this.isActive = true,
    this.autoExecute = false,
    this.executionCount = 0,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  RecurringTransactionModel copyWith({
    String? id,
    String? userId,
    String? accountId,
    TransactionType? type,
    double? amount,
    String? categoryId,
    String? title,
    String? description,
    List<String>? tags,
    String? currency,
    RecurrenceFrequency? frequency,
    DateTime? startDate,
    DateTime? endDate,
    int? maxOccurrences,
    DateTime? lastExecuted,
    DateTime? nextDue,
    bool? isActive,
    bool? autoExecute,
    int? executionCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return RecurringTransactionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      accountId: accountId ?? this.accountId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      categoryId: categoryId ?? this.categoryId,
      title: title ?? this.title,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      currency: currency ?? this.currency,
      frequency: frequency ?? this.frequency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      maxOccurrences: maxOccurrences ?? this.maxOccurrences,
      lastExecuted: lastExecuted ?? this.lastExecuted,
      nextDue: nextDue ?? this.nextDue,
      isActive: isActive ?? this.isActive,
      autoExecute: autoExecute ?? this.autoExecute,
      executionCount: executionCount ?? this.executionCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'account_id': accountId,
      'type': type.name,
      'amount': amount,
      'category_id': categoryId,
      'title': title,
      'description': description,
      'tags': tags,
      'currency': currency,
      'frequency': frequency.name,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'max_occurrences': maxOccurrences,
      'last_executed': lastExecuted?.toIso8601String(),
      'next_due': nextDue.toIso8601String(),
      'is_active': isActive,
      'auto_execute': autoExecute,
      'execution_count': executionCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_synced': isSynced,
    };
  }

  factory RecurringTransactionModel.fromJson(Map<String, dynamic> json) {
    return RecurringTransactionModel(
      id: json['id'],
      userId: json['user_id'],
      accountId: json['account_id'],
      type: TransactionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TransactionType.expense,
      ),
      amount: json['amount'].toDouble(),
      categoryId: json['category_id'],
      title: json['title'],
      description: json['description'],
      tags: List<String>.from(json['tags'] ?? []),
      currency: json['currency'] ?? 'USD',
      frequency: RecurrenceFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
        orElse: () => RecurrenceFrequency.monthly,
      ),
      startDate: DateTime.parse(json['start_date']),
      endDate: json['end_date'] != null
          ? DateTime.parse(json['end_date'])
          : null,
      maxOccurrences: json['max_occurrences'],
      lastExecuted: json['last_executed'] != null
          ? DateTime.parse(json['last_executed'])
          : null,
      nextDue: DateTime.parse(json['next_due']),
      isActive: json['is_active'] ?? true,
      autoExecute: json['auto_execute'] ?? false,
      executionCount: json['execution_count'] ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      isSynced: json['is_synced'] ?? false,
    );
  }

  TransactionModel toTransaction({DateTime? customDate}) {
    return TransactionModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      accountId: accountId,
      type: type,
      amount: amount,
      categoryId: categoryId ?? '',
      title: title,
      description: description,
      tags: tags,
      currency: currency,
      date: customDate ?? nextDue,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  DateTime calculateNextDue() {
    final baseDate = lastExecuted ?? startDate;

    switch (frequency) {
      case RecurrenceFrequency.daily:
        return baseDate.add(const Duration(days: 1));
      case RecurrenceFrequency.weekly:
        return baseDate.add(const Duration(days: 7));
      case RecurrenceFrequency.biweekly:
        return baseDate.add(const Duration(days: 14));
      case RecurrenceFrequency.monthly:
        return DateTime(baseDate.year, baseDate.month + 1, baseDate.day);
      case RecurrenceFrequency.quarterly:
        return DateTime(baseDate.year, baseDate.month + 3, baseDate.day);
      case RecurrenceFrequency.yearly:
        return DateTime(baseDate.year + 1, baseDate.month, baseDate.day);
    }
  }

  bool get isDue =>
      DateTime.now().isAfter(nextDue) ||
      DateTime.now().isAtSameMomentAs(nextDue);

  bool get isOverdue =>
      DateTime.now().isAfter(nextDue.add(const Duration(days: 1)));

  bool get shouldStop {
    if (!isActive) return true;
    if (endDate != null && DateTime.now().isAfter(endDate!)) return true;
    if (maxOccurrences != null && executionCount >= maxOccurrences!) {
      return true;
    }
    return false;
  }

  @override
  String toString() {
    return 'RecurringTransactionModel(id: $id, title: $title, frequency: $frequency)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecurringTransactionModel &&
        other.id == id &&
        other.title == title &&
        other.frequency == frequency;
  }

  @override
  int get hashCode {
    return id.hashCode ^ title.hashCode ^ frequency.hashCode;
  }
}
