import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/routine_model.dart';
import '../models/dhikr_model.dart';
import '../providers/routine_provider.dart';

class RoutineBuilderScreen extends ConsumerStatefulWidget {
  final RoutineModel? routine; // For editing existing routine
  final RoutineModel? template; // For creating from template

  const RoutineBuilderScreen({super.key, this.routine, this.template});

  @override
  ConsumerState<RoutineBuilderScreen> createState() =>
      _RoutineBuilderScreenState();
}

class _RoutineBuilderScreenState extends ConsumerState<RoutineBuilderScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  DhikrCategory _selectedCategory = DhikrCategory.general;
  List<RoutineStep> _steps = [];
  RoutineSchedule? _schedule;
  List<String> _tags = [];
  bool _isActive = true;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.routine != null) {
      // Editing existing routine
      final routine = widget.routine!;
      _nameController.text = routine.name;
      _descriptionController.text = routine.description ?? '';
      _selectedCategory = routine.category;
      _steps = List.from(routine.steps);
      _schedule = routine.schedule;
      _tags = List.from(routine.tags);
      _isActive = routine.isActive;
      _isFavorite = routine.isFavorite;
    } else if (widget.template != null) {
      // Creating from template
      final template = widget.template!;
      _nameController.text = template.name;
      _descriptionController.text = template.description ?? '';
      _selectedCategory = template.category;
      _steps = template.steps
          .map(
            (step) => step.copyWith(
              id:
                  DateTime.now().millisecondsSinceEpoch.toString() +
                  step.order.toString(),
              currentRepetitions: 0,
            ),
          )
          .toList();
      _tags = List.from(template.tags);
    } else {
      // Creating new routine - don't add step automatically
      // User can add steps manually using the "Add Step" button
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.routine != null ? 'Edit Routine' : 'Create Routine'),
        actions: [
          TextButton(onPressed: _saveRoutine, child: const Text('Save')),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic information
              _buildBasicInfoSection(),
              const SizedBox(height: 24),

              // Steps section
              _buildStepsSection(),
              const SizedBox(height: 24),

              // Schedule section
              _buildScheduleSection(),
              const SizedBox(height: 24),

              // Tags section
              _buildTagsSection(),
              const SizedBox(height: 24),

              // Settings section
              _buildSettingsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Routine Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a routine name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            DropdownButtonFormField<DhikrCategory>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: DhikrCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category.displayName),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCategory = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Dhikr Steps',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addNewStep,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Step'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_steps.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text(
                    'No steps added yet.\nTap "Add Step" to get started.',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ReorderableListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _steps.length,
                onReorder: _reorderSteps,
                itemBuilder: (context, index) {
                  final step = _steps[index];
                  return _buildStepCard(step, index, key: ValueKey(step.id));
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepCard(RoutineStep step, int index, {required Key key}) {
    return Card(
      key: key,
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(radius: 16, child: Text('${index + 1}')),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    step.arabicText.isNotEmpty
                        ? step.arabicText
                        : 'Step ${index + 1}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    if (value == 'edit') {
                      _editStep(index);
                    } else if (value == 'delete') {
                      _deleteStep(index);
                    }
                  },
                ),
              ],
            ),
            if (step.transliteration.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                step.transliteration,
                style: const TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.blue,
                ),
              ),
            ],
            if (step.translation.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(step.translation, style: TextStyle(color: Colors.grey[600])),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.repeat, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  '${step.targetRepetitions} repetitions',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                if (step.tags.isNotEmpty) ...[
                  const SizedBox(width: 16),
                  Icon(Icons.tag, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    step.tags.join(', '),
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Schedule & Reminders',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: _configureSchedule,
                  child: Text(
                    _schedule != null ? 'Edit Schedule' : 'Add Schedule',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            if (_schedule == null)
              const Text(
                'No schedule configured. Tap "Add Schedule" to set up reminders.',
                style: TextStyle(color: Colors.grey),
              )
            else
              _buildScheduleInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleInfo() {
    if (_schedule == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.schedule, color: Colors.blue[700]),
            const SizedBox(width: 8),
            Text(
              'Type: ${_schedule!.type.name.toUpperCase()}',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ],
        ),
        if (_schedule!.reminderTimes.isNotEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.notifications, color: Colors.orange[700]),
              const SizedBox(width: 8),
              Text(
                'Reminders: ${_schedule!.reminderTimes.join(', ')}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildTagsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tags',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(onPressed: _addTag, child: const Text('Add Tag')),
              ],
            ),
            const SizedBox(height: 8),

            if (_tags.isEmpty)
              const Text(
                'No tags added. Tags help organize and search your routines.',
                style: TextStyle(color: Colors.grey),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _tags
                    .map(
                      (tag) => Chip(
                        label: Text(tag),
                        onDeleted: () => _removeTag(tag),
                      ),
                    )
                    .toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Settings',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Active'),
              subtitle: const Text('Enable this routine for daily use'),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
            ),

            SwitchListTile(
              title: const Text('Favorite'),
              subtitle: const Text('Mark as favorite for quick access'),
              value: _isFavorite,
              onChanged: (value) {
                setState(() {
                  _isFavorite = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  void _addNewStep() {
    _showStepDialog();
  }

  void _editStep(int index) {
    _showStepDialog(step: _steps[index], index: index);
  }

  void _deleteStep(int index) {
    setState(() {
      _steps.removeAt(index);
      // Update order for remaining steps
      for (int i = 0; i < _steps.length; i++) {
        _steps[i] = _steps[i].copyWith(order: i + 1);
      }
    });
  }

  void _reorderSteps(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final step = _steps.removeAt(oldIndex);
      _steps.insert(newIndex, step);

      // Update order for all steps
      for (int i = 0; i < _steps.length; i++) {
        _steps[i] = _steps[i].copyWith(order: i + 1);
      }
    });
  }

  void _showStepDialog({RoutineStep? step, int? index}) {
    showDialog(
      context: context,
      builder: (context) => StepEditorDialog(
        step: step,
        onSave: (newStep) {
          setState(() {
            if (index != null) {
              _steps[index] = newStep;
            } else {
              _steps.add(newStep.copyWith(order: _steps.length + 1));
            }
          });
        },
      ),
    );
  }

  void _configureSchedule() {
    showDialog(
      context: context,
      builder: (context) => ScheduleConfigDialog(
        schedule: _schedule,
        onSave: (schedule) {
          setState(() {
            _schedule = schedule;
          });
        },
      ),
    );
  }

  void _addTag() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('Add Tag'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: 'Tag name',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final tag = controller.text.trim();
                if (tag.isNotEmpty && !_tags.contains(tag)) {
                  setState(() {
                    _tags.add(tag);
                  });
                }
                Navigator.of(context).pop();
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  void _saveRoutine() {
    if (_formKey.currentState!.validate() && _steps.isNotEmpty) {
      final totalRepetitions = _steps.fold<int>(
        0,
        (sum, step) => sum + step.targetRepetitions,
      );

      final routine = RoutineModel(
        id:
            widget.routine?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        userId: 'demo-user-id',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isNotEmpty
            ? _descriptionController.text.trim()
            : null,
        type: widget.template != null ? RoutineType.custom : RoutineType.custom,
        category: _selectedCategory,
        steps: _steps,
        schedule: _schedule,
        totalRepetitions: totalRepetitions,
        currentRepetitions: widget.routine?.currentRepetitions ?? 0,
        isActive: _isActive,
        isFavorite: _isFavorite,
        tags: _tags,
        templateId: widget.template?.id,
        createdAt: widget.routine?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        lastCompletedAt: widget.routine?.lastCompletedAt,
      );

      if (widget.routine != null) {
        ref.read(routinesProvider.notifier).updateRoutine(routine);
      } else {
        ref.read(routinesProvider.notifier).addRoutine(routine);
      }

      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            widget.routine != null ? 'Routine updated!' : 'Routine created!',
          ),
        ),
      );
    } else if (_steps.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one dhikr step')),
      );
    }
  }
}

class StepEditorDialog extends StatefulWidget {
  final RoutineStep? step;
  final Function(RoutineStep) onSave;

  const StepEditorDialog({super.key, this.step, required this.onSave});

  @override
  State<StepEditorDialog> createState() => _StepEditorDialogState();
}

class _StepEditorDialogState extends State<StepEditorDialog> {
  final _formKey = GlobalKey<FormState>();
  final _arabicController = TextEditingController();
  final _transliterationController = TextEditingController();
  final _translationController = TextEditingController();
  final _repetitionsController = TextEditingController();
  final _sourceController = TextEditingController();
  final _tagsController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.step != null) {
      final step = widget.step!;
      _arabicController.text = step.arabicText;
      _transliterationController.text = step.transliteration;
      _translationController.text = step.translation;
      _repetitionsController.text = step.targetRepetitions.toString();
      _sourceController.text = step.source ?? '';
      _tagsController.text = step.tags.join(', ');
    } else {
      _repetitionsController.text = '33';
    }
  }

  @override
  void dispose() {
    _arabicController.dispose();
    _transliterationController.dispose();
    _translationController.dispose();
    _repetitionsController.dispose();
    _sourceController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.step != null ? 'Edit Step' : 'Add Step'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Arabic text field with RTL support
                TextFormField(
                  controller: _arabicController,
                  decoration: const InputDecoration(
                    labelText: 'Arabic Text *',
                    border: OutlineInputBorder(),
                    hintText: 'سُبْحَانَ اللَّهِ',
                  ),
                  textDirection: TextDirection.rtl,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter Arabic text';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Transliteration field
                TextFormField(
                  controller: _transliterationController,
                  decoration: const InputDecoration(
                    labelText: 'Transliteration *',
                    border: OutlineInputBorder(),
                    hintText: 'Subhan Allah',
                  ),
                  style: const TextStyle(fontStyle: FontStyle.italic),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter transliteration';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Translation field
                TextFormField(
                  controller: _translationController,
                  decoration: const InputDecoration(
                    labelText: 'Translation *',
                    border: OutlineInputBorder(),
                    hintText: 'Glory be to Allah',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter translation';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Repetitions field
                TextFormField(
                  controller: _repetitionsController,
                  decoration: const InputDecoration(
                    labelText: 'Repetitions *',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.repeat),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter number of repetitions';
                    }
                    final number = int.tryParse(value.trim());
                    if (number == null || number <= 0) {
                      return 'Please enter a valid positive number';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Source field (optional)
                TextFormField(
                  controller: _sourceController,
                  decoration: const InputDecoration(
                    labelText: 'Source (Optional)',
                    border: OutlineInputBorder(),
                    hintText: 'Quran, Hadith, etc.',
                  ),
                ),
                const SizedBox(height: 16),

                // Tags field (optional)
                TextFormField(
                  controller: _tagsController,
                  decoration: const InputDecoration(
                    labelText: 'Tags (Optional)',
                    border: OutlineInputBorder(),
                    hintText: 'morning, tasbih, protection',
                    helperText: 'Separate tags with commas',
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(onPressed: _saveStep, child: const Text('Save')),
      ],
    );
  }

  void _saveStep() {
    if (_formKey.currentState!.validate()) {
      final tags = _tagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      final step = RoutineStep(
        id: widget.step?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
        arabicText: _arabicController.text.trim(),
        transliteration: _transliterationController.text.trim(),
        translation: _translationController.text.trim(),
        targetRepetitions: int.parse(_repetitionsController.text.trim()),
        currentRepetitions: widget.step?.currentRepetitions ?? 0,
        source: _sourceController.text.trim().isNotEmpty
            ? _sourceController.text.trim()
            : null,
        tags: tags,
        order: widget.step?.order ?? 1,
      );

      widget.onSave(step);
      Navigator.of(context).pop();
    }
  }
}

class ScheduleConfigDialog extends StatefulWidget {
  final RoutineSchedule? schedule;
  final Function(RoutineSchedule) onSave;

  const ScheduleConfigDialog({super.key, this.schedule, required this.onSave});

  @override
  State<ScheduleConfigDialog> createState() => _ScheduleConfigDialogState();
}

class _ScheduleConfigDialogState extends State<ScheduleConfigDialog> {
  RoutineScheduleType _scheduleType = RoutineScheduleType.daily;
  List<int> _selectedWeekdays = [];
  List<String> _reminderTimes = [];
  bool _isActive = true;

  final List<String> _weekdayNames = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.schedule != null) {
      final schedule = widget.schedule!;
      _scheduleType = schedule.type;
      _selectedWeekdays = List.from(schedule.weekdays);
      _reminderTimes = List.from(schedule.reminderTimes);
      _isActive = schedule.isActive;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Configure Schedule'),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Schedule type
              Text(
                'Schedule Type',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<RoutineScheduleType>(
                value: _scheduleType,
                decoration: const InputDecoration(border: OutlineInputBorder()),
                items: RoutineScheduleType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.name.toUpperCase()),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _scheduleType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // Weekdays selection (for weekly schedule)
              if (_scheduleType == RoutineScheduleType.weekly) ...[
                Text(
                  'Select Days',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: List.generate(7, (index) {
                    final dayIndex = index + 1;
                    final isSelected = _selectedWeekdays.contains(dayIndex);
                    return FilterChip(
                      label: Text(_weekdayNames[index]),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _selectedWeekdays.add(dayIndex);
                          } else {
                            _selectedWeekdays.remove(dayIndex);
                          }
                          _selectedWeekdays.sort();
                        });
                      },
                    );
                  }),
                ),
                const SizedBox(height: 16),
              ],

              // Reminder times
              Text(
                'Reminder Times',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              if (_reminderTimes.isEmpty)
                const Text(
                  'No reminders set',
                  style: TextStyle(color: Colors.grey),
                )
              else
                Wrap(
                  spacing: 8,
                  children: _reminderTimes
                      .map(
                        (time) => Chip(
                          label: Text(time),
                          onDeleted: () => _removeReminderTime(time),
                        ),
                      )
                      .toList(),
                ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _addReminderTime,
                icon: const Icon(Icons.add),
                label: const Text('Add Reminder'),
              ),
              const SizedBox(height: 16),

              // Active toggle
              SwitchListTile(
                title: const Text('Active'),
                subtitle: const Text('Enable schedule notifications'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(onPressed: _saveSchedule, child: const Text('Save')),
      ],
    );
  }

  void _addReminderTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (time != null) {
      final timeString =
          '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
      if (!_reminderTimes.contains(timeString)) {
        setState(() {
          _reminderTimes.add(timeString);
          _reminderTimes.sort();
        });
      }
    }
  }

  void _removeReminderTime(String time) {
    setState(() {
      _reminderTimes.remove(time);
    });
  }

  void _saveSchedule() {
    final schedule = RoutineSchedule(
      type: _scheduleType,
      weekdays: _scheduleType == RoutineScheduleType.weekly
          ? _selectedWeekdays
          : [],
      reminderTimes: _reminderTimes,
      isActive: _isActive,
    );

    widget.onSave(schedule);
    Navigator.of(context).pop();
  }
}
