import 'dart:math';
import '../models/prayer_time_model.dart';

class PrayerTimeService {
  static const double _defaultLatitude = 21.3891; // Mecca latitude
  static const double _defaultLongitude = 39.8579; // Mecca longitude

  // Calculate prayer times for a given date and location
  static List<PrayerTimeModel> calculatePrayerTimes({
    required DateTime date,
    double latitude = _defaultLatitude,
    double longitude = _defaultLongitude,
    String? location,
  }) {
    final julianDate = _getJulianDate(date);
    final timeZone = _getTimeZone(longitude);
    
    // Calculate sun declination and equation of time
    final sunDeclination = _getSunDeclination(julianDate);
    final equationOfTime = _getEquationOfTime(julianDate);
    
    // Calculate prayer times
    final fajrTime = _calculatePrayerTime(
      julianDate, latitude, longitude, -18.0, timeZone, equationOfTime, sunDeclination
    );
    
    final dhuhrTime = _calculateDhuhrTime(
      julianDate, longitude, timeZone, equationOfTime
    );
    
    final asrTime = _calculateAsrTime(
      julianDate, latitude, longitude, timeZone, equationOfTime, sunDeclination
    );
    
    final maghribTime = _calculatePrayerTime(
      julianDate, latitude, longitude, -0.833, timeZone, equationOfTime, sunDeclination
    );
    
    final ishaTime = _calculatePrayerTime(
      julianDate, latitude, longitude, -17.0, timeZone, equationOfTime, sunDeclination
    );

    return [
      PrayerTimeModel(
        id: 'fajr_${date.millisecondsSinceEpoch}',
        name: PrayerName.fajr.displayName,
        time: _timeToDateTime(date, fajrTime),
        date: date,
        location: location,
        latitude: latitude,
        longitude: longitude,
      ),
      PrayerTimeModel(
        id: 'dhuhr_${date.millisecondsSinceEpoch}',
        name: PrayerName.dhuhr.displayName,
        time: _timeToDateTime(date, dhuhrTime),
        date: date,
        location: location,
        latitude: latitude,
        longitude: longitude,
      ),
      PrayerTimeModel(
        id: 'asr_${date.millisecondsSinceEpoch}',
        name: PrayerName.asr.displayName,
        time: _timeToDateTime(date, asrTime),
        date: date,
        location: location,
        latitude: latitude,
        longitude: longitude,
      ),
      PrayerTimeModel(
        id: 'maghrib_${date.millisecondsSinceEpoch}',
        name: PrayerName.maghrib.displayName,
        time: _timeToDateTime(date, maghribTime),
        date: date,
        location: location,
        latitude: latitude,
        longitude: longitude,
      ),
      PrayerTimeModel(
        id: 'isha_${date.millisecondsSinceEpoch}',
        name: PrayerName.isha.displayName,
        time: _timeToDateTime(date, ishaTime),
        date: date,
        location: location,
        latitude: latitude,
        longitude: longitude,
      ),
    ];
  }

  static double _getJulianDate(DateTime date) {
    final a = (14 - date.month) ~/ 12;
    final y = date.year - a;
    final m = date.month + 12 * a - 3;
    
    return date.day + (153 * m + 2) ~/ 5 + 365 * y + y ~/ 4 - y ~/ 100 + y ~/ 400 - 32045;
  }

  static double _getTimeZone(double longitude) {
    return longitude / 15.0;
  }

  static double _getSunDeclination(double julianDate) {
    final n = julianDate - 2451545.0;
    final l = (280.460 + 0.9856474 * n) % 360;
    final g = _degreesToRadians((357.528 + 0.9856003 * n) % 360);
    final lambda = _degreesToRadians(l + 1.915 * sin(g) + 0.020 * sin(2 * g));
    
    return _radiansToDegrees(asin(sin(_degreesToRadians(23.439)) * sin(lambda)));
  }

  static double _getEquationOfTime(double julianDate) {
    final n = julianDate - 2451545.0;
    final l = _degreesToRadians((280.460 + 0.9856474 * n) % 360);
    final g = _degreesToRadians((357.528 + 0.9856003 * n) % 360);
    final lambda = l + _degreesToRadians(1.915 * sin(g) + 0.020 * sin(2 * g));
    
    final alpha = atan2(cos(_degreesToRadians(23.439)) * sin(lambda), cos(lambda));
    final eot = l - alpha;
    
    return _radiansToDegrees(eot) * 4; // Convert to minutes
  }

  static double _calculatePrayerTime(
    double julianDate,
    double latitude,
    double longitude,
    double angle,
    double timeZone,
    double equationOfTime,
    double sunDeclination,
  ) {
    final latRad = _degreesToRadians(latitude);
    final decRad = _degreesToRadians(sunDeclination);
    final angleRad = _degreesToRadians(angle);
    
    final hourAngle = acos(
      (sin(angleRad) - sin(latRad) * sin(decRad)) / (cos(latRad) * cos(decRad))
    );
    
    final time = 12 - _radiansToDegrees(hourAngle) / 15.0 - equationOfTime / 60.0 + timeZone;
    
    return time;
  }

  static double _calculateDhuhrTime(
    double julianDate,
    double longitude,
    double timeZone,
    double equationOfTime,
  ) {
    return 12 - equationOfTime / 60.0 + timeZone;
  }

  static double _calculateAsrTime(
    double julianDate,
    double latitude,
    double longitude,
    double timeZone,
    double equationOfTime,
    double sunDeclination,
  ) {
    final latRad = _degreesToRadians(latitude);
    final decRad = _degreesToRadians(sunDeclination);
    
    // Hanafi method: shadow length = 2 * object length + shadow at Dhuhr
    // Shafi method: shadow length = object length + shadow at Dhuhr
    final shadowRatio = 1.0; // Using Shafi method
    
    final angle = atan(1.0 / (shadowRatio + tan(latRad - decRad)));
    final hourAngle = acos(
      (sin(angle) - sin(latRad) * sin(decRad)) / (cos(latRad) * cos(decRad))
    );
    
    final time = 12 + _radiansToDegrees(hourAngle) / 15.0 - equationOfTime / 60.0 + timeZone;
    
    return time;
  }

  static DateTime _timeToDateTime(DateTime date, double time) {
    final hours = time.floor();
    final minutes = ((time - hours) * 60).round();
    
    return DateTime(date.year, date.month, date.day, hours, minutes);
  }

  static double _degreesToRadians(double degrees) {
    return degrees * pi / 180.0;
  }

  static double _radiansToDegrees(double radians) {
    return radians * 180.0 / pi;
  }

  // Get next prayer time
  static PrayerTimeModel? getNextPrayer(List<PrayerTimeModel> prayerTimes) {
    final now = DateTime.now();
    
    for (final prayer in prayerTimes) {
      if (prayer.time.isAfter(now) && !prayer.isCompleted) {
        return prayer;
      }
    }
    
    return null; // All prayers for today are completed
  }

  // Get current prayer time (the one that just passed)
  static PrayerTimeModel? getCurrentPrayer(List<PrayerTimeModel> prayerTimes) {
    final now = DateTime.now();
    PrayerTimeModel? currentPrayer;
    
    for (final prayer in prayerTimes) {
      if (prayer.time.isBefore(now)) {
        currentPrayer = prayer;
      } else {
        break;
      }
    }
    
    return currentPrayer;
  }

  // Calculate time remaining until next prayer
  static Duration? getTimeUntilNextPrayer(List<PrayerTimeModel> prayerTimes) {
    final nextPrayer = getNextPrayer(prayerTimes);
    if (nextPrayer == null) return null;
    
    return nextPrayer.time.difference(DateTime.now());
  }

  // Check if it's time for prayer (within 5 minutes)
  static bool isPrayerTime(PrayerTimeModel prayer) {
    final now = DateTime.now();
    final timeDifference = prayer.time.difference(now).abs();
    
    return timeDifference.inMinutes <= 5;
  }

  // Get prayer times for multiple days
  static List<PrayerTimeModel> getPrayerTimesForRange({
    required DateTime startDate,
    required DateTime endDate,
    double latitude = _defaultLatitude,
    double longitude = _defaultLongitude,
    String? location,
  }) {
    final List<PrayerTimeModel> allPrayerTimes = [];
    
    for (DateTime date = startDate; 
         date.isBefore(endDate) || date.isAtSameMomentAs(endDate); 
         date = date.add(const Duration(days: 1))) {
      
      final dayPrayerTimes = calculatePrayerTimes(
        date: date,
        latitude: latitude,
        longitude: longitude,
        location: location,
      );
      
      allPrayerTimes.addAll(dayPrayerTimes);
    }
    
    return allPrayerTimes;
  }
}
