import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/prayer_time_model.dart';
import '../services/prayer_time_service.dart';

// Prayer times provider for today
final prayerTimesProvider =
    StateNotifierProvider<
      PrayerTimesNotifier,
      AsyncValue<List<PrayerTimeModel>>
    >((ref) {
      return PrayerTimesNotifier();
    });

// Alias for backward compatibility
final todayPrayerTimesProvider = prayerTimesProvider;

// Next prayer provider
final nextPrayerProvider = Provider<PrayerTimeModel?>((ref) {
  final prayerTimesAsync = ref.watch(todayPrayerTimesProvider);

  return prayerTimesAsync.when(
    data: (prayerTimes) => PrayerTimeService.getNextPrayer(prayerTimes),
    loading: () => null,
    error: (_, __) => null,
  );
});

// Current prayer provider
final currentPrayerProvider = Provider<PrayerTimeModel?>((ref) {
  final prayerTimesAsync = ref.watch(todayPrayerTimesProvider);

  return prayerTimesAsync.when(
    data: (prayerTimes) => PrayerTimeService.getCurrentPrayer(prayerTimes),
    loading: () => null,
    error: (_, __) => null,
  );
});

// Time until next prayer provider
final timeUntilNextPrayerProvider = Provider<Duration?>((ref) {
  final prayerTimesAsync = ref.watch(todayPrayerTimesProvider);

  return prayerTimesAsync.when(
    data: (prayerTimes) =>
        PrayerTimeService.getTimeUntilNextPrayer(prayerTimes),
    loading: () => null,
    error: (_, __) => null,
  );
});

// Real-time countdown stream provider
final prayerCountdownStreamProvider = StreamProvider<String>((ref) {
  return Stream.periodic(const Duration(seconds: 1), (_) {
    final nextPrayer = ref.read(nextPrayerProvider);
    if (nextPrayer == null) return 'No upcoming prayer';

    final now = DateTime.now();
    final difference = nextPrayer.time.difference(now);

    if (difference.isNegative || difference == Duration.zero) {
      return 'Prayer time now!';
    }

    final hours = difference.inHours;
    final minutes = difference.inMinutes % 60;
    final seconds = difference.inSeconds % 60;

    if (hours > 0) {
      return '${nextPrayer.name} in ${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${nextPrayer.name} in ${minutes}m ${seconds}s';
    } else {
      return '${nextPrayer.name} in ${seconds}s';
    }
  });
});

// Prayer completion status provider
final prayerCompletionProvider =
    StateNotifierProvider<PrayerCompletionNotifier, Map<String, bool>>((ref) {
      return PrayerCompletionNotifier();
    });

class PrayerTimesNotifier
    extends StateNotifier<AsyncValue<List<PrayerTimeModel>>> {
  PrayerTimesNotifier() : super(const AsyncValue.loading()) {
    loadTodayPrayerTimes();
  }

  Future<void> loadTodayPrayerTimes({
    double? latitude,
    double? longitude,
    String? location,
  }) async {
    try {
      state = const AsyncValue.loading();

      final today = DateTime.now();
      final prayerTimes = PrayerTimeService.calculatePrayerTimes(
        date: today,
        latitude: latitude ?? 21.3891, // Default to Mecca
        longitude: longitude ?? 39.8579,
        location: location ?? 'Mecca, Saudi Arabia',
      );

      state = AsyncValue.data(prayerTimes);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadPrayerTimesForDate(
    DateTime date, {
    double? latitude,
    double? longitude,
    String? location,
  }) async {
    try {
      state = const AsyncValue.loading();

      final prayerTimes = PrayerTimeService.calculatePrayerTimes(
        date: date,
        latitude: latitude ?? 21.3891,
        longitude: longitude ?? 39.8579,
        location: location ?? 'Mecca, Saudi Arabia',
      );

      state = AsyncValue.data(prayerTimes);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void updateLocation({
    required double latitude,
    required double longitude,
    String? location,
  }) {
    loadTodayPrayerTimes(
      latitude: latitude,
      longitude: longitude,
      location: location,
    );
  }
}

class PrayerCompletionNotifier extends StateNotifier<Map<String, bool>> {
  PrayerCompletionNotifier() : super({});

  void markPrayerCompleted(String prayerId) {
    state = {...state, prayerId: true};
    _savePrayerCompletion();
  }

  void markPrayerIncomplete(String prayerId) {
    final newState = Map<String, bool>.from(state);
    newState.remove(prayerId);
    state = newState;
    _savePrayerCompletion();
  }

  bool isPrayerCompleted(String prayerId) {
    return state[prayerId] ?? false;
  }

  void resetDailyCompletion() {
    state = {};
    _savePrayerCompletion();
  }

  int getCompletedPrayersCount() {
    return state.values.where((completed) => completed).length;
  }

  double getCompletionPercentage() {
    const totalPrayers = 5; // Fajr, Dhuhr, Asr, Maghrib, Isha
    final completedCount = getCompletedPrayersCount();
    return completedCount / totalPrayers;
  }

  void _savePrayerCompletion() {
    // In a real app, save to local storage or database
    // For now, just keep in memory
  }

  void _loadPrayerCompletion() {
    // In a real app, load from local storage or database
    // For now, start with empty state
  }
}

// Prayer streak provider
final prayerStreakProvider = StateNotifierProvider<PrayerStreakNotifier, int>((
  ref,
) {
  return PrayerStreakNotifier();
});

class PrayerStreakNotifier extends StateNotifier<int> {
  PrayerStreakNotifier() : super(0) {
    _loadStreak();
  }

  void incrementStreak() {
    state = state + 1;
    _saveStreak();
  }

  void resetStreak() {
    state = 0;
    _saveStreak();
  }

  void updateStreakBasedOnCompletion(double completionPercentage) {
    // Consider a day successful if at least 80% of prayers are completed
    if (completionPercentage >= 0.8) {
      incrementStreak();
    } else {
      resetStreak();
    }
  }

  void _saveStreak() {
    // In a real app, save to local storage or database
  }

  void _loadStreak() {
    // In a real app, load from local storage or database
  }
}

// Prayer statistics provider
final prayerStatsProvider = Provider<PrayerStatistics>((ref) {
  final completionMap = ref.watch(prayerCompletionProvider);
  final streak = ref.watch(prayerStreakProvider);

  return PrayerStatistics(
    completedToday: completionMap.values.where((completed) => completed).length,
    totalToday: 5,
    completionPercentage:
        completionMap.values.where((completed) => completed).length / 5,
    currentStreak: streak,
  );
});

class PrayerStatistics {
  final int completedToday;
  final int totalToday;
  final double completionPercentage;
  final int currentStreak;

  const PrayerStatistics({
    required this.completedToday,
    required this.totalToday,
    required this.completionPercentage,
    required this.currentStreak,
  });

  int get remainingToday => totalToday - completedToday;
  bool get isAllCompleted => completedToday == totalToday;
}
