import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/memo_model.dart';
import '../../providers/memo_provider.dart';
import '../../services/audio_service.dart';
import '../create_memo_screen.dart';

class DashboardTab extends ConsumerWidget {
  final AudioService audioService;

  const DashboardTab({super.key, required this.audioService});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final memosAsync = ref.watch(memosProvider);

    return memosAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(memosProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
      data: (memos) => SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.dashboard, size: 32, color: Colors.deepPurple),
                        const SizedBox(width: 12),
                        Text(
                          'Dashboard',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Overview of your memo collection',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Statistics cards
            _buildStatsSection(context, memos),
            const SizedBox(height: 20),

            // Quick actions
            _buildQuickActionsSection(context, ref),
            const SizedBox(height: 20),

            // Recent memos
            _buildRecentMemosSection(context, ref, memos),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection(BuildContext context, List<MemoModel> memos) {
    final textMemos = memos.where((m) => m.type == MemoType.text).length;
    final voiceMemos = memos.where((m) => m.type == MemoType.voice).length;
    final todoMemos = memos.where((m) => m.type == MemoType.todo).length;
    final totalTodos = memos.expand((m) => m.todoItems).length;
    final completedTodos = memos.expand((m) => m.todoItems).where((t) => t.isCompleted).length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Statistics',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                'Total Memos',
                memos.length.toString(),
                Icons.note,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                context,
                'Text Notes',
                textMemos.toString(),
                Icons.text_snippet,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                'Voice Memos',
                voiceMemos.toString(),
                Icons.mic,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                context,
                'Todo Lists',
                todoMemos.toString(),
                Icons.checklist,
                Colors.purple,
              ),
            ),
          ],
        ),
        if (totalTodos > 0) ...[
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Total Tasks',
                  totalTodos.toString(),
                  Icons.task,
                  Colors.indigo,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Completed',
                  '$completedTodos/$totalTodos',
                  Icons.check_circle,
                  Colors.teal,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                context,
                'New Note',
                'Create a text note',
                Icons.note_add,
                Colors.blue,
                () => _createMemo(context, MemoType.text),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                context,
                'New Todo',
                'Create a task list',
                Icons.add_task,
                Colors.purple,
                () => _createMemo(context, MemoType.todo),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                context,
                'Voice Memo',
                'Record audio note',
                Icons.mic,
                Colors.orange,
                () => _createMemo(context, MemoType.voice),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                context,
                'Mixed Note',
                'Rich content note',
                Icons.auto_awesome,
                Colors.green,
                () => _createMemo(context, MemoType.mixed),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentMemosSection(BuildContext context, WidgetRef ref, List<MemoModel> memos) {
    final recentMemos = memos.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Memos',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            if (memos.length > 5)
              TextButton(
                onPressed: () {
                  // TODO: Navigate to all memos view
                },
                child: const Text('View All'),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (recentMemos.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                children: [
                  const Icon(Icons.note, size: 48, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    'No memos yet',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Create your first memo using the quick actions above',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          )
        else
          ...recentMemos.map((memo) => Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: _getMemoTypeColor(memo.type).withValues(alpha: 0.1),
                child: Icon(
                  _getMemoTypeIcon(memo.type),
                  color: _getMemoTypeColor(memo.type),
                ),
              ),
              title: Text(memo.title),
              subtitle: Text(
                memo.description ?? 'No description',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              trailing: Text(
                _formatDate(memo.createdAt),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              onTap: () => _editMemo(context, memo),
            ),
          )),
      ],
    );
  }

  void _createMemo(BuildContext context, MemoType type) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateMemoScreen(initialType: type),
      ),
    );
  }

  void _editMemo(BuildContext context, MemoModel memo) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateMemoScreen(editingMemo: memo),
      ),
    );
  }

  IconData _getMemoTypeIcon(MemoType type) {
    switch (type) {
      case MemoType.text:
        return Icons.text_snippet;
      case MemoType.voice:
        return Icons.mic;
      case MemoType.todo:
        return Icons.checklist;
      case MemoType.mixed:
        return Icons.auto_awesome;
    }
  }

  Color _getMemoTypeColor(MemoType type) {
    switch (type) {
      case MemoType.text:
        return Colors.blue;
      case MemoType.voice:
        return Colors.orange;
      case MemoType.todo:
        return Colors.purple;
      case MemoType.mixed:
        return Colors.green;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
