// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FileAttachmentModel _$FileAttachmentModelFromJson(Map<String, dynamic> json) =>
    FileAttachmentModel(
      id: json['id'] as String,
      name: json['name'] as String,
      path: json['path'] as String,
      url: json['url'] as String?,
      mimeType: json['mimeType'] as String,
      size: (json['size'] as num).toInt(),
      uploadedAt: DateTime.parse(json['uploadedAt'] as String),
    );

Map<String, dynamic> _$FileAttachmentModelToJson(
  FileAttachmentModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'path': instance.path,
  'url': instance.url,
  'mimeType': instance.mimeType,
  'size': instance.size,
  'uploadedAt': instance.uploadedAt.toIso8601String(),
};
