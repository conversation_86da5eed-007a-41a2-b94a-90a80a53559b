import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/routine_model.dart';
import '../providers/routine_provider.dart';

class RoutineSessionScreen extends ConsumerStatefulWidget {
  final RoutineModel routine;

  const RoutineSessionScreen({
    super.key,
    required this.routine,
  });

  @override
  ConsumerState<RoutineSessionScreen> createState() => _RoutineSessionScreenState();
}

class _RoutineSessionScreenState extends ConsumerState<RoutineSessionScreen> {
  int currentStepIndex = 0;
  Map<String, int> stepProgress = {};
  DateTime? sessionStartTime;

  @override
  void initState() {
    super.initState();
    sessionStartTime = DateTime.now();
    // Initialize progress for all steps
    for (final step in widget.routine.steps) {
      stepProgress[step.id] = step.currentRepetitions;
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentStep = widget.routine.steps[currentStepIndex];
    final currentCount = stepProgress[currentStep.id] ?? 0;
    final isCompleted = currentCount >= currentStep.targetRepetitions;
    final allStepsCompleted = _areAllStepsCompleted();

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.routine.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetSession,
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => _endSession(context),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(),
            const SizedBox(height: 24),

            // Current step card
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Step counter
                      Text(
                        'Step ${currentStepIndex + 1} of ${widget.routine.steps.length}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Arabic text
                      Text(
                        currentStep.arabicText,
                        style: const TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 16),

                      // Transliteration
                      if (currentStep.transliteration != null)
                        Text(
                          currentStep.transliteration!,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontStyle: FontStyle.italic,
                            color: Colors.grey[700],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      const SizedBox(height: 12),

                      // Translation
                      if (currentStep.translation != null)
                        Text(
                          currentStep.translation!,
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.grey[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      const SizedBox(height: 32),

                      // Counter
                      _buildCounter(currentStep, currentCount),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Navigation buttons
            _buildNavigationButtons(isCompleted, allStepsCompleted),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final totalSteps = widget.routine.steps.length;
    final completedSteps = _getCompletedStepsCount();

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              '$completedSteps / $totalSteps steps',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: completedSteps / totalSteps,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildCounter(RoutineStep step, int currentCount) {
    return Column(
      children: [
        // Count display
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: Theme.of(context).colorScheme.primary,
              width: 3,
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '$currentCount',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                Text(
                  '/ ${step.targetRepetitions}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),

        // Increment button
        ElevatedButton(
          onPressed: currentCount < step.targetRepetitions
              ? () => _incrementCount(step.id)
              : null,
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(200, 60),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: Text(
            currentCount < step.targetRepetitions ? 'Count' : 'Completed',
            style: const TextStyle(fontSize: 18),
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationButtons(bool isCompleted, bool allStepsCompleted) {
    return Row(
      children: [
        // Previous button
        if (currentStepIndex > 0)
          Expanded(
            child: OutlinedButton(
              onPressed: _previousStep,
              child: const Text('Previous'),
            ),
          ),
        if (currentStepIndex > 0) const SizedBox(width: 16),

        // Next/Complete button
        Expanded(
          child: ElevatedButton(
            onPressed: allStepsCompleted
                ? () => _completeSession(context)
                : (isCompleted ? _nextStep : null),
            child: Text(
              allStepsCompleted
                  ? 'Complete Session'
                  : (isCompleted ? 'Next Step' : 'Complete this step first'),
            ),
          ),
        ),
      ],
    );
  }

  void _incrementCount(String stepId) {
    setState(() {
      stepProgress[stepId] = (stepProgress[stepId] ?? 0) + 1;
    });

    // Update the routine provider
    ref.read(routinesProvider.notifier).incrementStepCount(
          widget.routine.id,
          stepId,
        );
  }

  void _nextStep() {
    if (currentStepIndex < widget.routine.steps.length - 1) {
      setState(() {
        currentStepIndex++;
      });
    }
  }

  void _previousStep() {
    if (currentStepIndex > 0) {
      setState(() {
        currentStepIndex--;
      });
    }
  }

  void _resetSession() {
    setState(() {
      currentStepIndex = 0;
      for (final step in widget.routine.steps) {
        stepProgress[step.id] = 0;
      }
      sessionStartTime = DateTime.now();
    });
  }

  void _endSession(BuildContext context) {
    ref.read(activeRoutineSessionProvider.notifier).endSession();
    Navigator.of(context).pop();
  }

  void _completeSession(BuildContext context) {
    // Mark routine as completed
    ref.read(routinesProvider.notifier).completeRoutine(widget.routine.id);
    
    // End the session
    ref.read(activeRoutineSessionProvider.notifier).endSession();

    // Show completion dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Session Complete!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'Congratulations! You have completed ${widget.routine.name}.',
              textAlign: TextAlign.center,
            ),
            if (sessionStartTime != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Session duration: ${_getSessionDuration()}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Close session screen
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  bool _areAllStepsCompleted() {
    for (final step in widget.routine.steps) {
      final currentCount = stepProgress[step.id] ?? 0;
      if (currentCount < step.targetRepetitions) {
        return false;
      }
    }
    return true;
  }

  int _getCompletedStepsCount() {
    int completed = 0;
    for (final step in widget.routine.steps) {
      final currentCount = stepProgress[step.id] ?? 0;
      if (currentCount >= step.targetRepetitions) {
        completed++;
      }
    }
    return completed;
  }

  String _getSessionDuration() {
    if (sessionStartTime == null) return '';
    final duration = DateTime.now().difference(sessionStartTime!);
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }
}
