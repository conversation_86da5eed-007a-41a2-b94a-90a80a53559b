import 'dart:ui';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

// Supported locales
const List<Locale> supportedLocales = [
  Locale('en', 'US'), // English
  Locale('ar', 'SA'), // Arabic
];

// Current locale provider
final localeProvider = StateNotifierProvider<LocaleNotifier, Locale>((ref) {
  return LocaleNotifier();
});

// RTL provider
final isRTLProvider = Provider<bool>((ref) {
  final locale = ref.watch(localeProvider);
  return locale.languageCode == 'ar';
});

class LocaleNotifier extends StateNotifier<Locale> {
  LocaleNotifier() : super(const Locale('en', 'US')) {
    _loadLocale();
  }

  Future<void> _loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString(AppConstants.keyLanguage);
    
    if (languageCode != null) {
      final locale = supportedLocales.firstWhere(
        (locale) => locale.languageCode == languageCode,
        orElse: () => const Locale('en', 'US'),
      );
      state = locale;
    } else {
      // Use system locale if available
      final systemLocale = PlatformDispatcher.instance.locale;
      final supportedLocale = supportedLocales.firstWhere(
        (locale) => locale.languageCode == systemLocale.languageCode,
        orElse: () => const Locale('en', 'US'),
      );
      state = supportedLocale;
    }
  }

  Future<void> setLocale(Locale locale) async {
    if (supportedLocales.contains(locale)) {
      state = locale;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.keyLanguage, locale.languageCode);
    }
  }

  Future<void> setLanguage(String languageCode) async {
    final locale = supportedLocales.firstWhere(
      (locale) => locale.languageCode == languageCode,
      orElse: () => const Locale('en', 'US'),
    );
    await setLocale(locale);
  }
}

// Localization strings
class AppLocalizations {
  static const Map<String, Map<String, String>> _localizedValues = {
    'en': {
      // App
      'app_name': 'ShadowSuite',
      'dashboard': 'Dashboard',
      'memo_suite': 'Memo Suite',
      'athkar_pro': 'Athkar Pro',
      'money_flow': 'Money Flow',
      'profile': 'Profile',
      'settings': 'Settings',
      
      // Authentication
      'sign_in': 'Sign In',
      'sign_up': 'Sign Up',
      'sign_out': 'Sign Out',
      'email': 'Email',
      'password': 'Password',
      'confirm_password': 'Confirm Password',
      'display_name': 'Display Name',
      'forgot_password': 'Forgot Password?',
      'reset_password': 'Reset Password',
      'create_account': 'Create Account',
      'already_have_account': 'Already have an account?',
      'dont_have_account': "Don't have an account?",
      
      // Common
      'save': 'Save',
      'cancel': 'Cancel',
      'delete': 'Delete',
      'edit': 'Edit',
      'add': 'Add',
      'search': 'Search',
      'filter': 'Filter',
      'sort': 'Sort',
      'loading': 'Loading...',
      'error': 'Error',
      'success': 'Success',
      'confirm': 'Confirm',
      'yes': 'Yes',
      'no': 'No',
      'ok': 'OK',
      'close': 'Close',
      
      // Memo Suite
      'record': 'Record',
      'stop_recording': 'Stop Recording',
      'play': 'Play',
      'pause': 'Pause',
      'transcription': 'Transcription',
      'duration': 'Duration',
      'title': 'Title',
      'description': 'Description',
      'tags': 'Tags',
      
      // Athkar Pro
      'routine': 'Routine',
      'routines': 'Routines',
      'target_count': 'Target Count',
      'current_count': 'Current Count',
      'progress': 'Progress',
      'completed': 'Completed',
      'arabic_text': 'Arabic Text',
      'transliteration': 'Transliteration',
      'translation': 'Translation',
      'repetitions': 'Repetitions',
      
      // Money Flow
      'accounts': 'Accounts',
      'categories': 'Categories',
      'transactions': 'Transactions',
      'budgets': 'Budgets',
      'reports': 'Reports',
      'income': 'Income',
      'expense': 'Expense',
      'transfer': 'Transfer',
      'amount': 'Amount',
      'balance': 'Balance',
      'date': 'Date',
      'notes': 'Notes',
      
      // Settings
      'language': 'Language',
      'theme': 'Theme',
      'light': 'Light',
      'dark': 'Dark',
      'system': 'System',
      'notifications': 'Notifications',
      'sync': 'Sync',
      'offline_mode': 'Offline Mode',
      'about': 'About',
    },
    'ar': {
      // App
      'app_name': 'سويت الظل',
      'dashboard': 'لوحة التحكم',
      'memo_suite': 'مجموعة المذكرات',
      'athkar_pro': 'أذكار برو',
      'money_flow': 'تدفق المال',
      'profile': 'الملف الشخصي',
      'settings': 'الإعدادات',
      
      // Authentication
      'sign_in': 'تسجيل الدخول',
      'sign_up': 'إنشاء حساب',
      'sign_out': 'تسجيل الخروج',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'confirm_password': 'تأكيد كلمة المرور',
      'display_name': 'اسم العرض',
      'forgot_password': 'نسيت كلمة المرور؟',
      'reset_password': 'إعادة تعيين كلمة المرور',
      'create_account': 'إنشاء حساب',
      'already_have_account': 'لديك حساب بالفعل؟',
      'dont_have_account': 'ليس لديك حساب؟',
      
      // Common
      'save': 'حفظ',
      'cancel': 'إلغاء',
      'delete': 'حذف',
      'edit': 'تعديل',
      'add': 'إضافة',
      'search': 'بحث',
      'filter': 'تصفية',
      'sort': 'ترتيب',
      'loading': 'جاري التحميل...',
      'error': 'خطأ',
      'success': 'نجح',
      'confirm': 'تأكيد',
      'yes': 'نعم',
      'no': 'لا',
      'ok': 'موافق',
      'close': 'إغلاق',
      
      // Memo Suite
      'record': 'تسجيل',
      'stop_recording': 'إيقاف التسجيل',
      'play': 'تشغيل',
      'pause': 'إيقاف مؤقت',
      'transcription': 'النسخ النصي',
      'duration': 'المدة',
      'title': 'العنوان',
      'description': 'الوصف',
      'tags': 'العلامات',
      
      // Athkar Pro
      'routine': 'روتين',
      'routines': 'الروتينات',
      'target_count': 'العدد المستهدف',
      'current_count': 'العدد الحالي',
      'progress': 'التقدم',
      'completed': 'مكتمل',
      'arabic_text': 'النص العربي',
      'transliteration': 'النقل الصوتي',
      'translation': 'الترجمة',
      'repetitions': 'التكرارات',
      
      // Money Flow
      'accounts': 'الحسابات',
      'categories': 'الفئات',
      'transactions': 'المعاملات',
      'budgets': 'الميزانيات',
      'reports': 'التقارير',
      'income': 'دخل',
      'expense': 'مصروف',
      'transfer': 'تحويل',
      'amount': 'المبلغ',
      'balance': 'الرصيد',
      'date': 'التاريخ',
      'notes': 'ملاحظات',
      
      // Settings
      'language': 'اللغة',
      'theme': 'المظهر',
      'light': 'فاتح',
      'dark': 'داكن',
      'system': 'النظام',
      'notifications': 'الإشعارات',
      'sync': 'المزامنة',
      'offline_mode': 'وضع عدم الاتصال',
      'about': 'حول',
    },
  };

  static String translate(String key, Locale locale) {
    final languageCode = locale.languageCode;
    return _localizedValues[languageCode]?[key] ?? 
           _localizedValues['en']?[key] ?? 
           key;
  }
}

// Translation helper provider
final translationProvider = Provider.family<String, String>((ref, key) {
  final locale = ref.watch(localeProvider);
  return AppLocalizations.translate(key, locale);
});
