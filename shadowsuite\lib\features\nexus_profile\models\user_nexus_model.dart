class UserNexusModel {
  final String id;
  final String authUserId;
  final String? username;
  final String email;
  final String? fullName;
  final String? displayName;
  final String? avatarUrl;
  final String? coverImageUrl;
  final String? bio;
  final String? tagline;
  final String? phone;
  final String? location;
  final String? website;
  final Map<String, dynamic>? socialLinks;
  final DateTime? dateOfBirth;
  final String timezone;
  final String languagePreference;
  final String themePreference;
  final Map<String, dynamic>? notificationSettings;
  final Map<String, dynamic>? privacySettings;
  final Map<String, dynamic>? appPreferences;
  final String subscriptionTier;
  final DateTime? subscriptionExpiresAt;
  final int totalStorageUsed;
  final int storageLimit;
  final bool isVerified;
  final bool isActive;
  final bool isPremium;
  final DateTime? lastSeenAt;
  final DateTime? lastSyncAt;
  final Map<String, dynamic>? deviceInfo;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserNexusModel({
    required this.id,
    required this.authUserId,
    this.username,
    required this.email,
    this.fullName,
    this.displayName,
    this.avatarUrl,
    this.coverImageUrl,
    this.bio,
    this.tagline,
    this.phone,
    this.location,
    this.website,
    this.socialLinks,
    this.dateOfBirth,
    this.timezone = 'UTC',
    this.languagePreference = 'en',
    this.themePreference = 'system',
    this.notificationSettings,
    this.privacySettings,
    this.appPreferences,
    this.subscriptionTier = 'free',
    this.subscriptionExpiresAt,
    this.totalStorageUsed = 0,
    this.storageLimit = 1073741824, // 1GB
    this.isVerified = false,
    this.isActive = true,
    this.isPremium = false,
    this.lastSeenAt,
    this.lastSyncAt,
    this.deviceInfo,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserNexusModel.fromJson(Map<String, dynamic> json) {
    return UserNexusModel(
      id: json['id'] as String,
      authUserId: json['authUserId'] as String,
      username: json['username'] as String?,
      email: json['email'] as String,
      fullName: json['fullName'] as String?,
      displayName: json['displayName'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      coverImageUrl: json['coverImageUrl'] as String?,
      bio: json['bio'] as String?,
      tagline: json['tagline'] as String?,
      phone: json['phone'] as String?,
      location: json['location'] as String?,
      website: json['website'] as String?,
      socialLinks: json['socialLinks'] as Map<String, dynamic>?,
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      timezone: json['timezone'] as String? ?? 'UTC',
      languagePreference: json['languagePreference'] as String? ?? 'en',
      themePreference: json['themePreference'] as String? ?? 'system',
      notificationSettings:
          json['notificationSettings'] as Map<String, dynamic>?,
      privacySettings: json['privacySettings'] as Map<String, dynamic>?,
      appPreferences: json['appPreferences'] as Map<String, dynamic>?,
      subscriptionTier: json['subscriptionTier'] as String? ?? 'free',
      subscriptionExpiresAt: json['subscriptionExpiresAt'] != null
          ? DateTime.parse(json['subscriptionExpiresAt'] as String)
          : null,
      totalStorageUsed: json['totalStorageUsed'] as int? ?? 0,
      storageLimit: json['storageLimit'] as int? ?? 1073741824,
      isVerified: json['isVerified'] as bool? ?? false,
      isActive: json['isActive'] as bool? ?? true,
      isPremium: json['isPremium'] as bool? ?? false,
      lastSeenAt: json['lastSeenAt'] != null
          ? DateTime.parse(json['lastSeenAt'] as String)
          : null,
      lastSyncAt: json['lastSyncAt'] != null
          ? DateTime.parse(json['lastSyncAt'] as String)
          : null,
      deviceInfo: json['deviceInfo'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'authUserId': authUserId,
      'username': username,
      'email': email,
      'fullName': fullName,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'coverImageUrl': coverImageUrl,
      'bio': bio,
      'tagline': tagline,
      'phone': phone,
      'location': location,
      'website': website,
      'socialLinks': socialLinks,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'timezone': timezone,
      'languagePreference': languagePreference,
      'themePreference': themePreference,
      'notificationSettings': notificationSettings,
      'privacySettings': privacySettings,
      'appPreferences': appPreferences,
      'subscriptionTier': subscriptionTier,
      'subscriptionExpiresAt': subscriptionExpiresAt?.toIso8601String(),
      'totalStorageUsed': totalStorageUsed,
      'storageLimit': storageLimit,
      'isVerified': isVerified,
      'isActive': isActive,
      'isPremium': isPremium,
      'lastSeenAt': lastSeenAt?.toIso8601String(),
      'lastSyncAt': lastSyncAt?.toIso8601String(),
      'deviceInfo': deviceInfo,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  UserNexusModel copyWith({
    String? id,
    String? authUserId,
    String? username,
    String? email,
    String? fullName,
    String? displayName,
    String? avatarUrl,
    String? coverImageUrl,
    String? bio,
    String? tagline,
    String? phone,
    String? location,
    String? website,
    Map<String, dynamic>? socialLinks,
    DateTime? dateOfBirth,
    String? timezone,
    String? languagePreference,
    String? themePreference,
    Map<String, dynamic>? notificationSettings,
    Map<String, dynamic>? privacySettings,
    Map<String, dynamic>? appPreferences,
    String? subscriptionTier,
    DateTime? subscriptionExpiresAt,
    int? totalStorageUsed,
    int? storageLimit,
    bool? isVerified,
    bool? isActive,
    bool? isPremium,
    DateTime? lastSeenAt,
    DateTime? lastSyncAt,
    Map<String, dynamic>? deviceInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserNexusModel(
      id: id ?? this.id,
      authUserId: authUserId ?? this.authUserId,
      username: username ?? this.username,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      bio: bio ?? this.bio,
      tagline: tagline ?? this.tagline,
      phone: phone ?? this.phone,
      location: location ?? this.location,
      website: website ?? this.website,
      socialLinks: socialLinks ?? this.socialLinks,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      timezone: timezone ?? this.timezone,
      languagePreference: languagePreference ?? this.languagePreference,
      themePreference: themePreference ?? this.themePreference,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      privacySettings: privacySettings ?? this.privacySettings,
      appPreferences: appPreferences ?? this.appPreferences,
      subscriptionTier: subscriptionTier ?? this.subscriptionTier,
      subscriptionExpiresAt:
          subscriptionExpiresAt ?? this.subscriptionExpiresAt,
      totalStorageUsed: totalStorageUsed ?? this.totalStorageUsed,
      storageLimit: storageLimit ?? this.storageLimit,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      isPremium: isPremium ?? this.isPremium,
      lastSeenAt: lastSeenAt ?? this.lastSeenAt,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  String get displayNameOrEmail =>
      displayName ?? fullName ?? email.split('@').first;

  String get initials {
    if (fullName != null && fullName!.isNotEmpty) {
      final parts = fullName!.trim().split(' ');
      if (parts.length >= 2) {
        return '${parts.first[0]}${parts.last[0]}'.toUpperCase();
      }
      return parts.first[0].toUpperCase();
    }
    return email[0].toUpperCase();
  }

  double get storageUsagePercentage {
    if (storageLimit == 0) return 0;
    return (totalStorageUsed / storageLimit) * 100;
  }

  bool get isStorageNearLimit => storageUsagePercentage > 80;

  String get formattedStorageUsed {
    if (totalStorageUsed < 1024) return '${totalStorageUsed}B';
    if (totalStorageUsed < 1024 * 1024)
      return '${(totalStorageUsed / 1024).toStringAsFixed(1)}KB';
    if (totalStorageUsed < 1024 * 1024 * 1024)
      return '${(totalStorageUsed / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(totalStorageUsed / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String get formattedStorageLimit {
    if (storageLimit < 1024 * 1024 * 1024)
      return '${(storageLimit / (1024 * 1024)).toStringAsFixed(0)}MB';
    return '${(storageLimit / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserNexusModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() =>
      'UserNexusModel(id: $id, email: $email, displayName: $displayNameOrEmail)';
}
