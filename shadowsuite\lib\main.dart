import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/models/memo_model.dart';
import 'features/memo_suite/providers/memo_provider.dart';
import 'features/memo_suite/screens/create_memo_screen.dart';

void main() {
  runApp(const ProviderScope(child: ShadowSuiteApp()));
}

class ShadowSuiteApp extends StatelessWidget {
  const ShadowSuiteApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ShadowSuite',
      debugShowCheckedModeBanner: false,
      theme:
          ThemeData.from(
            colorScheme: ColorScheme.fromSeed(
              seedColor: Colors.deepPurple,
              brightness: Brightness.light,
            ),
            useMaterial3: true,
          ).copyWith(
            appBarTheme: const AppBarTheme(centerTitle: true, elevation: 0),
            cardTheme: CardThemeData(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            navigationRailTheme: const NavigationRailThemeData(
              selectedIconTheme: IconThemeData(size: 24),
              unselectedIconTheme: IconThemeData(size: 20),
              labelType: NavigationRailLabelType.all,
            ),
          ),
      darkTheme:
          ThemeData.from(
            colorScheme: ColorScheme.fromSeed(
              seedColor: Colors.deepPurple,
              brightness: Brightness.dark,
            ),
            useMaterial3: true,
          ).copyWith(
            appBarTheme: const AppBarTheme(centerTitle: true, elevation: 0),
            cardTheme: CardThemeData(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            navigationRailTheme: const NavigationRailThemeData(
              selectedIconTheme: IconThemeData(size: 24),
              unselectedIconTheme: IconThemeData(size: 20),
              labelType: NavigationRailLabelType.all,
            ),
          ),
      themeMode: ThemeMode.system,
      home: const MainScreen(),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const MemoSuiteScreen(),
    const AthkarProScreen(),
    const MoneyFlowScreen(),
    const ProfileScreen(),
    const SettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          NavigationRail(
            selectedIndex: _selectedIndex,
            onDestinationSelected: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            labelType: NavigationRailLabelType.all,
            destinations: const [
              NavigationRailDestination(
                icon: Icon(Icons.dashboard_outlined),
                selectedIcon: Icon(Icons.dashboard),
                label: Text('Dashboard'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.mic_outlined),
                selectedIcon: Icon(Icons.mic),
                label: Text('Memo Suite'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.auto_stories_outlined),
                selectedIcon: Icon(Icons.auto_stories),
                label: Text('Athkar Pro'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.account_balance_wallet_outlined),
                selectedIcon: Icon(Icons.account_balance_wallet),
                label: Text('Money Flow'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.person_outlined),
                selectedIcon: Icon(Icons.person),
                label: Text('Profile'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.settings_outlined),
                selectedIcon: Icon(Icons.settings),
                label: Text('Settings'),
              ),
            ],
          ),
          const VerticalDivider(thickness: 1, width: 1),
          Expanded(child: _screens[_selectedIndex]),
        ],
      ),
    );
  }
}

// Simple placeholder screens
class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.dashboard, size: 64, color: Colors.blue),
            SizedBox(height: 16),
            Text(
              'Welcome to ShadowSuite',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Your all-in-one productivity suite',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}

class MemoSuiteScreen extends ConsumerStatefulWidget {
  const MemoSuiteScreen({super.key});

  @override
  ConsumerState<MemoSuiteScreen> createState() => _MemoSuiteScreenState();
}

class _MemoSuiteScreenState extends ConsumerState<MemoSuiteScreen> {
  int _recordingDuration = 0;

  void _addMemo() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const CreateMemoScreen()));
  }

  Future<void> _startRecording() async {
    ref.read(recordingStateProvider.notifier).setRecording();
    setState(() {
      _recordingDuration = 0;
    });

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Recording started...')));
  }

  Future<void> _stopRecording() async {
    ref.read(recordingStateProvider.notifier).setIdle();

    // Create a new memo with the recording
    final memosNotifier = ref.read(memosProvider.notifier);
    memosNotifier.addMemo(
      MemoModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: 'demo-user-id',
        title: 'Voice Memo',
        description: 'Recorded on ${DateTime.now().toString().split('.')[0]}',
        transcription: 'Audio transcription will be available soon...',
        audioFilePath: null,
        audioUrl: null,
        duration: _recordingDuration,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
        tags: [],
      ),
    );

    setState(() {
      _recordingDuration = 0;
    });

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Recording saved!')));
  }

  void _toggleRecording() {
    final recordingState = ref.read(recordingStateProvider);
    if (recordingState == RecordingState.recording) {
      _stopRecording();
    } else {
      _startRecording();
    }
  }

  void _deleteMemo(String id) {
    ref.read(memosProvider.notifier).deleteMemo(id);
  }

  @override
  Widget build(BuildContext context) {
    final memosAsync = ref.watch(memosProvider);
    final recordingState = ref.watch(recordingStateProvider);
    final isRecording = recordingState == RecordingState.recording;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Memo Suite'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.add_box),
            onPressed: _addMemo,
            tooltip: 'Add text memo',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Search functionality')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: () {
              ref.read(memosProvider.notifier).syncMemos();
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('Syncing memos...')));
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Recording status indicator
          if (isRecording)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.red.withValues(alpha: 0.1),
              child: Row(
                children: [
                  const Icon(Icons.fiber_manual_record, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Recording...',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${(_recordingDuration ~/ 60).toString().padLeft(2, '0')}:${(_recordingDuration % 60).toString().padLeft(2, '0')}',
                    style: const TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

          // Main content
          Expanded(
            child: memosAsync.when(
              data: (memos) {
                if (memos.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.mic_none, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'No memos yet',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Tap the microphone to start recording',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: memos.length,
                  itemBuilder: (context, index) {
                    final memo = memos[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.blue.withValues(alpha: 0.1),
                          child: Icon(
                            memo.audioFilePath != null ? Icons.mic : Icons.note,
                            color: Colors.blue,
                          ),
                        ),
                        title: Text(memo.title),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (memo.description != null)
                              Text(memo.description!),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 14,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${(memo.duration ~/ 60).toString().padLeft(2, '0')}:${(memo.duration % 60).toString().padLeft(2, '0')}',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Icon(
                                  Icons.calendar_today,
                                  size: 14,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${memo.createdAt.day}/${memo.createdAt.month}/${memo.createdAt.year}',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        trailing: PopupMenuButton(
                          itemBuilder: (context) => [
                            if (memo.audioFilePath != null)
                              const PopupMenuItem(
                                value: 'play',
                                child: Row(
                                  children: [
                                    Icon(Icons.play_arrow),
                                    SizedBox(width: 8),
                                    Text('Play'),
                                  ],
                                ),
                              ),
                            const PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit),
                                  SizedBox(width: 8),
                                  Text('Edit'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text(
                                    'Delete',
                                    style: TextStyle(color: Colors.red),
                                  ),
                                ],
                              ),
                            ),
                          ],
                          onSelected: (value) {
                            if (value == 'play') {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Playing ${memo.title}'),
                                ),
                              );
                            } else if (value == 'edit') {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      CreateMemoScreen(editingMemo: memo),
                                ),
                              );
                            } else if (value == 'delete') {
                              _deleteMemo(memo.id);
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Memo deleted')),
                              );
                            }
                          },
                        ),
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(memo.title),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (memo.description != null) ...[
                                    Text('Description: ${memo.description}'),
                                    const SizedBox(height: 8),
                                  ],
                                  Text(
                                    'Duration: ${(memo.duration ~/ 60).toString().padLeft(2, '0')}:${(memo.duration % 60).toString().padLeft(2, '0')}',
                                  ),
                                  const SizedBox(height: 8),
                                  Text('Created: ${memo.createdAt}'),
                                  if (memo.transcription != null) ...[
                                    const SizedBox(height: 16),
                                    const Text(
                                      'Transcription:',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(memo.transcription!),
                                  ],
                                  if (memo.tags.isNotEmpty) ...[
                                    const SizedBox(height: 16),
                                    const Text(
                                      'Tags:',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Wrap(
                                      spacing: 8,
                                      children: memo.tags
                                          .map(
                                            (tag) => Chip(
                                              label: Text(tag),
                                              materialTapTargetSize:
                                                  MaterialTapTargetSize
                                                      .shrinkWrap,
                                            ),
                                          )
                                          .toList(),
                                    ),
                                  ],
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('Close'),
                                ),
                                if (memo.audioFilePath != null)
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'Playing ${memo.title}',
                                          ),
                                        ),
                                      );
                                    },
                                    icon: const Icon(Icons.play_arrow),
                                    label: const Text('Play'),
                                  ),
                              ],
                            ),
                          );
                        },
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text('Error: $error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () =>
                          ref.read(memosProvider.notifier).loadMemos(),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _toggleRecording,
        backgroundColor: isRecording ? Colors.red : null,
        child: Icon(isRecording ? Icons.stop : Icons.mic),
      ),
    );
  }
}

class AthkarProScreen extends StatefulWidget {
  const AthkarProScreen({super.key});

  @override
  State<AthkarProScreen> createState() => _AthkarProScreenState();
}

class _AthkarProScreenState extends State<AthkarProScreen> {
  final List<Map<String, dynamic>> _routines = [
    {
      'id': '1',
      'name': 'Morning Athkar',
      'description': 'Daily morning remembrance',
      'targetCount': 33,
      'currentCount': 15,
      'steps': [
        {
          'arabicText': 'سُبْحَانَ اللَّهِ',
          'transliteration': 'Subhan Allah',
          'translation': 'Glory be to Allah',
          'repetitions': 33,
          'currentRepetitions': 15,
        },
        {
          'arabicText': 'الْحَمْدُ لِلَّهِ',
          'transliteration': 'Alhamdulillah',
          'translation': 'Praise be to Allah',
          'repetitions': 33,
          'currentRepetitions': 0,
        },
        {
          'arabicText': 'اللَّهُ أَكْبَرُ',
          'transliteration': 'Allahu Akbar',
          'translation': 'Allah is the Greatest',
          'repetitions': 34,
          'currentRepetitions': 0,
        },
      ],
      'isActive': true,
      'reminderTime': '06:00',
    },
    {
      'id': '2',
      'name': 'Evening Athkar',
      'description': 'Daily evening remembrance',
      'targetCount': 100,
      'currentCount': 0,
      'steps': [
        {
          'arabicText': 'أَسْتَغْفِرُ اللَّهَ',
          'transliteration': 'Astaghfirullah',
          'translation': 'I seek forgiveness from Allah',
          'repetitions': 100,
          'currentRepetitions': 0,
        },
      ],
      'isActive': true,
      'reminderTime': '18:00',
    },
  ];

  void _addRoutine() {
    setState(() {
      _routines.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'name': 'New Routine ${_routines.length + 1}',
        'description': 'Custom athkar routine',
        'targetCount': 33,
        'currentCount': 0,
        'steps': [
          {
            'arabicText': 'سُبْحَانَ اللَّهِ',
            'transliteration': 'Subhan Allah',
            'translation': 'Glory be to Allah',
            'repetitions': 33,
            'currentRepetitions': 0,
          },
        ],
        'isActive': true,
        'reminderTime': '09:00',
      });
    });
  }

  void _incrementCount(String routineId, int stepIndex) {
    setState(() {
      final routine = _routines.firstWhere((r) => r['id'] == routineId);
      final step = routine['steps'][stepIndex];

      if (step['currentRepetitions'] < step['repetitions']) {
        step['currentRepetitions']++;

        // Update routine current count
        int totalCurrent = 0;
        for (var s in routine['steps']) {
          totalCurrent += s['currentRepetitions'] as int;
        }
        routine['currentCount'] = totalCurrent;
      }
    });
  }

  void _resetRoutine(String routineId) {
    setState(() {
      final routine = _routines.firstWhere((r) => r['id'] == routineId);
      routine['currentCount'] = 0;
      for (var step in routine['steps']) {
        step['currentRepetitions'] = 0;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Athkar Pro'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(icon: const Icon(Icons.add), onPressed: _addRoutine),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings coming soon')),
              );
            },
          ),
        ],
      ),
      body: _routines.isEmpty
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.auto_stories_outlined,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No routines yet',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Tap the + button to create a routine',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _routines.length,
              itemBuilder: (context, index) {
                final routine = _routines[index];
                final progress = routine['targetCount'] > 0
                    ? routine['currentCount'] / routine['targetCount']
                    : 0.0;

                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    routine['name'],
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    routine['description'],
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                            ),
                            PopupMenuButton(
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit),
                                      SizedBox(width: 8),
                                      Text('Edit'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'reset',
                                  child: Row(
                                    children: [
                                      Icon(Icons.refresh),
                                      SizedBox(width: 8),
                                      Text('Reset'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, color: Colors.red),
                                      SizedBox(width: 8),
                                      Text(
                                        'Delete',
                                        style: TextStyle(color: Colors.red),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                              onSelected: (value) {
                                if (value == 'reset') {
                                  _resetRoutine(routine['id']);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('${routine['name']} reset'),
                                    ),
                                  );
                                } else if (value == 'edit') {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Edit functionality coming soon',
                                      ),
                                    ),
                                  );
                                } else if (value == 'delete') {
                                  setState(() {
                                    _routines.removeWhere(
                                      (r) => r['id'] == routine['id'],
                                    );
                                  });
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        '${routine['name']} deleted',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Progress indicator
                        Row(
                          children: [
                            Expanded(
                              child: LinearProgressIndicator(
                                value: progress.clamp(0.0, 1.0),
                                backgroundColor: Colors.grey[300],
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  progress >= 1.0 ? Colors.green : Colors.blue,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              '${routine['currentCount']}/${routine['targetCount']}',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: progress >= 1.0
                                        ? Colors.green
                                        : Colors.blue,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Steps
                        ...routine['steps'].asMap().entries.map<Widget>((
                          entry,
                        ) {
                          final stepIndex = entry.key;
                          final step = entry.value;
                          final stepProgress = step['repetitions'] > 0
                              ? step['currentRepetitions'] / step['repetitions']
                              : 0.0;

                          return Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: Column(
                              children: [
                                // Arabic text
                                Text(
                                  step['arabicText'],
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                  textAlign: TextAlign.center,
                                  textDirection: TextDirection.rtl,
                                ),
                                const SizedBox(height: 8),

                                // Transliteration
                                Text(
                                  step['transliteration'],
                                  style: Theme.of(context).textTheme.bodyLarge
                                      ?.copyWith(fontStyle: FontStyle.italic),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 4),

                                // Translation
                                Text(
                                  step['translation'],
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(color: Colors.grey[600]),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 12),

                                // Counter and progress
                                Row(
                                  children: [
                                    Expanded(
                                      child: LinearProgressIndicator(
                                        value: stepProgress.clamp(0.0, 1.0),
                                        backgroundColor: Colors.grey[300],
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              stepProgress >= 1.0
                                                  ? Colors.green
                                                  : Colors.orange,
                                            ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      '${step['currentRepetitions']}/${step['repetitions']}',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                    const SizedBox(width: 12),
                                    ElevatedButton(
                                      onPressed:
                                          step['currentRepetitions'] <
                                              step['repetitions']
                                          ? () => _incrementCount(
                                              routine['id'],
                                              stepIndex,
                                            )
                                          : null,
                                      style: ElevatedButton.styleFrom(
                                        minimumSize: const Size(60, 36),
                                        backgroundColor: stepProgress >= 1.0
                                            ? Colors.green
                                            : null,
                                      ),
                                      child: Icon(
                                        stepProgress >= 1.0
                                            ? Icons.check
                                            : Icons.add,
                                        size: 18,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          );
                        }).toList(),

                        // Reminder info
                        if (routine['reminderTime'] != null)
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.blue[50],
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.schedule,
                                  size: 16,
                                  color: Colors.blue[700],
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Reminder: ${routine['reminderTime']}',
                                  style: TextStyle(
                                    color: Colors.blue[700],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }
}

class MoneyFlowScreen extends StatefulWidget {
  const MoneyFlowScreen({super.key});

  @override
  State<MoneyFlowScreen> createState() => _MoneyFlowScreenState();
}

class _MoneyFlowScreenState extends State<MoneyFlowScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final List<Map<String, dynamic>> _accounts = [
    {
      'id': '1',
      'name': 'Checking Account',
      'type': 'checking',
      'balance': 2500.00,
      'currency': 'USD',
      'color': Colors.blue,
    },
    {
      'id': '2',
      'name': 'Savings Account',
      'type': 'savings',
      'balance': 15000.00,
      'currency': 'USD',
      'color': Colors.green,
    },
    {
      'id': '3',
      'name': 'Credit Card',
      'type': 'credit',
      'balance': -850.00,
      'currency': 'USD',
      'color': Colors.red,
    },
  ];

  final List<Map<String, dynamic>> _categories = [
    {
      'id': '1',
      'name': 'Food & Dining',
      'type': 'expense',
      'color': Colors.orange,
      'icon': Icons.restaurant,
    },
    {
      'id': '2',
      'name': 'Transportation',
      'type': 'expense',
      'color': Colors.blue,
      'icon': Icons.directions_car,
    },
    {
      'id': '3',
      'name': 'Entertainment',
      'type': 'expense',
      'color': Colors.purple,
      'icon': Icons.movie,
    },
    {
      'id': '4',
      'name': 'Salary',
      'type': 'income',
      'color': Colors.green,
      'icon': Icons.work,
    },
    {
      'id': '5',
      'name': 'Freelance',
      'type': 'income',
      'color': Colors.teal,
      'icon': Icons.computer,
    },
  ];

  final List<Map<String, dynamic>> _transactions = [
    {
      'id': '1',
      'accountId': '1',
      'categoryId': '1',
      'type': 'expense',
      'amount': 25.50,
      'description': 'Lunch at restaurant',
      'date': DateTime.now().subtract(const Duration(hours: 2)),
    },
    {
      'id': '2',
      'accountId': '1',
      'categoryId': '4',
      'type': 'income',
      'amount': 3000.00,
      'description': 'Monthly salary',
      'date': DateTime.now().subtract(const Duration(days: 1)),
    },
    {
      'id': '3',
      'accountId': '3',
      'categoryId': '2',
      'type': 'expense',
      'amount': 45.00,
      'description': 'Gas station',
      'date': DateTime.now().subtract(const Duration(days: 2)),
    },
  ];

  final List<Map<String, dynamic>> _budgets = [
    {
      'id': '1',
      'categoryId': '1',
      'name': 'Food Budget',
      'amount': 500.00,
      'spent': 125.50,
      'period': 'monthly',
    },
    {
      'id': '2',
      'categoryId': '2',
      'name': 'Transportation Budget',
      'amount': 200.00,
      'spent': 45.00,
      'period': 'monthly',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _addTransaction() {
    setState(() {
      _transactions.insert(0, {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'accountId': _accounts.first['id'],
        'categoryId': _categories.first['id'],
        'type': 'expense',
        'amount': 10.00,
        'description': 'New transaction',
        'date': DateTime.now(),
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Money Flow'),
        automaticallyImplyLeading: false,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
            Tab(icon: Icon(Icons.account_balance), text: 'Accounts'),
            Tab(icon: Icon(Icons.category), text: 'Categories'),
            Tab(icon: Icon(Icons.receipt_long), text: 'Transactions'),
            Tab(icon: Icon(Icons.repeat), text: 'Recurring'),
            Tab(icon: Icon(Icons.savings), text: 'Budgets'),
            Tab(icon: Icon(Icons.analytics), text: 'Reports'),
          ],
        ),
        actions: [
          IconButton(icon: const Icon(Icons.add), onPressed: _addTransaction),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDashboard(),
          _buildAccounts(),
          _buildCategories(),
          _buildTransactions(),
          _buildRecurring(),
          _buildBudgets(),
          _buildReports(),
        ],
      ),
    );
  }

  Widget _buildDashboard() {
    final totalBalance = _accounts.fold<double>(
      0,
      (sum, account) => sum + account['balance'],
    );
    final monthlyIncome = _transactions
        .where(
          (t) =>
              t['type'] == 'income' &&
              DateTime.now().difference(t['date']).inDays <= 30,
        )
        .fold<double>(0, (sum, t) => sum + t['amount']);
    final monthlyExpenses = _transactions
        .where(
          (t) =>
              t['type'] == 'expense' &&
              DateTime.now().difference(t['date']).inDays <= 30,
        )
        .fold<double>(0, (sum, t) => sum + t['amount']);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary cards
          Row(
            children: [
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.account_balance_wallet,
                          size: 32,
                          color: Colors.blue,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '\$${totalBalance.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: totalBalance >= 0
                                    ? Colors.green
                                    : Colors.red,
                              ),
                        ),
                        const Text('Total Balance'),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.trending_up,
                          size: 32,
                          color: Colors.green,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '\$${monthlyIncome.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                        ),
                        const Text('Monthly Income'),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.trending_down,
                          size: 32,
                          color: Colors.red,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '\$${monthlyExpenses.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                        ),
                        const Text('Monthly Expenses'),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Recent transactions
          Text(
            'Recent Transactions',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ..._transactions.take(5).map((transaction) {
            final account = _accounts.firstWhere(
              (a) => a['id'] == transaction['accountId'],
            );
            final category = _categories.firstWhere(
              (c) => c['id'] == transaction['categoryId'],
            );

            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: category['color'].withValues(alpha: 0.1),
                  child: Icon(category['icon'], color: category['color']),
                ),
                title: Text(transaction['description']),
                subtitle: Text('${account['name']} • ${category['name']}'),
                trailing: Text(
                  '${transaction['type'] == 'expense' ? '-' : '+'}\$${transaction['amount'].toStringAsFixed(2)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: transaction['type'] == 'expense'
                        ? Colors.red
                        : Colors.green,
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildAccounts() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _accounts.length,
      itemBuilder: (context, index) {
        final account = _accounts[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: account['color'].withValues(alpha: 0.1),
              child: Icon(Icons.account_balance, color: account['color']),
            ),
            title: Text(account['name']),
            subtitle: Text(account['type'].toString().toUpperCase()),
            trailing: Text(
              '\$${account['balance'].toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: account['balance'] >= 0 ? Colors.green : Colors.red,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategories() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: category['color'].withValues(alpha: 0.1),
              child: Icon(category['icon'], color: category['color']),
            ),
            title: Text(category['name']),
            subtitle: Text(category['type'].toString().toUpperCase()),
            trailing: Icon(
              category['type'] == 'income'
                  ? Icons.trending_up
                  : Icons.trending_down,
              color: category['type'] == 'income' ? Colors.green : Colors.red,
            ),
          ),
        );
      },
    );
  }

  Widget _buildTransactions() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _transactions.length,
      itemBuilder: (context, index) {
        final transaction = _transactions[index];
        final account = _accounts.firstWhere(
          (a) => a['id'] == transaction['accountId'],
        );
        final category = _categories.firstWhere(
          (c) => c['id'] == transaction['categoryId'],
        );

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: category['color'].withValues(alpha: 0.1),
              child: Icon(category['icon'], color: category['color']),
            ),
            title: Text(transaction['description']),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${account['name']} • ${category['name']}'),
                Text(
                  '${transaction['date'].day}/${transaction['date'].month}/${transaction['date'].year}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
            trailing: Text(
              '${transaction['type'] == 'expense' ? '-' : '+'}\$${transaction['amount'].toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: transaction['type'] == 'expense'
                    ? Colors.red
                    : Colors.green,
              ),
            ),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Transaction details: ${transaction['description']}',
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildRecurring() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.repeat, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Recurring Transactions',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          SizedBox(height: 8),
          Text(
            'Set up automatic recurring transactions',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildBudgets() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _budgets.length,
      itemBuilder: (context, index) {
        final budget = _budgets[index];
        final category = _categories.firstWhere(
          (c) => c['id'] == budget['categoryId'],
        );
        final progress = budget['spent'] / budget['amount'];

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(category['icon'], color: category['color']),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            budget['name'],
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '${budget['period']} budget',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '\$${budget['spent'].toStringAsFixed(2)} / \$${budget['amount'].toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                LinearProgressIndicator(
                  value: progress.clamp(0.0, 1.0),
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    progress >= 1.0
                        ? Colors.red
                        : progress >= 0.8
                        ? Colors.orange
                        : Colors.green,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${(progress * 100).toStringAsFixed(1)}% used',
                  style: TextStyle(
                    color: progress >= 1.0
                        ? Colors.red
                        : progress >= 0.8
                        ? Colors.orange
                        : Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildReports() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.analytics, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Financial Reports',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          SizedBox(height: 8),
          Text(
            'Charts and analytics coming soon',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }
}

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person, size: 64, color: Colors.purple),
            SizedBox(height: 16),
            Text(
              'Profile',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'User profile and sync settings',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.settings, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Settings',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'App preferences and configuration',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
