import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/futuristic_theme.dart';
import '../../../core/models/user_model.dart';

class ProfileInfoSection extends ConsumerWidget {
  final UserModel user;

  const ProfileInfoSection({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: FuturisticTheme.primaryBlue.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: FuturisticTheme.primaryBlue.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person_outline,
                color: FuturisticTheme.primaryBlue,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Personal Information',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: FuturisticTheme.primaryBlue,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => _editPersonalInfo(context),
                icon: const Icon(Icons.edit),
                color: FuturisticTheme.primaryBlue,
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Email
          _buildInfoRow(
            context,
            'Email',
            user.email,
            Icons.email_outlined,
          ),

          // Display Name
          _buildInfoRow(
            context,
            'Display Name',
            user.displayName ?? 'Not set',
            Icons.badge_outlined,
          ),

          // Phone (if available)
          if (user.phone != null && user.phone!.isNotEmpty)
            _buildInfoRow(
              context,
              'Phone',
              user.phone!,
              Icons.phone_outlined,
            ),

          // Location (if available)
          if (user.location != null && user.location!.isNotEmpty)
            _buildInfoRow(
              context,
              'Location',
              user.location!,
              Icons.location_on_outlined,
            ),

          // Website (if available)
          if (user.website != null && user.website!.isNotEmpty)
            _buildInfoRow(
              context,
              'Website',
              user.website!,
              Icons.language_outlined,
            ),

          // Bio (if available)
          if (user.bio != null && user.bio!.isNotEmpty)
            _buildInfoRow(
              context,
              'Bio',
              user.bio!,
              Icons.description_outlined,
              isMultiline: true,
            ),

          const SizedBox(height: 16),

          // Account Details
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: FuturisticTheme.primaryBlue.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: FuturisticTheme.primaryBlue.withOpacity(0.1),
              ),
            ),
            child: Column(
              children: [
                _buildDetailRow(
                  context,
                  'Member Since',
                  _formatDate(user.createdAt),
                  Icons.calendar_today_outlined,
                ),
                const SizedBox(height: 8),
                _buildDetailRow(
                  context,
                  'Last Updated',
                  _formatDate(user.updatedAt),
                  Icons.update_outlined,
                ),
                if (user.lastSeenAt != null) ...[
                  const SizedBox(height: 8),
                  _buildDetailRow(
                    context,
                    'Last Seen',
                    _formatDate(user.lastSeenAt!),
                    Icons.access_time_outlined,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    bool isMultiline = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: FuturisticTheme.primaryBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: FuturisticTheme.primaryBlue,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: isMultiline ? null : 1,
                  overflow: isMultiline ? null : TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          color: FuturisticTheme.primaryBlue,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const Spacer(),
        Text(
          value,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()} weeks ago';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _editPersonalInfo(BuildContext context) {
    // TODO: Navigate to edit personal info screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit personal info functionality coming soon'),
      ),
    );
  }
}
