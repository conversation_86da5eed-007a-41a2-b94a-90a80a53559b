{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396241969721615", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13393736612586395", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false], "network_stats": {"srtt": 74170}, "server": "https://zrnpgshoakinpuhldctp.supabase.co"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396242324547163", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true], "network_stats": {"srtt": 80668}, "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396242324806320", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396242325392478", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true], "network_stats": {"srtt": 90607}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396242373701636", "port": 443, "protocol_str": "quic"}], "anonymization": ["LAAAACYAAABodHRwczovL2xlbnNmcm9udGVuZC1wYS5nb29nbGVhcGlzLmNvbQAA", false], "network_stats": {"srtt": 102437}, "server": "https://lensfrontend-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396242375318520", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true], "network_stats": {"srtt": 94883}, "server": "https://www.googleadservices.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396242375609612", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true], "network_stats": {"srtt": 87841}, "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396242376669962", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true], "network_stats": {"srtt": 92501}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true], "network_stats": {"srtt": 93003}, "server": "https://www.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false], "network_stats": {"srtt": 158288}, "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false], "network_stats": {"srtt": 92446}, "server": "https://www.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false], "network_stats": {"srtt": 167473}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false], "network_stats": {"srtt": 95109}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false], "network_stats": {"srtt": 67164}, "server": "https://fonts.gstatic.com"}], "supports_quic": {"address": "**************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}