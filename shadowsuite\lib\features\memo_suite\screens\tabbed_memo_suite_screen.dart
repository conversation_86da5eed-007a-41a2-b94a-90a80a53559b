import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/memo_model.dart';
import '../providers/memo_provider.dart';
import '../services/audio_service.dart';
import 'tabs/dashboard_tab.dart';
import 'tabs/notes_tab.dart';
import 'tabs/todo_tab.dart';
import 'tabs/voice_tab.dart';

class TabbedMemoSuiteScreen extends ConsumerStatefulWidget {
  const TabbedMemoSuiteScreen({super.key});

  @override
  ConsumerState<TabbedMemoSuiteScreen> createState() => _TabbedMemoSuiteScreenState();
}

class _TabbedMemoSuiteScreenState extends ConsumerState<TabbedMemoSuiteScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final AudioService _audioService = AudioService();
  int _recordingDuration = 0;
  String? _currentRecordingPath;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _audioService.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _audioService.dispose();
    super.dispose();
  }

  Future<void> _startRecording() async {
    try {
      final recordingPath = await _audioService.startRecording();
      if (recordingPath != null) {
        ref.read(recordingStateProvider.notifier).setRecording();
        setState(() {
          _recordingDuration = 0;
          _currentRecordingPath = recordingPath;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Recording started...')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to start recording'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting recording: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      final recordingPath = await _audioService.stopRecording();
      ref.read(recordingStateProvider.notifier).setIdle();

      if (recordingPath != null && _currentRecordingPath != null) {
        // Get audio duration
        final duration = await _audioService.getAudioDuration(_currentRecordingPath!);

        // Get transcription
        final transcription = await _audioService.transcribeAudio(_currentRecordingPath!);

        // Create a new voice memo
        final memosNotifier = ref.read(memosProvider.notifier);
        memosNotifier.addMemo(MemoModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          userId: 'demo-user-id',
          title: 'Voice Memo',
          description: 'Recorded on ${DateTime.now().toString().split('.')[0]}',
          transcription: transcription,
          audioFilePath: _currentRecordingPath,
          audioUrl: null,
          duration: duration,
          type: MemoType.voice,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isSynced: false,
          tags: ['voice'],
          category: 'Voice Memos',
          isPinned: false,
          isArchived: false,
          todoItems: [],
          attachments: [],
        ));

        setState(() {
          _recordingDuration = 0;
          _currentRecordingPath = null;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Voice memo saved!')),
          );
          
          // Switch to voice tab to show the new recording
          _tabController.animateTo(3);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to save recording'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error stopping recording: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleRecording() {
    final recordingState = ref.read(recordingStateProvider);
    if (recordingState == RecordingState.recording) {
      _stopRecording();
    } else {
      _startRecording();
    }
  }

  Future<void> _syncMemos() async {
    try {
      await ref.read(memosProvider.notifier).syncMemos();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Sync completed!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sync failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final recordingState = ref.watch(recordingStateProvider);
    final isRecording = recordingState == RecordingState.recording;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Memo Suite'),
        automaticallyImplyLeading: false,
        actions: [
          // Quick voice recording button
          IconButton(
            icon: Icon(
              isRecording ? Icons.stop : Icons.mic,
              color: isRecording ? Colors.red : null,
            ),
            onPressed: _toggleRecording,
            tooltip: isRecording ? 'Stop recording' : 'Start voice recording',
          ),
          // Sync button
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: _syncMemos,
            tooltip: 'Sync memos',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.dashboard),
              text: 'Dashboard',
            ),
            Tab(
              icon: Icon(Icons.note),
              text: 'Notes',
            ),
            Tab(
              icon: Icon(Icons.checklist),
              text: 'Todo',
            ),
            Tab(
              icon: Icon(Icons.mic),
              text: 'Voice',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Recording status indicator
          if (isRecording)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              color: Colors.red.withValues(alpha: 0.1),
              child: Row(
                children: [
                  const Icon(Icons.fiber_manual_record, color: Colors.red, size: 16),
                  const SizedBox(width: 8),
                  const Text(
                    'Recording in progress...',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${(_recordingDuration ~/ 60).toString().padLeft(2, '0')}:${(_recordingDuration % 60).toString().padLeft(2, '0')}',
                    style: const TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                DashboardTab(audioService: _audioService),
                NotesTab(audioService: _audioService),
                TodoTab(audioService: _audioService),
                VoiceTab(audioService: _audioService),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
