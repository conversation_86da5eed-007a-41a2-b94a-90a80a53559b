import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/futuristic_theme.dart';
import '../../../core/models/user_model.dart';
import '../../../core/providers/theme_provider.dart';

class ProfileSettingsSection extends ConsumerWidget {
  final UserModel user;

  const ProfileSettingsSection({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeState = ref.watch(themeProvider);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: FuturisticTheme.primaryBlue.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: FuturisticTheme.primaryBlue.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings_outlined,
                color: FuturisticTheme.primaryBlue,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'App Settings',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: FuturisticTheme.primaryBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Theme Setting
          _buildSettingTile(
            context,
            'Dark Mode',
            'Switch between light and dark themes',
            Icons.dark_mode_outlined,
            trailing: Switch(
              value: themeState.isDark,
              onChanged: (value) {
                ref.read(themeProvider.notifier).toggleDarkMode();
              },
              activeColor: FuturisticTheme.primaryBlue,
            ),
          ),

          // Notifications Setting
          _buildSettingTile(
            context,
            'Notifications',
            'Manage notification preferences',
            Icons.notifications_outlined,
            trailing: Switch(
              value: _getNotificationSetting(),
              onChanged: (value) {
                _toggleNotifications(value);
              },
              activeColor: FuturisticTheme.primaryBlue,
            ),
          ),

          // Language Setting
          _buildSettingTile(
            context,
            'Language',
            'Change app language',
            Icons.language_outlined,
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _showLanguageOptions(context),
          ),

          // Privacy Setting
          _buildSettingTile(
            context,
            'Privacy',
            'Manage privacy and data settings',
            Icons.privacy_tip_outlined,
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _showPrivacySettings(context),
          ),

          // Backup & Restore
          _buildSettingTile(
            context,
            'Backup & Restore',
            'Manage your data backup',
            Icons.backup_outlined,
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _showBackupOptions(context),
          ),

          // About
          _buildSettingTile(
            context,
            'About ShadowSuite',
            'App version and information',
            Icons.info_outlined,
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _showAboutDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon, {
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: FuturisticTheme.primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: FuturisticTheme.primaryBlue, size: 20),
        ),
        title: Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
        trailing: trailing,
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        tileColor: Colors.transparent,
        hoverColor: FuturisticTheme.primaryBlue.withOpacity(0.05),
      ),
    );
  }

  bool _getNotificationSetting() {
    // TODO: Get actual notification setting from preferences
    return true;
  }

  void _toggleNotifications(bool value) {
    // TODO: Implement notification toggle
    print('Notifications toggled: $value');
  }

  void _showLanguageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select Language',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Text('🇺🇸'),
              title: const Text('English'),
              trailing: const Icon(Icons.check),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Text('🇸🇦'),
              title: const Text('العربية'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Text('🇪🇸'),
              title: const Text('Español'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _showPrivacySettings(BuildContext context) {
    // TODO: Navigate to privacy settings screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Privacy settings coming soon')),
    );
  }

  void _showBackupOptions(BuildContext context) {
    // TODO: Navigate to backup options screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Backup options coming soon')));
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About ShadowSuite'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Version: 1.0.0'),
            SizedBox(height: 8),
            Text('Your comprehensive productivity companion'),
            SizedBox(height: 16),
            Text('Features:'),
            Text('• Memo Suite - Notes, Voice, Todo'),
            Text('• Athkar Pro - Islamic Prayer Tracker'),
            Text('• Money Flow - Personal Finance'),
            Text('• Real-time Cloud Sync'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
