import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/providers/localization_provider.dart';
import '../../../features/athkar_pro/screens/dhikr_tab.dart';
import '../../../features/athkar_pro/screens/prayer_tab.dart';
import '../../../features/athkar_pro/screens/quran_tab.dart';
import '../../../features/athkar_pro/screens/prayer_time_settings_screen.dart';

class AthkarProScreen extends ConsumerWidget {
  const AthkarProScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Athkar Pro'),
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const PrayerTimeSettingsScreen(),
                  ),
                );
              },
            ),
          ],
          bottom: const TabBar(
            tabs: [
              Tab(icon: Icon(Icons.auto_stories), text: 'Dhikr'),
              Tab(icon: Icon(Icons.access_time), text: 'Prayer'),
              Tab(icon: Icon(Icons.menu_book), text: 'Quran'),
            ],
          ),
        ),
        body: TabBarView(
          children: [const DhikrTab(), const PrayerTab(), const QuranTab()],
        ),
      ),
    );
  }
}
