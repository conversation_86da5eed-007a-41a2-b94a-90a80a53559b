import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_nexus_model.dart';
import '../services/nexus_profile_service.dart';
import '../../../core/services/auth_service.dart';

// Profile state
class NexusProfileState {
  final UserNexusModel? profile;
  final bool isLoading;
  final bool isUpdating;
  final String? error;
  final List<Map<String, dynamic>> activityLogs;
  final List<Map<String, dynamic>> syncLogs;
  final bool isRealTimeConnected;

  const NexusProfileState({
    this.profile,
    this.isLoading = false,
    this.isUpdating = false,
    this.error,
    this.activityLogs = const [],
    this.syncLogs = const [],
    this.isRealTimeConnected = false,
  });

  NexusProfileState copyWith({
    UserNexusModel? profile,
    bool? isLoading,
    bool? isUpdating,
    String? error,
    List<Map<String, dynamic>>? activityLogs,
    List<Map<String, dynamic>>? syncLogs,
    bool? isRealTimeConnected,
  }) {
    return NexusProfileState(
      profile: profile ?? this.profile,
      isLoading: isLoading ?? this.isLoading,
      isUpdating: isUpdating ?? this.isUpdating,
      error: error,
      activityLogs: activityLogs ?? this.activityLogs,
      syncLogs: syncLogs ?? this.syncLogs,
      isRealTimeConnected: isRealTimeConnected ?? this.isRealTimeConnected,
    );
  }
}

// Profile notifier
class NexusProfileNotifier extends StateNotifier<NexusProfileState> {
  final NexusProfileService _profileService = NexusProfileService();
  final AuthService _authService = AuthService();
  RealtimeChannel? _profileSubscription;

  NexusProfileNotifier() : super(const NexusProfileState()) {
    _initializeProfile();
  }

  Future<void> _initializeProfile() async {
    if (!_profileService.isInitialized) {
      state = state.copyWith(error: 'Profile service not initialized');
      return;
    }

    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      state = state.copyWith(error: 'No authenticated user');
      return;
    }

    await loadProfile(currentUser.id);
  }

  Future<void> loadProfile(String authUserId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      debugPrint('🔄 Loading profile for user: $authUserId');
      
      final profile = await _profileService.getUserProfile(authUserId);
      
      if (profile != null) {
        state = state.copyWith(
          profile: profile,
          isLoading: false,
        );
        
        // Start real-time subscription
        _subscribeToProfileUpdates(authUserId);
        
        // Load activity and sync logs
        _loadActivityLogs(authUserId);
        _loadSyncLogs(authUserId);
        
        debugPrint('✅ Profile loaded successfully');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Profile not found',
        );
      }
    } catch (e) {
      debugPrint('❌ Error loading profile: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load profile: $e',
      );
    }
  }

  Future<void> createProfile(UserNexusModel profile) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      debugPrint('👤 Creating new profile');
      
      final createdProfile = await _profileService.createUserProfile(profile);
      
      state = state.copyWith(
        profile: createdProfile,
        isLoading: false,
      );
      
      // Start real-time subscription
      _subscribeToProfileUpdates(profile.authUserId);
      
      debugPrint('✅ Profile created successfully');
    } catch (e) {
      debugPrint('❌ Error creating profile: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create profile: $e',
      );
    }
  }

  Future<void> updateProfile(Map<String, dynamic> updates) async {
    if (state.profile == null) return;

    state = state.copyWith(isUpdating: true, error: null);

    try {
      debugPrint('🔄 Updating profile');
      
      final updatedProfile = await _profileService.updateUserProfile(
        state.profile!.id,
        updates,
      );
      
      state = state.copyWith(
        profile: updatedProfile,
        isUpdating: false,
      );
      
      debugPrint('✅ Profile updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating profile: $e');
      state = state.copyWith(
        isUpdating: false,
        error: 'Failed to update profile: $e',
      );
    }
  }

  Future<void> updateAvatar(String avatarUrl) async {
    await updateProfile({'avatarUrl': avatarUrl});
  }

  Future<void> updateCoverImage(String coverImageUrl) async {
    await updateProfile({'coverImageUrl': coverImageUrl});
  }

  Future<void> updatePersonalInfo({
    String? fullName,
    String? displayName,
    String? bio,
    String? tagline,
    String? phone,
    String? location,
    String? website,
    DateTime? dateOfBirth,
  }) async {
    final updates = <String, dynamic>{};
    
    if (fullName != null) updates['fullName'] = fullName;
    if (displayName != null) updates['displayName'] = displayName;
    if (bio != null) updates['bio'] = bio;
    if (tagline != null) updates['tagline'] = tagline;
    if (phone != null) updates['phone'] = phone;
    if (location != null) updates['location'] = location;
    if (website != null) updates['website'] = website;
    if (dateOfBirth != null) updates['dateOfBirth'] = dateOfBirth.toIso8601String();
    
    if (updates.isNotEmpty) {
      await updateProfile(updates);
    }
  }

  Future<void> updatePreferences({
    String? timezone,
    String? languagePreference,
    String? themePreference,
    Map<String, dynamic>? notificationSettings,
    Map<String, dynamic>? privacySettings,
    Map<String, dynamic>? appPreferences,
  }) async {
    final updates = <String, dynamic>{};
    
    if (timezone != null) updates['timezone'] = timezone;
    if (languagePreference != null) updates['languagePreference'] = languagePreference;
    if (themePreference != null) updates['themePreference'] = themePreference;
    if (notificationSettings != null) updates['notificationSettings'] = notificationSettings;
    if (privacySettings != null) updates['privacySettings'] = privacySettings;
    if (appPreferences != null) updates['appPreferences'] = appPreferences;
    
    if (updates.isNotEmpty) {
      await updateProfile(updates);
    }
  }

  Future<void> updateSocialLinks(Map<String, dynamic> socialLinks) async {
    await updateProfile({'socialLinks': socialLinks});
  }

  Future<void> refreshActivityLogs() async {
    if (state.profile != null) {
      await _loadActivityLogs(state.profile!.authUserId);
    }
  }

  Future<void> refreshSyncLogs() async {
    if (state.profile != null) {
      await _loadSyncLogs(state.profile!.authUserId);
    }
  }

  Future<void> _loadActivityLogs(String authUserId) async {
    try {
      final logs = await _profileService.getActivityLogs(authUserId);
      state = state.copyWith(activityLogs: logs);
    } catch (e) {
      debugPrint('❌ Error loading activity logs: $e');
    }
  }

  Future<void> _loadSyncLogs(String authUserId) async {
    try {
      final logs = await _profileService.getSyncLogs(authUserId);
      state = state.copyWith(syncLogs: logs);
    } catch (e) {
      debugPrint('❌ Error loading sync logs: $e');
    }
  }

  void _subscribeToProfileUpdates(String authUserId) {
    try {
      _profileSubscription?.unsubscribe();
      
      _profileSubscription = _profileService.subscribeToProfile(
        authUserId,
        (updatedProfile) {
          debugPrint('📡 Real-time profile update received');
          state = state.copyWith(
            profile: updatedProfile,
            isRealTimeConnected: true,
          );
        },
      );
      
      state = state.copyWith(isRealTimeConnected: true);
      debugPrint('✅ Real-time profile subscription started');
    } catch (e) {
      debugPrint('❌ Error setting up real-time subscription: $e');
      state = state.copyWith(isRealTimeConnected: false);
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  @override
  void dispose() {
    _profileSubscription?.unsubscribe();
    super.dispose();
  }
}

// Providers
final nexusProfileProvider = StateNotifierProvider<NexusProfileNotifier, NexusProfileState>((ref) {
  return NexusProfileNotifier();
});

// Convenience providers
final currentNexusProfileProvider = Provider<UserNexusModel?>((ref) {
  return ref.watch(nexusProfileProvider).profile;
});

final profileLoadingProvider = Provider<bool>((ref) {
  return ref.watch(nexusProfileProvider).isLoading;
});

final profileUpdatingProvider = Provider<bool>((ref) {
  return ref.watch(nexusProfileProvider).isUpdating;
});

final profileErrorProvider = Provider<String?>((ref) {
  return ref.watch(nexusProfileProvider).error;
});

final activityLogsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  return ref.watch(nexusProfileProvider).activityLogs;
});

final syncLogsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  return ref.watch(nexusProfileProvider).syncLogs;
});

final realTimeConnectionProvider = Provider<bool>((ref) {
  return ref.watch(nexusProfileProvider).isRealTimeConnected;
});
