<variant
    name="release"
    package="com.baseflow.permissionhandler"
    minSdkVersion="19"
    targetSdkVersion="19"
    mergedManifest="D:\apps\android\shadowsuite\build\permission_handler_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="D:\apps\android\shadowsuite\build\permission_handler_android\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\apps\android\shadowsuite\build\permission_handler_android\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    partialResultsDir="D:\apps\android\shadowsuite\build\permission_handler_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\apps\android\shadowsuite\build\permission_handler_android\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\apps\android\shadowsuite\build\permission_handler_android\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="com.baseflow.permissionhandler"
      generatedSourceFolders="D:\apps\android\shadowsuite\build\permission_handler_android\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\apps\android\shadowsuite\build\permission_handler_android\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\94db09f884c4047c5175e96b4990c719\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
