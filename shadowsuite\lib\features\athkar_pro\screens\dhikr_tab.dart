import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/dhikr_provider.dart';
import '../providers/routine_provider.dart';
import '../models/dhikr_model.dart';
import '../models/routine_model.dart';
import '../screens/routine_builder_screen.dart';

class DhikrTab extends ConsumerStatefulWidget {
  const DhikrTab({super.key});

  @override
  ConsumerState<DhikrTab> createState() => _DhikrTabState();
}

class _DhikrTabState extends ConsumerState<DhikrTab> {
  DhikrCategory? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    final dhikrListAsync = ref.watch(dhikrListProvider);
    final filteredDhikr = ref.watch(filteredDhikrProvider(_selectedCategory));
    final activeSession = ref.watch(activeDhikrSessionProvider);
    final dhikrStats = ref.watch(dhikrStatsProvider);
    final routinesAsync = ref.watch(routinesProvider);
    final routineStats = ref.watch(routineStatsProvider);

    return Scaffold(
      body: Column(
        children: [
          // Category filter
          _buildCategoryFilter(),

          // Statistics card
          _buildStatsCard(context, dhikrStats),

          // Active session indicator
          if (activeSession != null)
            _buildActiveSessionCard(context, ref, activeSession),

          // Content tabs
          Expanded(
            child: DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  const TabBar(
                    tabs: [
                      Tab(text: 'Individual Dhikr'),
                      Tab(text: 'Routines'),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        // Individual dhikr tab
                        dhikrListAsync.when(
                          loading: () =>
                              const Center(child: CircularProgressIndicator()),
                          error: (error, stackTrace) => Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.error,
                                  size: 64,
                                  color: Colors.red,
                                ),
                                const SizedBox(height: 16),
                                Text('Error loading dhikr: $error'),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: () =>
                                      ref.invalidate(dhikrListProvider),
                                  child: const Text('Retry'),
                                ),
                              ],
                            ),
                          ),
                          data: (_) =>
                              _buildDhikrList(context, ref, filteredDhikr),
                        ),

                        // Routines tab
                        routinesAsync.when(
                          loading: () =>
                              const Center(child: CircularProgressIndicator()),
                          error: (error, stackTrace) => Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.error,
                                  size: 64,
                                  color: Colors.red,
                                ),
                                const SizedBox(height: 16),
                                Text('Error loading routines: $error'),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: () =>
                                      ref.invalidate(routinesProvider),
                                  child: const Text('Retry'),
                                ),
                              ],
                            ),
                          ),
                          data: (routines) =>
                              _buildRoutinesList(context, ref, routines),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateOptions(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    final categories = ref.watch(dhikrCategoriesProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Categories',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: _selectedCategory == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = selected ? null : _selectedCategory;
                    });
                  },
                ),
                const SizedBox(width: 8),
                ...categories.map(
                  (category) => Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category.displayName),
                      selected: _selectedCategory == category,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = selected ? category : null;
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard(BuildContext context, DhikrStatistics stats) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Today\'s Progress',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      context,
                      'Completed',
                      '${stats.completedToday}/${stats.totalDhikr}',
                      Icons.check_circle,
                      Colors.green,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      context,
                      'Total Count',
                      stats.totalCount.toString(),
                      Icons.repeat,
                      Colors.blue,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      context,
                      'Target',
                      stats.targetCount.toString(),
                      Icons.flag,
                      Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: stats.progressPercentage,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  stats.isAllCompleted ? Colors.green : Colors.blue,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${(stats.progressPercentage * 100).round()}% of target reached',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildActiveSessionCard(
    BuildContext context,
    WidgetRef ref,
    DhikrSession session,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        color: Colors.green.withValues(alpha: 0.1),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(Icons.play_circle, color: Colors.green[700], size: 32),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Active Session',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.green[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Count: ${session.count}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.green[600],
                      ),
                    ),
                  ],
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  ref.read(activeDhikrSessionProvider.notifier).endSession();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[700],
                  foregroundColor: Colors.white,
                ),
                child: const Text('End'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDhikrList(
    BuildContext context,
    WidgetRef ref,
    List<DhikrModel> dhikrList,
  ) {
    if (dhikrList.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.beenhere, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No dhikr found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Try selecting a different category',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: dhikrList.length,
      itemBuilder: (context, index) {
        final dhikr = dhikrList[index];
        return _buildDhikrCard(context, ref, dhikr);
      },
    );
  }

  Widget _buildDhikrCard(
    BuildContext context,
    WidgetRef ref,
    DhikrModel dhikr,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Arabic text
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                dhikr.arabicText,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
                textAlign: TextAlign.center,
                textDirection: TextDirection.rtl,
              ),
            ),
            const SizedBox(height: 12),

            // Transliteration
            Text(
              dhikr.transliteration,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontStyle: FontStyle.italic,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // Translation
            Text(
              dhikr.translation,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Progress and counter
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            '${dhikr.currentCount}/${dhikr.targetCount}',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: dhikr.isCompleted
                                      ? Colors.green
                                      : Colors.blue,
                                ),
                          ),
                          const SizedBox(width: 8),
                          if (dhikr.isCompleted)
                            const Icon(
                              Icons.check_circle,
                              color: Colors.green,
                              size: 20,
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: dhikr.progress,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          dhikr.isCompleted ? Colors.green : Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Row(
                  children: [
                    ElevatedButton(
                      onPressed: dhikr.isCompleted
                          ? null
                          : () {
                              ref
                                  .read(dhikrListProvider.notifier)
                                  .incrementDhikrCount(dhikr.id);
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: dhikr.isCompleted
                            ? Colors.green
                            : null,
                        minimumSize: const Size(60, 40),
                      ),
                      child: Icon(
                        dhikr.isCompleted ? Icons.check : Icons.add,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () {
                        ref
                            .read(dhikrListProvider.notifier)
                            .resetDhikrCount(dhikr.id);
                      },
                      icon: const Icon(Icons.refresh),
                    ),
                  ],
                ),
              ],
            ),

            // Category and source
            const SizedBox(height: 12),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    dhikr.category,
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (dhikr.source != null) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      dhikr.source!,
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoutinesList(
    BuildContext context,
    WidgetRef ref,
    List<RoutineModel> routines,
  ) {
    if (routines.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.auto_stories, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No routines yet',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Create your first custom routine',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: routines.length,
      itemBuilder: (context, index) {
        final routine = routines[index];
        return _buildRoutineCard(context, ref, routine);
      },
    );
  }

  Widget _buildRoutineCard(
    BuildContext context,
    WidgetRef ref,
    RoutineModel routine,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: routine.isCompleted
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.blue.withValues(alpha: 0.1),
                  child: Icon(
                    routine.isCompleted ? Icons.check : Icons.auto_stories,
                    color: routine.isCompleted ? Colors.green : Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              routine.name,
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                          ),
                          if (routine.isFavorite)
                            const Icon(
                              Icons.favorite,
                              color: Colors.red,
                              size: 16,
                            ),
                        ],
                      ),
                      if (routine.description != null)
                        Text(
                          routine.description!,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      const SizedBox(height: 4),
                      Text(
                        '${routine.steps.length} steps • ${routine.category.displayName}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'favorite',
                      child: Row(
                        children: [
                          Icon(
                            routine.isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                          ),
                          SizedBox(width: 8),
                          Text(
                            routine.isFavorite
                                ? 'Remove from Favorites'
                                : 'Add to Favorites',
                          ),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'reset',
                      child: Row(
                        children: [
                          Icon(Icons.refresh),
                          SizedBox(width: 8),
                          Text('Reset Progress'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) =>
                                RoutineBuilderScreen(routine: routine),
                          ),
                        );
                        break;
                      case 'favorite':
                        ref
                            .read(routinesProvider.notifier)
                            .toggleFavorite(routine.id);
                        break;
                      case 'reset':
                        ref
                            .read(routinesProvider.notifier)
                            .resetRoutine(routine.id);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${routine.name} progress reset'),
                          ),
                        );
                        break;
                      case 'delete':
                        _showDeleteRoutineDialog(context, ref, routine);
                        break;
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${routine.currentRepetitions}/${routine.totalRepetitions}',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: routine.isCompleted
                                      ? Colors.green
                                      : Colors.blue,
                                ),
                          ),
                          Text(
                            '${routine.completedSteps}/${routine.steps.length} steps',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: routine.progress,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          routine.isCompleted ? Colors.green : Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: routine.isCompleted
                      ? null
                      : () {
                          _startRoutineSession(context, ref, routine);
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: routine.isCompleted ? Colors.green : null,
                  ),
                  child: Icon(
                    routine.isCompleted ? Icons.check : Icons.play_arrow,
                    size: 20,
                  ),
                ),
              ],
            ),

            // Tags
            if (routine.tags.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children: routine.tags
                    .map(
                      (tag) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          tag,
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showCreateOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('Create New Routine'),
              subtitle: const Text('Build a custom athkar routine'),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const RoutineBuilderScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.content_copy),
              title: const Text('Use Template'),
              subtitle: const Text('Start from a pre-made template'),
              onTap: () {
                Navigator.of(context).pop();
                _showTemplateSelection(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showTemplateSelection(BuildContext context) {
    final templatesAsync = ref.read(routineTemplatesProvider);

    templatesAsync.when(
      loading: () => showDialog(
        context: context,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Loading templates...'),
            ],
          ),
        ),
      ),
      error: (error, stackTrace) => ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading templates: $error')),
      ),
      data: (templates) => showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Choose Template'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: templates.length,
              itemBuilder: (context, index) {
                final template = templates[index];
                return ListTile(
                  title: Text(template.name),
                  subtitle: Text('${template.steps.length} steps'),
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) =>
                            RoutineBuilderScreen(template: template),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  void _startRoutineSession(
    BuildContext context,
    WidgetRef ref,
    RoutineModel routine,
  ) {
    ref.read(activeRoutineSessionProvider.notifier).startSession(routine);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Started ${routine.name} session'),
        action: SnackBarAction(
          label: 'View',
          onPressed: () {
            // TODO: Navigate to routine session screen
          },
        ),
      ),
    );
  }

  void _showDeleteRoutineDialog(
    BuildContext context,
    WidgetRef ref,
    RoutineModel routine,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Routine'),
        content: Text(
          'Are you sure you want to delete "${routine.name}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(routinesProvider.notifier).deleteRoutine(routine.id);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${routine.name} deleted')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
