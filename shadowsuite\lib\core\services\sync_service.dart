import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import 'database_service.dart';
import 'supabase_service.dart';

class SyncService {
  static SyncService? _instance;
  final DatabaseService _databaseService = DatabaseService();
  final SupabaseService _supabaseService = SupabaseService();
  final Connectivity _connectivity = Connectivity();
  
  Timer? _syncTimer;
  bool _isSyncing = false;
  
  SyncService._internal();

  factory SyncService() {
    _instance ??= SyncService._internal();
    return _instance!;
  }

  Future<void> initialize() async {
    await _startPeriodicSync();
    _listenToConnectivityChanges();
  }

  Future<void> _startPeriodicSync() async {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(AppConstants.syncInterval, (timer) {
      if (_supabaseService.isAuthenticated) {
        syncAll();
      }
    });
  }

  void _listenToConnectivityChanges() {
    _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
      if (result != ConnectivityResult.none && _supabaseService.isAuthenticated) {
        syncAll();
      }
    });
  }

  Future<bool> get isOnline async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncAll() async {
    if (_isSyncing || !await isOnline || !_supabaseService.isAuthenticated) {
      return;
    }

    _isSyncing = true;
    try {
      final userId = _supabaseService.currentUser!.id;
      
      // Sync in order: users, then dependent tables
      await _syncTable('users', userId);
      await _syncTable('memos', userId);
      await _syncTable('athkar_routines', userId);
      await _syncTable('athkar_steps', userId);
      await _syncTable('accounts', userId);
      await _syncTable('categories', userId);
      await _syncTable('transactions', userId);
      await _syncTable('budgets', userId);
      
      await _updateLastSyncTime();
    } catch (e) {
      print('Sync error: $e');
    } finally {
      _isSyncing = false;
    }
  }

  Future<void> _syncTable(String tableName, String userId) async {
    try {
      // Get last sync time
      final lastSync = await _getLastSyncTime(tableName);
      
      // Upload local changes first
      await _uploadLocalChanges(tableName, userId);
      
      // Download remote changes
      await _downloadRemoteChanges(tableName, userId, lastSync);
      
    } catch (e) {
      print('Error syncing table $tableName: $e');
    }
  }

  Future<void> _uploadLocalChanges(String tableName, String userId) async {
    final db = await _databaseService.database;
    
    // Get unsynced local records
    final unsyncedRecords = await db.query(
      tableName,
      where: 'user_id = ? AND is_synced = 0',
      whereArgs: [userId],
    );

    if (unsyncedRecords.isEmpty) return;

    // Convert to Supabase format and upload
    final supabaseRecords = unsyncedRecords.map((record) {
      final converted = Map<String, dynamic>.from(record);
      
      // Convert SQLite format to Supabase format
      converted['created_at'] = DateTime.parse(converted['created_at']).toIso8601String();
      converted['updated_at'] = DateTime.parse(converted['updated_at']).toIso8601String();
      
      // Handle boolean fields
      if (converted.containsKey('is_active')) {
        converted['is_active'] = converted['is_active'] == 1;
      }
      if (converted.containsKey('is_synced')) {
        converted['is_synced'] = converted['is_synced'] == 1;
      }
      
      // Handle JSON fields
      if (converted.containsKey('tags') && converted['tags'] is String) {
        converted['tags'] = jsonDecode(converted['tags'] ?? '[]');
      }
      if (converted.containsKey('reminder_days') && converted['reminder_days'] is String) {
        converted['reminder_days'] = jsonDecode(converted['reminder_days'] ?? '[]');
      }
      if (converted.containsKey('preferences') && converted['preferences'] is String) {
        converted['preferences'] = jsonDecode(converted['preferences'] ?? '{}');
      }
      
      return converted;
    }).toList();

    // Batch upload to Supabase
    await _supabaseService.batchInsert(
      table: tableName,
      data: supabaseRecords,
    );

    // Mark as synced in local database
    for (final record in unsyncedRecords) {
      await db.update(
        tableName,
        {'is_synced': 1},
        where: 'id = ?',
        whereArgs: [record['id']],
      );
    }
  }

  Future<void> _downloadRemoteChanges(String tableName, String userId, DateTime? lastSync) async {
    final db = await _databaseService.database;
    
    // Build query for remote changes
    String whereClause = 'user_id=eq.$userId';
    if (lastSync != null) {
      whereClause += '&updated_at=gt.${lastSync.toIso8601String()}';
    }

    // Get remote records
    final remoteRecords = await _supabaseService.select(
      table: tableName,
      where: whereClause,
      orderBy: 'updated_at',
    );

    if (remoteRecords.isEmpty) return;

    // Convert and insert/update local records
    for (final remoteRecord in remoteRecords) {
      final converted = Map<String, dynamic>.from(remoteRecord);
      
      // Convert Supabase format to SQLite format
      if (converted.containsKey('created_at')) {
        converted['created_at'] = DateTime.parse(converted['created_at']).toIso8601String();
      }
      if (converted.containsKey('updated_at')) {
        converted['updated_at'] = DateTime.parse(converted['updated_at']).toIso8601String();
      }
      
      // Handle boolean fields
      if (converted.containsKey('is_active')) {
        converted['is_active'] = converted['is_active'] == true ? 1 : 0;
      }
      converted['is_synced'] = 1; // Mark as synced
      
      // Handle JSON fields
      if (converted.containsKey('tags') && converted['tags'] is List) {
        converted['tags'] = jsonEncode(converted['tags']);
      }
      if (converted.containsKey('reminder_days') && converted['reminder_days'] is List) {
        converted['reminder_days'] = jsonEncode(converted['reminder_days']);
      }
      if (converted.containsKey('preferences') && converted['preferences'] is Map) {
        converted['preferences'] = jsonEncode(converted['preferences']);
      }

      // Check if record exists locally
      final existingRecords = await db.query(
        tableName,
        where: 'id = ?',
        whereArgs: [converted['id']],
      );

      if (existingRecords.isEmpty) {
        // Insert new record
        await db.insert(tableName, converted);
      } else {
        // Update existing record (conflict resolution: remote wins)
        await db.update(
          tableName,
          converted,
          where: 'id = ?',
          whereArgs: [converted['id']],
        );
      }
    }
  }

  Future<DateTime?> _getLastSyncTime(String tableName) async {
    final prefs = await SharedPreferences.getInstance();
    final lastSyncString = prefs.getString('${AppConstants.keyLastSync}_$tableName');
    return lastSyncString != null ? DateTime.parse(lastSyncString) : null;
  }

  Future<void> _updateLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now().toIso8601String();
    
    // Update last sync time for all tables
    final tables = ['users', 'memos', 'athkar_routines', 'athkar_steps', 
                   'accounts', 'categories', 'transactions', 'budgets'];
    
    for (final table in tables) {
      await prefs.setString('${AppConstants.keyLastSync}_$table', now);
    }
  }

  Future<void> markForSync(String tableName, String recordId) async {
    final db = await _databaseService.database;
    await db.update(
      tableName,
      {'is_synced': 0, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [recordId],
    );
  }

  void dispose() {
    _syncTimer?.cancel();
  }
}
