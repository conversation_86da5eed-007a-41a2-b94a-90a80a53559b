import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/memo_model.dart';
import '../../providers/memo_provider.dart';
import '../../providers/search_provider.dart';
import '../../services/audio_service.dart';
import '../create_memo_screen.dart';

class NotesTab extends ConsumerStatefulWidget {
  final AudioService audioService;

  const NotesTab({super.key, required this.audioService});

  @override
  ConsumerState<NotesTab> createState() => _NotesTabState();
}

class _NotesTabState extends ConsumerState<NotesTab> {
  final TextEditingController _searchController = TextEditingController();
  String _sortBy = 'date';
  bool _showArchived = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final memosAsync = ref.watch(memosProvider);
    final searchQuery = ref.watch(memoSearchQueryProvider);

    return memosAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(memosProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
      data: (allMemos) {
        // Filter for text and mixed memos
        var memos = allMemos.where((memo) => 
          memo.type == MemoType.text || memo.type == MemoType.mixed
        ).toList();

        // Apply search filter
        if (searchQuery.isNotEmpty) {
          memos = memos.where((memo) =>
            memo.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
            (memo.description?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
            (memo.richContent?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
            memo.tags.any((tag) => tag.toLowerCase().contains(searchQuery.toLowerCase()))
          ).toList();
        }

        // Apply archive filter
        if (!_showArchived) {
          memos = memos.where((memo) => !memo.isArchived).toList();
        }

        // Apply sorting
        _sortMemos(memos);

        return Scaffold(
          body: Column(
            children: [
              // Search and filter section
              _buildSearchAndFilters(),
              
              // Content
              Expanded(
                child: memos.isEmpty
                    ? _buildEmptyState()
                    : _buildMemosList(memos),
              ),
            ],
          ),
          floatingActionButton: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Mixed memo FAB
              FloatingActionButton(
                heroTag: "mixed_memo",
                onPressed: () => _createMemo(MemoType.mixed),
                backgroundColor: Colors.green,
                child: const Icon(Icons.auto_awesome),
              ),
              const SizedBox(height: 8),
              // Text memo FAB
              FloatingActionButton(
                heroTag: "text_memo",
                onPressed: () => _createMemo(MemoType.text),
                child: const Icon(Icons.note_add),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search notes...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        ref.read(memoSearchQueryProvider.notifier).state = '';
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.background,
            ),
            onChanged: (value) {
              ref.read(memoSearchQueryProvider.notifier).state = value;
            },
          ),
          const SizedBox(height: 12),
          
          // Filter row
          Row(
            children: [
              // Sort dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  decoration: const InputDecoration(
                    labelText: 'Sort by',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'date', child: Text('Date')),
                    DropdownMenuItem(value: 'title', child: Text('Title')),
                    DropdownMenuItem(value: 'type', child: Text('Type')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),
              
              // Show archived toggle
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Show Archived'),
                  value: _showArchived,
                  onChanged: (value) {
                    setState(() {
                      _showArchived = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.note,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No notes yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to create your first note',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemosList(List<MemoModel> memos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: memos.length,
      itemBuilder: (context, index) {
        final memo = memos[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getMemoTypeColor(memo.type).withValues(alpha: 0.1),
              child: Icon(
                _getMemoTypeIcon(memo.type),
                color: _getMemoTypeColor(memo.type),
              ),
            ),
            title: Row(
              children: [
                Expanded(child: Text(memo.title)),
                if (memo.isPinned)
                  const Icon(Icons.push_pin, size: 16, color: Colors.orange),
                if (memo.isArchived)
                  const Icon(Icons.archive, size: 16, color: Colors.grey),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (memo.description != null)
                  Text(
                    memo.description!,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.access_time, size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate(memo.createdAt),
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    if (memo.category != null) ...[
                      const SizedBox(width: 16),
                      Icon(Icons.folder, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        memo.category!,
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ],
                ),
                if (memo.tags.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 4,
                    children: memo.tags.take(3).map((tag) => Chip(
                      label: Text(tag),
                      labelStyle: const TextStyle(fontSize: 10),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    )).toList(),
                  ),
                ],
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('Edit'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: memo.isPinned ? 'unpin' : 'pin',
                  child: Row(
                    children: [
                      Icon(memo.isPinned ? Icons.push_pin_outlined : Icons.push_pin),
                      const SizedBox(width: 8),
                      Text(memo.isPinned ? 'Unpin' : 'Pin'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: memo.isArchived ? 'unarchive' : 'archive',
                  child: Row(
                    children: [
                      Icon(memo.isArchived ? Icons.unarchive : Icons.archive),
                      const SizedBox(width: 8),
                      Text(memo.isArchived ? 'Unarchive' : 'Archive'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              onSelected: (value) => _handleMenuAction(value, memo),
            ),
            onTap: () => _editMemo(memo),
          ),
        );
      },
    );
  }

  void _sortMemos(List<MemoModel> memos) {
    switch (_sortBy) {
      case 'date':
        memos.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'title':
        memos.sort((a, b) => a.title.compareTo(b.title));
        break;
      case 'type':
        memos.sort((a, b) => a.type.name.compareTo(b.type.name));
        break;
    }
  }

  void _createMemo(MemoType type) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateMemoScreen(initialType: type),
      ),
    );
  }

  void _editMemo(MemoModel memo) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateMemoScreen(editingMemo: memo),
      ),
    );
  }

  void _handleMenuAction(String action, MemoModel memo) {
    switch (action) {
      case 'edit':
        _editMemo(memo);
        break;
      case 'pin':
      case 'unpin':
        ref.read(memosProvider.notifier).updateMemo(
          memo.copyWith(isPinned: !memo.isPinned),
        );
        break;
      case 'archive':
      case 'unarchive':
        ref.read(memosProvider.notifier).updateMemo(
          memo.copyWith(isArchived: !memo.isArchived),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(memo);
        break;
    }
  }

  void _showDeleteConfirmation(MemoModel memo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Note'),
        content: Text('Are you sure you want to delete "${memo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(memosProvider.notifier).deleteMemo(memo.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Note deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  IconData _getMemoTypeIcon(MemoType type) {
    switch (type) {
      case MemoType.text:
        return Icons.text_snippet;
      case MemoType.mixed:
        return Icons.auto_awesome;
      default:
        return Icons.note;
    }
  }

  Color _getMemoTypeColor(MemoType type) {
    switch (type) {
      case MemoType.text:
        return Colors.blue;
      case MemoType.mixed:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
