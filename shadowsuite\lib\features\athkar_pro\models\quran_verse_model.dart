class QuranVerseModel {
  final String id;
  final int surahNumber;
  final String surahName;
  final String surahNameArabic;
  final int verseNumber;
  final String arabicText;
  final String transliteration;
  final String translation;
  final String? tafsir;
  final List<String> tags;
  final bool isFavorite;
  final DateTime createdAt;
  final DateTime? lastReadAt;
  final int readCount;

  const QuranVerseModel({
    required this.id,
    required this.surahNumber,
    required this.surahName,
    required this.surahNameArabic,
    required this.verseNumber,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    this.tafsir,
    this.tags = const [],
    this.isFavorite = false,
    required this.createdAt,
    this.lastReadAt,
    this.readCount = 0,
  });

  QuranVerseModel copyWith({
    String? id,
    int? surahNumber,
    String? surahName,
    String? surahNameArabic,
    int? verseNumber,
    String? arabicText,
    String? transliteration,
    String? translation,
    String? tafsir,
    List<String>? tags,
    bool? isFavorite,
    DateTime? createdAt,
    DateTime? lastReadAt,
    int? readCount,
  }) {
    return QuranVerseModel(
      id: id ?? this.id,
      surahNumber: surahNumber ?? this.surahNumber,
      surahName: surahName ?? this.surahName,
      surahNameArabic: surahNameArabic ?? this.surahNameArabic,
      verseNumber: verseNumber ?? this.verseNumber,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      tafsir: tafsir ?? this.tafsir,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
      createdAt: createdAt ?? this.createdAt,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      readCount: readCount ?? this.readCount,
    );
  }

  String get reference => '$surahName $verseNumber';
  String get fullReference => '$surahName ($surahNameArabic) - Verse $verseNumber';

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surahNumber': surahNumber,
      'surahName': surahName,
      'surahNameArabic': surahNameArabic,
      'verseNumber': verseNumber,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'tafsir': tafsir,
      'tags': tags,
      'isFavorite': isFavorite,
      'createdAt': createdAt.toIso8601String(),
      'lastReadAt': lastReadAt?.toIso8601String(),
      'readCount': readCount,
    };
  }

  factory QuranVerseModel.fromJson(Map<String, dynamic> json) {
    return QuranVerseModel(
      id: json['id'],
      surahNumber: json['surahNumber'],
      surahName: json['surahName'],
      surahNameArabic: json['surahNameArabic'],
      verseNumber: json['verseNumber'],
      arabicText: json['arabicText'],
      transliteration: json['transliteration'],
      translation: json['translation'],
      tafsir: json['tafsir'],
      tags: List<String>.from(json['tags'] ?? []),
      isFavorite: json['isFavorite'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      lastReadAt: json['lastReadAt'] != null 
          ? DateTime.parse(json['lastReadAt']) 
          : null,
      readCount: json['readCount'] ?? 0,
    );
  }

  @override
  String toString() {
    return 'QuranVerseModel(id: $id, surah: $surahName, verse: $verseNumber)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuranVerseModel &&
        other.id == id &&
        other.surahNumber == surahNumber &&
        other.verseNumber == verseNumber;
  }

  @override
  int get hashCode {
    return id.hashCode ^ surahNumber.hashCode ^ verseNumber.hashCode;
  }
}

class SupplicationModel {
  final String id;
  final String title;
  final String arabicText;
  final String transliteration;
  final String translation;
  final String category;
  final String? occasion;
  final String? source;
  final List<String> benefits;
  final bool isFavorite;
  final DateTime createdAt;
  final DateTime? lastReadAt;
  final int readCount;

  const SupplicationModel({
    required this.id,
    required this.title,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.category,
    this.occasion,
    this.source,
    this.benefits = const [],
    this.isFavorite = false,
    required this.createdAt,
    this.lastReadAt,
    this.readCount = 0,
  });

  SupplicationModel copyWith({
    String? id,
    String? title,
    String? arabicText,
    String? transliteration,
    String? translation,
    String? category,
    String? occasion,
    String? source,
    List<String>? benefits,
    bool? isFavorite,
    DateTime? createdAt,
    DateTime? lastReadAt,
    int? readCount,
  }) {
    return SupplicationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      category: category ?? this.category,
      occasion: occasion ?? this.occasion,
      source: source ?? this.source,
      benefits: benefits ?? this.benefits,
      isFavorite: isFavorite ?? this.isFavorite,
      createdAt: createdAt ?? this.createdAt,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      readCount: readCount ?? this.readCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'category': category,
      'occasion': occasion,
      'source': source,
      'benefits': benefits,
      'isFavorite': isFavorite,
      'createdAt': createdAt.toIso8601String(),
      'lastReadAt': lastReadAt?.toIso8601String(),
      'readCount': readCount,
    };
  }

  factory SupplicationModel.fromJson(Map<String, dynamic> json) {
    return SupplicationModel(
      id: json['id'],
      title: json['title'],
      arabicText: json['arabicText'],
      transliteration: json['transliteration'],
      translation: json['translation'],
      category: json['category'],
      occasion: json['occasion'],
      source: json['source'],
      benefits: List<String>.from(json['benefits'] ?? []),
      isFavorite: json['isFavorite'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      lastReadAt: json['lastReadAt'] != null 
          ? DateTime.parse(json['lastReadAt']) 
          : null,
      readCount: json['readCount'] ?? 0,
    );
  }

  @override
  String toString() {
    return 'SupplicationModel(id: $id, title: $title, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SupplicationModel &&
        other.id == id &&
        other.title == title &&
        other.arabicText == arabicText;
  }

  @override
  int get hashCode {
    return id.hashCode ^ title.hashCode ^ arabicText.hashCode;
  }
}
