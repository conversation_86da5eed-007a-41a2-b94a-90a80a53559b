import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/quran_model.dart';

// Bookmarks provider
final bookmarksProvider = StateNotifierProvider<BookmarksNotifier, AsyncValue<List<BookmarkModel>>>((ref) {
  return BookmarksNotifier();
});

// Verse bookmarks provider (for checking if a verse is bookmarked)
final verseBookmarkProvider = Provider.family<bool, String>((ref, verseKey) {
  final bookmarksAsync = ref.watch(bookmarksProvider);
  
  return bookmarksAsync.when(
    data: (bookmarks) => bookmarks.any((bookmark) => 
      '${bookmark.surahNumber}:${bookmark.verseNumber}' == verseKey),
    loading: () => false,
    error: (_, __) => false,
  );
});

// Surah bookmarks provider (get all bookmarks for a specific surah)
final surahBookmarksProvider = Provider.family<List<BookmarkModel>, int>((ref, surahNumber) {
  final bookmarksAsync = ref.watch(bookmarksProvider);
  
  return bookmarksAsync.when(
    data: (bookmarks) => bookmarks.where((bookmark) => 
      bookmark.surahNumber == surahNumber).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

class BookmarksNotifier extends StateNotifier<AsyncValue<List<BookmarkModel>>> {
  BookmarksNotifier() : super(const AsyncValue.loading()) {
    loadBookmarks();
  }

  Future<void> loadBookmarks() async {
    try {
      state = const AsyncValue.loading();
      
      // Load sample bookmarks for demo
      final bookmarks = _getSampleBookmarks();
      
      state = AsyncValue.data(bookmarks);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addBookmark(BookmarkModel bookmark) async {
    state.whenData((bookmarks) {
      // Check if bookmark already exists
      final exists = bookmarks.any((b) => 
        b.surahNumber == bookmark.surahNumber && 
        b.verseNumber == bookmark.verseNumber);
      
      if (!exists) {
        state = AsyncValue.data([bookmark, ...bookmarks]);
      }
    });
  }

  Future<void> removeBookmark(String bookmarkId) async {
    state.whenData((bookmarks) {
      final updatedList = bookmarks.where((bookmark) => bookmark.id != bookmarkId).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> removeBookmarkByVerse(int surahNumber, int verseNumber) async {
    state.whenData((bookmarks) {
      final updatedList = bookmarks.where((bookmark) => 
        !(bookmark.surahNumber == surahNumber && bookmark.verseNumber == verseNumber)
      ).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> updateBookmark(BookmarkModel updatedBookmark) async {
    state.whenData((bookmarks) {
      final updatedList = bookmarks.map((bookmark) {
        return bookmark.id == updatedBookmark.id ? updatedBookmark : bookmark;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> toggleVerseBookmark(int surahNumber, int verseNumber, {String? note, List<String>? tags}) async {
    state.whenData((bookmarks) {
      final existingBookmark = bookmarks.firstWhere(
        (bookmark) => bookmark.surahNumber == surahNumber && bookmark.verseNumber == verseNumber,
        orElse: () => BookmarkModel(
          id: '',
          userId: '',
          surahNumber: -1,
          verseNumber: -1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      if (existingBookmark.id.isNotEmpty) {
        // Remove existing bookmark
        removeBookmark(existingBookmark.id);
      } else {
        // Add new bookmark
        final newBookmark = BookmarkModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          userId: 'demo-user-id',
          surahNumber: surahNumber,
          verseNumber: verseNumber,
          note: note,
          tags: tags ?? [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        addBookmark(newBookmark);
      }
    });
  }

  List<BookmarkModel> _getSampleBookmarks() {
    final now = DateTime.now();
    const userId = 'demo-user-id';
    
    return [
      BookmarkModel(
        id: 'bookmark_1',
        userId: userId,
        surahNumber: 1,
        verseNumber: 1,
        note: 'Beautiful opening verse',
        tags: ['bismillah', 'opening'],
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
      BookmarkModel(
        id: 'bookmark_2',
        userId: userId,
        surahNumber: 1,
        verseNumber: 2,
        note: 'Praise to Allah',
        tags: ['praise', 'gratitude'],
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 3)),
      ),
      BookmarkModel(
        id: 'bookmark_3',
        userId: userId,
        surahNumber: 2,
        verseNumber: 255,
        note: 'Ayat al-Kursi - Throne Verse',
        tags: ['ayat-al-kursi', 'protection', 'important'],
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
    ];
  }
}

// Bookmark statistics provider
final bookmarkStatsProvider = Provider<BookmarkStatistics>((ref) {
  final bookmarksAsync = ref.watch(bookmarksProvider);
  
  return bookmarksAsync.when(
    data: (bookmarks) {
      final totalBookmarks = bookmarks.length;
      final uniqueSurahs = bookmarks.map((b) => b.surahNumber).toSet().length;
      final recentBookmarks = bookmarks.where((b) => 
        b.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 7)))
      ).length;
      final taggedBookmarks = bookmarks.where((b) => b.tags.isNotEmpty).length;
      
      return BookmarkStatistics(
        totalBookmarks: totalBookmarks,
        uniqueSurahs: uniqueSurahs,
        recentBookmarks: recentBookmarks,
        taggedBookmarks: taggedBookmarks,
      );
    },
    loading: () => const BookmarkStatistics(
      totalBookmarks: 0,
      uniqueSurahs: 0,
      recentBookmarks: 0,
      taggedBookmarks: 0,
    ),
    error: (_, __) => const BookmarkStatistics(
      totalBookmarks: 0,
      uniqueSurahs: 0,
      recentBookmarks: 0,
      taggedBookmarks: 0,
    ),
  );
});

class BookmarkStatistics {
  final int totalBookmarks;
  final int uniqueSurahs;
  final int recentBookmarks;
  final int taggedBookmarks;

  const BookmarkStatistics({
    required this.totalBookmarks,
    required this.uniqueSurahs,
    required this.recentBookmarks,
    required this.taggedBookmarks,
  });
}

// Bookmark search provider
final bookmarkSearchProvider = Provider.family<List<BookmarkModel>, String>((ref, query) {
  final bookmarksAsync = ref.watch(bookmarksProvider);
  
  return bookmarksAsync.when(
    data: (bookmarks) {
      if (query.isEmpty) return bookmarks;
      
      final lowerQuery = query.toLowerCase();
      return bookmarks.where((bookmark) {
        return (bookmark.note?.toLowerCase().contains(lowerQuery) ?? false) ||
               bookmark.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Bookmark tags provider (get all unique tags)
final bookmarkTagsProvider = Provider<List<String>>((ref) {
  final bookmarksAsync = ref.watch(bookmarksProvider);
  
  return bookmarksAsync.when(
    data: (bookmarks) {
      final allTags = <String>{};
      for (final bookmark in bookmarks) {
        allTags.addAll(bookmark.tags);
      }
      final tagsList = allTags.toList();
      tagsList.sort();
      return tagsList;
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Bookmarks by tag provider
final bookmarksByTagProvider = Provider.family<List<BookmarkModel>, String>((ref, tag) {
  final bookmarksAsync = ref.watch(bookmarksProvider);
  
  return bookmarksAsync.when(
    data: (bookmarks) => bookmarks.where((bookmark) => 
      bookmark.tags.contains(tag)).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});
