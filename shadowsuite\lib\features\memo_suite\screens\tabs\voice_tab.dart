import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:audioplayers/audioplayers.dart'; // Temporarily disabled
import '../../../../core/models/memo_model.dart';
import '../../providers/memo_provider.dart';
import '../../services/audio_service.dart';
import '../create_voice_memo_screen.dart';

class VoiceTab extends ConsumerStatefulWidget {
  final AudioService audioService;

  const VoiceTab({super.key, required this.audioService});

  @override
  ConsumerState<VoiceTab> createState() => _VoiceTabState();
}

class _VoiceTabState extends ConsumerState<VoiceTab> {
  final TextEditingController _searchController = TextEditingController();
  String _sortBy = 'date';
  String? _currentlyPlaying;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final memosAsync = ref.watch(memosProvider);
    final searchQuery = ref.watch(memoSearchQueryProvider);
    final recordingState = ref.watch(recordingStateProvider);

    return memosAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(memosProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
      data: (allMemos) {
        // Filter for voice memos
        var memos = allMemos
            .where((memo) => memo.type == MemoType.voice)
            .toList();

        // Apply search filter
        if (searchQuery.isNotEmpty) {
          memos = memos
              .where(
                (memo) =>
                    memo.title.toLowerCase().contains(
                      searchQuery.toLowerCase(),
                    ) ||
                    (memo.description?.toLowerCase().contains(
                          searchQuery.toLowerCase(),
                        ) ??
                        false) ||
                    (memo.transcription?.toLowerCase().contains(
                          searchQuery.toLowerCase(),
                        ) ??
                        false),
              )
              .toList();
        }

        // Apply sorting
        _sortMemos(memos);

        return Scaffold(
          body: Column(
            children: [
              // Search and filter section
              _buildSearchAndFilters(),

              // Recording controls section
              if (recordingState == RecordingState.recording)
                _buildRecordingControls(),

              // Content
              Expanded(
                child: memos.isEmpty
                    ? _buildEmptyState()
                    : _buildVoiceMemosList(memos),
              ),
            ],
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: recordingState == RecordingState.recording
                ? null
                : () => _createVoiceMemo(),
            backgroundColor: recordingState == RecordingState.recording
                ? Colors.grey
                : Colors.orange,
            child: const Icon(Icons.mic),
          ),
        );
      },
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search voice memos...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        ref.read(memoSearchQueryProvider.notifier).state = '';
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.background,
            ),
            onChanged: (value) {
              ref.read(memoSearchQueryProvider.notifier).state = value;
            },
          ),
          const SizedBox(height: 12),

          // Sort dropdown
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  decoration: const InputDecoration(
                    labelText: 'Sort by',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'date', child: Text('Date')),
                    DropdownMenuItem(value: 'title', child: Text('Title')),
                    DropdownMenuItem(
                      value: 'duration',
                      child: Text('Duration'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecordingControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: Colors.red.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.fiber_manual_record, color: Colors.red),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Recording in progress...',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              // Stop recording will be handled by parent
            },
            icon: const Icon(Icons.stop),
            label: const Text('Stop'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.mic_none, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No voice memos yet',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the microphone to record your first voice memo',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceMemosList(List<MemoModel> memos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: memos.length,
      itemBuilder: (context, index) {
        final memo = memos[index];
        final isPlaying = _currentlyPlaying == memo.id;

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.orange.withValues(alpha: 0.1),
              child: Icon(
                isPlaying ? Icons.volume_up : Icons.mic,
                color: isPlaying ? Colors.green : Colors.orange,
              ),
            ),
            title: Text(memo.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (memo.description != null)
                  Text(
                    memo.description!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.access_time, size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      _formatDuration(memo.duration),
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.calendar_today,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate(memo.createdAt),
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
                if (memo.transcription != null &&
                    memo.transcription!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      memo.transcription!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Play/Pause button
                IconButton(
                  icon: Icon(
                    isPlaying ? Icons.pause : Icons.play_arrow,
                    color: isPlaying ? Colors.red : Colors.green,
                  ),
                  onPressed: () => _togglePlayback(memo),
                ),
                // Menu button
                PopupMenuButton(
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'share',
                      child: Row(
                        children: [
                          Icon(Icons.share),
                          SizedBox(width: 8),
                          Text('Share'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) => _handleMenuAction(value, memo),
                ),
              ],
            ),
            onTap: () => _showVoiceMemoDetails(memo),
          ),
        );
      },
    );
  }

  void _sortMemos(List<MemoModel> memos) {
    switch (_sortBy) {
      case 'date':
        memos.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'title':
        memos.sort((a, b) => a.title.compareTo(b.title));
        break;
      case 'duration':
        memos.sort((a, b) => b.duration.compareTo(a.duration));
        break;
    }
  }

  void _createVoiceMemo() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CreateVoiceMemoScreen()),
    );
  }

  Future<void> _togglePlayback(MemoModel memo) async {
    if (_currentlyPlaying == memo.id) {
      // Stop playback
      await widget.audioService.stopPlayback();
      setState(() {
        _currentlyPlaying = null;
      });
    } else {
      // Start playback
      if (memo.audioFilePath != null) {
        try {
          // Check if audio file is accessible before playing
          final isAccessible = await widget.audioService.isAudioFileAccessible(
            memo.audioFilePath!,
          );
          if (!isAccessible) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Audio file not found: ${memo.audioFilePath}'),
                  backgroundColor: Colors.orange,
                  action: SnackBarAction(
                    label: 'Details',
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('Audio File Issue'),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'The audio file for this voice memo could not be found.',
                              ),
                              const SizedBox(height: 8),
                              const Text('File path:'),
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  memo.audioFilePath!,
                                  style: const TextStyle(
                                    fontFamily: 'monospace',
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('OK'),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              );
            }
            return;
          }

          await widget.audioService.playAudio(memo.audioFilePath!);
          setState(() {
            _currentlyPlaying = memo.id;
          });

          // Listen to player state changes for better control
          widget.audioService.playerStateStream.listen((state) {
            if (state == PlayerState.completed &&
                _currentlyPlaying == memo.id) {
              setState(() {
                _currentlyPlaying = null;
              });
            }
          });
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error playing audio: ${e.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No audio file associated with this memo'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _handleMenuAction(String action, MemoModel memo) {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CreateVoiceMemoScreen(editingMemo: memo),
          ),
        );
        break;
      case 'share':
        // TODO: Implement sharing functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Share feature coming soon!')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(memo);
        break;
    }
  }

  void _showVoiceMemoDetails(MemoModel memo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(memo.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (memo.description != null) ...[
              Text(memo.description!),
              const SizedBox(height: 16),
            ],
            Row(
              children: [
                const Icon(Icons.access_time, size: 16),
                const SizedBox(width: 8),
                Text('Duration: ${_formatDuration(memo.duration)}'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 8),
                Text('Recorded: ${_formatDate(memo.createdAt)}'),
              ],
            ),
            if (memo.transcription != null &&
                memo.transcription!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'Transcription:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(memo.transcription!),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _togglePlayback(memo);
            },
            icon: const Icon(Icons.play_arrow),
            label: const Text('Play'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(MemoModel memo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Voice Memo'),
        content: Text('Are you sure you want to delete "${memo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(memosProvider.notifier).deleteMemo(memo.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Voice memo deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
