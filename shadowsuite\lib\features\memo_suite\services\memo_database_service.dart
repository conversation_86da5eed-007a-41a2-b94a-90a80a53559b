import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../../../core/models/memo_model.dart';
import '../../../core/models/todo_item_model.dart';
import '../../../core/models/file_attachment_model.dart';
import '../../../core/services/database_service.dart';

class MemoDatabaseService {
  final DatabaseService _databaseService = DatabaseService();

  Future<Database> get _database async => await _databaseService.database;

  // MEMO CRUD OPERATIONS

  /// Create a new memo with its todos and attachments
  Future<String> createMemo(MemoModel memo) async {
    final db = await _database;
    
    await db.transaction((txn) async {
      // Insert memo
      await txn.insert('memos', _memoToMap(memo));
      
      // Insert todo items
      for (final todo in memo.todoItems) {
        await txn.insert('todo_items', _todoToMap(todo, memo.id));
      }
      
      // Insert file attachments
      for (final attachment in memo.attachments) {
        await txn.insert('file_attachments', _attachmentToMap(attachment, memo.id));
      }
    });
    
    return memo.id;
  }

  /// Get all memos for a user
  Future<List<MemoModel>> getAllMemos(String userId) async {
    final db = await _database;
    
    final memoMaps = await db.query(
      'memos',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );
    
    final memos = <MemoModel>[];
    
    for (final memoMap in memoMaps) {
      final memo = await _buildMemoFromMap(memoMap);
      memos.add(memo);
    }
    
    return memos;
  }

  /// Get a specific memo by ID
  Future<MemoModel?> getMemoById(String memoId) async {
    final db = await _database;
    
    final memoMaps = await db.query(
      'memos',
      where: 'id = ?',
      whereArgs: [memoId],
      limit: 1,
    );
    
    if (memoMaps.isEmpty) return null;
    
    return await _buildMemoFromMap(memoMaps.first);
  }

  /// Update an existing memo
  Future<void> updateMemo(MemoModel memo) async {
    final db = await _database;
    
    await db.transaction((txn) async {
      // Update memo
      await txn.update(
        'memos',
        _memoToMap(memo),
        where: 'id = ?',
        whereArgs: [memo.id],
      );
      
      // Delete existing todos and attachments
      await txn.delete('todo_items', where: 'memo_id = ?', whereArgs: [memo.id]);
      await txn.delete('file_attachments', where: 'memo_id = ?', whereArgs: [memo.id]);
      
      // Insert updated todos
      for (final todo in memo.todoItems) {
        await txn.insert('todo_items', _todoToMap(todo, memo.id));
      }
      
      // Insert updated attachments
      for (final attachment in memo.attachments) {
        await txn.insert('file_attachments', _attachmentToMap(attachment, memo.id));
      }
    });
  }

  /// Delete a memo and all its related data
  Future<void> deleteMemo(String memoId) async {
    final db = await _database;
    
    await db.transaction((txn) async {
      // Delete todos and attachments (foreign key constraints will handle this)
      await txn.delete('todo_items', where: 'memo_id = ?', whereArgs: [memoId]);
      await txn.delete('file_attachments', where: 'memo_id = ?', whereArgs: [memoId]);
      
      // Delete memo
      await txn.delete('memos', where: 'id = ?', whereArgs: [memoId]);
    });
  }

  /// Search memos by query
  Future<List<MemoModel>> searchMemos(String userId, String query) async {
    final db = await _database;
    
    final memoMaps = await db.query(
      'memos',
      where: '''
        user_id = ? AND (
          title LIKE ? OR 
          description LIKE ? OR 
          rich_content LIKE ? OR 
          transcription LIKE ? OR
          tags LIKE ?
        )
      ''',
      whereArgs: [
        userId,
        '%$query%',
        '%$query%',
        '%$query%',
        '%$query%',
        '%$query%',
      ],
      orderBy: 'created_at DESC',
    );
    
    final memos = <MemoModel>[];
    
    for (final memoMap in memoMaps) {
      final memo = await _buildMemoFromMap(memoMap);
      memos.add(memo);
    }
    
    return memos;
  }

  /// Get memos by type
  Future<List<MemoModel>> getMemosByType(String userId, MemoType type) async {
    final db = await _database;
    
    final memoMaps = await db.query(
      'memos',
      where: 'user_id = ? AND type = ?',
      whereArgs: [userId, type.name],
      orderBy: 'created_at DESC',
    );
    
    final memos = <MemoModel>[];
    
    for (final memoMap in memoMaps) {
      final memo = await _buildMemoFromMap(memoMap);
      memos.add(memo);
    }
    
    return memos;
  }

  /// Get memos by category
  Future<List<MemoModel>> getMemosByCategory(String userId, String category) async {
    final db = await _database;
    
    final memoMaps = await db.query(
      'memos',
      where: 'user_id = ? AND category = ?',
      whereArgs: [userId, category],
      orderBy: 'created_at DESC',
    );
    
    final memos = <MemoModel>[];
    
    for (final memoMap in memoMaps) {
      final memo = await _buildMemoFromMap(memoMap);
      memos.add(memo);
    }
    
    return memos;
  }

  /// Get unsynced memos
  Future<List<MemoModel>> getUnsyncedMemos(String userId) async {
    final db = await _database;
    
    final memoMaps = await db.query(
      'memos',
      where: 'user_id = ? AND is_synced = 0',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );
    
    final memos = <MemoModel>[];
    
    for (final memoMap in memoMaps) {
      final memo = await _buildMemoFromMap(memoMap);
      memos.add(memo);
    }
    
    return memos;
  }

  /// Mark memo as synced
  Future<void> markMemoAsSynced(String memoId) async {
    final db = await _database;
    
    await db.update(
      'memos',
      {'is_synced': 1},
      where: 'id = ?',
      whereArgs: [memoId],
    );
  }

  // HELPER METHODS

  /// Convert MemoModel to database map
  Map<String, dynamic> _memoToMap(MemoModel memo) {
    return {
      'id': memo.id,
      'user_id': memo.userId,
      'title': memo.title,
      'description': memo.description,
      'rich_content': memo.richContent,
      'transcription': memo.transcription,
      'audio_file_path': memo.audioFilePath,
      'audio_url': memo.audioUrl,
      'duration': memo.duration,
      'type': memo.type.name,
      'category': memo.category,
      'is_pinned': memo.isPinned ? 1 : 0,
      'is_archived': memo.isArchived ? 1 : 0,
      'created_at': memo.createdAt.toIso8601String(),
      'updated_at': memo.updatedAt.toIso8601String(),
      'is_synced': memo.isSynced ? 1 : 0,
      'tags': jsonEncode(memo.tags),
    };
  }

  /// Convert TodoItemModel to database map
  Map<String, dynamic> _todoToMap(TodoItemModel todo, String memoId) {
    return {
      'id': todo.id,
      'memo_id': memoId,
      'text': todo.text,
      'is_completed': todo.isCompleted ? 1 : 0,
      'due_date': todo.dueDate?.toIso8601String(),
      'priority': todo.priority.name,
      'created_at': todo.createdAt.toIso8601String(),
      'updated_at': todo.updatedAt.toIso8601String(),
    };
  }

  /// Convert FileAttachmentModel to database map
  Map<String, dynamic> _attachmentToMap(FileAttachmentModel attachment, String memoId) {
    return {
      'id': attachment.id,
      'memo_id': memoId,
      'name': attachment.name,
      'path': attachment.path,
      'url': attachment.url,
      'mime_type': attachment.mimeType,
      'size': attachment.size,
      'uploaded_at': attachment.uploadedAt.toIso8601String(),
    };
  }

  /// Build MemoModel from database map with todos and attachments
  Future<MemoModel> _buildMemoFromMap(Map<String, dynamic> map) async {
    final db = await _database;
    
    // Get todos
    final todoMaps = await db.query(
      'todo_items',
      where: 'memo_id = ?',
      whereArgs: [map['id']],
      orderBy: 'created_at ASC',
    );
    
    final todos = todoMaps.map((todoMap) => TodoItemModel(
      id: todoMap['id'] as String,
      text: todoMap['text'] as String,
      isCompleted: (todoMap['is_completed'] as int) == 1,
      dueDate: todoMap['due_date'] != null 
          ? DateTime.parse(todoMap['due_date'] as String) 
          : null,
      priority: Priority.values.firstWhere(
        (p) => p.name == todoMap['priority'],
        orElse: () => Priority.medium,
      ),
      createdAt: DateTime.parse(todoMap['created_at'] as String),
      updatedAt: DateTime.parse(todoMap['updated_at'] as String),
    )).toList();
    
    // Get attachments
    final attachmentMaps = await db.query(
      'file_attachments',
      where: 'memo_id = ?',
      whereArgs: [map['id']],
      orderBy: 'uploaded_at ASC',
    );
    
    final attachments = attachmentMaps.map((attachmentMap) => FileAttachmentModel(
      id: attachmentMap['id'] as String,
      name: attachmentMap['name'] as String,
      path: attachmentMap['path'] as String,
      url: attachmentMap['url'] as String?,
      mimeType: attachmentMap['mime_type'] as String,
      size: attachmentMap['size'] as int,
      uploadedAt: DateTime.parse(attachmentMap['uploaded_at'] as String),
    )).toList();
    
    // Parse tags
    final tagsJson = map['tags'] as String?;
    final tags = tagsJson != null && tagsJson.isNotEmpty
        ? List<String>.from(jsonDecode(tagsJson))
        : <String>[];
    
    return MemoModel(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      title: map['title'] as String,
      description: map['description'] as String?,
      richContent: map['rich_content'] as String?,
      transcription: map['transcription'] as String?,
      audioFilePath: map['audio_file_path'] as String?,
      audioUrl: map['audio_url'] as String?,
      duration: map['duration'] as int,
      type: MemoType.values.firstWhere(
        (t) => t.name == map['type'],
        orElse: () => MemoType.text,
      ),
      todoItems: todos,
      attachments: attachments,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      isSynced: (map['is_synced'] as int) == 1,
      tags: tags,
      category: map['category'] as String?,
      isPinned: (map['is_pinned'] as int) == 1,
      isArchived: (map['is_archived'] as int) == 1,
    );
  }
}
