<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android"
    name=":path_provider_android"
    type="LIBRARY"
    maven="io.flutter.plugins.pathprovider:path_provider_android:1.0-SNAPSHOT"
    agpVersion="8.7.3"
    buildFolder="D:\apps\android\shadowsuite\build\path_provider_android"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      disable="AndroidGradlePluginVersion,InvalidPackage,GradleDependency,NewerVersionAvailable"
      abortOnError="true"
      absolutePaths="true"
      checkAllWarnings="true"
      warningsAsErrors="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="AndroidGradlePluginVersion"
        severity="IGNORE" />
      <severity
        id="GradleDependency"
        severity="IGNORE" />
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
      <severity
        id="NewerVersionAvailable"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
