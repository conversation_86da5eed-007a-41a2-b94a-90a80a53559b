import 'package:json_annotation/json_annotation.dart';

part 'file_attachment_model.g.dart';

@JsonSerializable()
class FileAttachmentModel {
  final String id;
  final String name;
  final String path;
  final String? url;
  final String mimeType;
  final int size;
  final DateTime uploadedAt;

  const FileAttachmentModel({
    required this.id,
    required this.name,
    required this.path,
    this.url,
    required this.mimeType,
    required this.size,
    required this.uploadedAt,
  });

  factory FileAttachmentModel.fromJson(Map<String, dynamic> json) =>
      _$FileAttachmentModelFromJson(json);

  Map<String, dynamic> toJson() => _$FileAttachmentModelToJson(this);

  FileAttachmentModel copyWith({
    String? id,
    String? name,
    String? path,
    String? url,
    String? mimeType,
    int? size,
    DateTime? uploadedAt,
  }) {
    return FileAttachmentModel(
      id: id ?? this.id,
      name: name ?? this.name,
      path: path ?? this.path,
      url: url ?? this.url,
      mimeType: mimeType ?? this.mimeType,
      size: size ?? this.size,
      uploadedAt: uploadedAt ?? this.uploadedAt,
    );
  }

  String get formattedSize {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  bool get isImage => mimeType.startsWith('image/');
  bool get isAudio => mimeType.startsWith('audio/');
  bool get isVideo => mimeType.startsWith('video/');
  bool get isDocument => mimeType.contains('pdf') || 
                        mimeType.contains('document') || 
                        mimeType.contains('text') ||
                        mimeType.contains('spreadsheet') ||
                        mimeType.contains('presentation');

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FileAttachmentModel &&
        other.id == id &&
        other.name == name &&
        other.path == path &&
        other.url == url &&
        other.mimeType == mimeType &&
        other.size == size &&
        other.uploadedAt == uploadedAt;
  }

  @override
  int get hashCode => id.hashCode;
}
