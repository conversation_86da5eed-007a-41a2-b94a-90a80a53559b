import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/money_flow_provider.dart';
import '../models/account_model.dart';
import '../models/transaction_model.dart';
import 'create_transaction_screen.dart';

class AccountTransactionsScreen extends ConsumerStatefulWidget {
  final AccountModel account;

  const AccountTransactionsScreen({
    super.key,
    required this.account,
  });

  @override
  ConsumerState<AccountTransactionsScreen> createState() => _AccountTransactionsScreenState();
}

class _AccountTransactionsScreenState extends ConsumerState<AccountTransactionsScreen> {
  String _sortBy = 'date'; // date, amount, title
  bool _sortAscending = false;
  TransactionType? _filterType;

  @override
  Widget build(BuildContext context) {
    final transactionsAsync = ref.watch(transactionsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.account.name),
            Text(
              '\$${widget.account.balance.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: widget.account.balance >= 0 ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() {
                if (_sortBy == value) {
                  _sortAscending = !_sortAscending;
                } else {
                  _sortBy = value;
                  _sortAscending = false;
                }
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'date', child: Text('Sort by Date')),
              const PopupMenuItem(value: 'amount', child: Text('Sort by Amount')),
              const PopupMenuItem(value: 'title', child: Text('Sort by Title')),
            ],
          ),
          PopupMenuButton<TransactionType?>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() {
                _filterType = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: null, child: Text('All Transactions')),
              const PopupMenuItem(value: TransactionType.income, child: Text('Income Only')),
              const PopupMenuItem(value: TransactionType.expense, child: Text('Expenses Only')),
              const PopupMenuItem(value: TransactionType.transfer, child: Text('Transfers Only')),
            ],
          ),
        ],
      ),
      body: transactionsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(child: Text('Error: $error')),
        data: (allTransactions) {
          // Filter transactions for this account
          final accountTransactions = allTransactions.where((transaction) {
            return transaction.accountId == widget.account.id ||
                   transaction.toAccountId == widget.account.id;
          }).toList();

          // Apply type filter
          final filteredTransactions = _filterType != null
              ? accountTransactions.where((t) => t.type == _filterType).toList()
              : accountTransactions;

          // Apply sorting
          final sortedTransactions = _applySorting(filteredTransactions);

          if (sortedTransactions.isEmpty) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              _buildSummaryHeader(sortedTransactions),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: sortedTransactions.length,
                  itemBuilder: (context, index) {
                    final transaction = sortedTransactions[index];
                    return _buildTransactionCard(transaction);
                  },
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToCreateTransaction(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSummaryHeader(List<TransactionModel> transactions) {
    final totalIncome = transactions
        .where((t) => t.type == TransactionType.income || 
                     (t.type == TransactionType.transfer && t.toAccountId == widget.account.id))
        .fold<double>(0, (sum, t) => sum + t.amount);
    
    final totalExpenses = transactions
        .where((t) => t.type == TransactionType.expense || 
                     (t.type == TransactionType.transfer && t.accountId == widget.account.id))
        .fold<double>(0, (sum, t) => sum + t.amount);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryItem(
              'Total Income',
              totalIncome,
              Colors.green,
              Icons.trending_up,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Theme.of(context).dividerColor,
          ),
          Expanded(
            child: _buildSummaryItem(
              'Total Expenses',
              totalExpenses,
              Colors.red,
              Icons.trending_down,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Theme.of(context).dividerColor,
          ),
          Expanded(
            child: _buildSummaryItem(
              'Transactions',
              transactions.length.toDouble(),
              Theme.of(context).colorScheme.primary,
              Icons.receipt_long,
              isCount: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, double value, Color color, IconData icon, {bool isCount = false}) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          isCount ? value.toInt().toString() : '\$${value.toStringAsFixed(2)}',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Transactions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'No transactions found for this account',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToCreateTransaction(),
            icon: const Icon(Icons.add),
            label: const Text('Add Transaction'),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(TransactionModel transaction) {
    final isIncoming = transaction.type == TransactionType.income ||
        (transaction.type == TransactionType.transfer && transaction.toAccountId == widget.account.id);
    
    final isOutgoing = transaction.type == TransactionType.expense ||
        (transaction.type == TransactionType.transfer && transaction.accountId == widget.account.id);

    Color amountColor = Colors.grey;
    String amountPrefix = '';
    
    if (isIncoming) {
      amountColor = Colors.green;
      amountPrefix = '+';
    } else if (isOutgoing) {
      amountColor = Colors.red;
      amountPrefix = '-';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getTransactionColor(transaction.type).withValues(alpha: 0.1),
          child: Icon(
            _getTransactionIcon(transaction.type),
            color: _getTransactionColor(transaction.type),
          ),
        ),
        title: Text(
          transaction.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_formatDate(transaction.date)),
            if (transaction.description != null)
              Text(
                transaction.description!,
                style: TextStyle(color: Colors.grey[600]),
              ),
            if (transaction.type == TransactionType.transfer)
              Text(
                isIncoming 
                    ? 'Transfer from another account'
                    : 'Transfer to another account',
                style: TextStyle(
                  color: Colors.blue[600],
                  fontSize: 12,
                ),
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '$amountPrefix\$${transaction.amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: amountColor,
                fontSize: 16,
              ),
            ),
            if (transaction.tags.isNotEmpty)
              Text(
                transaction.tags.first,
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12,
                ),
              ),
          ],
        ),
        onTap: () => _showTransactionDetails(transaction),
      ),
    );
  }

  void _showTransactionDetails(TransactionModel transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(transaction.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Amount: \$${transaction.amount.toStringAsFixed(2)}'),
            Text('Type: ${transaction.type.displayName}'),
            Text('Date: ${_formatDate(transaction.date)}'),
            if (transaction.description != null)
              Text('Description: ${transaction.description}'),
            if (transaction.location != null)
              Text('Location: ${transaction.location}'),
            if (transaction.tags.isNotEmpty)
              Text('Tags: ${transaction.tags.join(', ')}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToEditTransaction(transaction);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  List<TransactionModel> _applySorting(List<TransactionModel> transactions) {
    final sorted = List<TransactionModel>.from(transactions);
    
    switch (_sortBy) {
      case 'date':
        sorted.sort((a, b) => _sortAscending 
            ? a.date.compareTo(b.date)
            : b.date.compareTo(a.date));
        break;
      case 'amount':
        sorted.sort((a, b) => _sortAscending 
            ? a.amount.compareTo(b.amount)
            : b.amount.compareTo(a.amount));
        break;
      case 'title':
        sorted.sort((a, b) => _sortAscending 
            ? a.title.compareTo(b.title)
            : b.title.compareTo(a.title));
        break;
    }
    
    return sorted;
  }

  void _navigateToCreateTransaction() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateTransactionScreen(),
      ),
    );
  }

  void _navigateToEditTransaction(TransactionModel transaction) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateTransactionScreen(
          editingTransaction: transaction,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Icons.add;
      case TransactionType.expense:
        return Icons.remove;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }

  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }
}
