import 'dart:io';
import 'dart:typed_data';
// import 'package:file_picker/file_picker.dart';  // Temporarily disabled for APK build
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../../../core/models/file_attachment_model.dart';

// Temporary enum for APK build
enum FileType { any, image, custom }

class FileAttachmentService {
  static const int maxFileSize = 50 * 1024 * 1024; // 50MB
  static const List<String> allowedImageTypes = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'bmp',
    'webp',
  ];
  static const List<String> allowedDocumentTypes = [
    'pdf',
    'doc',
    'docx',
    'txt',
    'rtf',
    'odt',
  ];
  static const List<String> allowedAudioTypes = [
    'mp3',
    'm4a',
    'wav',
    'aac',
    'ogg',
  ];

  /// Pick files from device storage
  Future<List<FileAttachmentModel>?> pickFiles({
    FileType type = FileType.any,
    bool allowMultiple = true,
    List<String>? allowedExtensions,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowMultiple: allowMultiple,
        allowedExtensions: allowedExtensions,
        withData: true, // For web compatibility
      );

      if (result == null || result.files.isEmpty) {
        return null;
      }

      final attachments = <FileAttachmentModel>[];

      for (final file in result.files) {
        // Validate file size
        if (file.size > maxFileSize) {
          throw Exception(
            'File ${file.name} is too large. Maximum size is 50MB.',
          );
        }

        // Validate file type
        final extension = path
            .extension(file.name)
            .toLowerCase()
            .replaceFirst('.', '');
        if (!_isAllowedFileType(extension)) {
          throw Exception('File type .$extension is not allowed.');
        }

        // Create attachment model
        final attachment = await _createAttachmentFromPlatformFile(file);
        attachments.add(attachment);
      }

      return attachments;
    } catch (e) {
      print('Error picking files: $e');
      rethrow;
    }
  }

  /// Pick images specifically
  Future<List<FileAttachmentModel>?> pickImages({
    bool allowMultiple = true,
  }) async {
    return await pickFiles(type: FileType.image, allowMultiple: allowMultiple);
  }

  /// Pick documents specifically
  Future<List<FileAttachmentModel>?> pickDocuments({
    bool allowMultiple = true,
  }) async {
    return await pickFiles(
      type: FileType.custom,
      allowMultiple: allowMultiple,
      allowedExtensions: allowedDocumentTypes,
    );
  }

  /// Pick audio files specifically
  Future<List<FileAttachmentModel>?> pickAudioFiles({
    bool allowMultiple = true,
  }) async {
    return await pickFiles(
      type: FileType.custom,
      allowMultiple: allowMultiple,
      allowedExtensions: allowedAudioTypes,
    );
  }

  /// Save file to local storage
  Future<String> saveFileToLocal(Uint8List data, String fileName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final attachmentsDir = Directory(
        path.join(directory.path, 'attachments'),
      );

      if (!await attachmentsDir.exists()) {
        await attachmentsDir.create(recursive: true);
      }

      // Generate unique filename to avoid conflicts
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(fileName);
      final baseName = path.basenameWithoutExtension(fileName);
      final uniqueFileName = '${baseName}_$timestamp$extension';

      final file = File(path.join(attachmentsDir.path, uniqueFileName));
      await file.writeAsBytes(data);

      return file.path;
    } catch (e) {
      print('Error saving file to local storage: $e');
      rethrow;
    }
  }

  /// Delete file from local storage
  Future<void> deleteLocalFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      print('Error deleting local file: $e');
    }
  }

  /// Get file data from local storage
  Future<Uint8List?> getFileData(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      print('Error reading file data: $e');
      return null;
    }
  }

  /// Get MIME type from file extension
  String getMimeType(String fileName) {
    final extension = path
        .extension(fileName)
        .toLowerCase()
        .replaceFirst('.', '');

    // Images
    if (allowedImageTypes.contains(extension)) {
      switch (extension) {
        case 'jpg':
        case 'jpeg':
          return 'image/jpeg';
        case 'png':
          return 'image/png';
        case 'gif':
          return 'image/gif';
        case 'bmp':
          return 'image/bmp';
        case 'webp':
          return 'image/webp';
        default:
          return 'image/$extension';
      }
    }

    // Documents
    if (allowedDocumentTypes.contains(extension)) {
      switch (extension) {
        case 'pdf':
          return 'application/pdf';
        case 'doc':
          return 'application/msword';
        case 'docx':
          return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        case 'txt':
          return 'text/plain';
        case 'rtf':
          return 'application/rtf';
        case 'odt':
          return 'application/vnd.oasis.opendocument.text';
        default:
          return 'application/$extension';
      }
    }

    // Audio
    if (allowedAudioTypes.contains(extension)) {
      switch (extension) {
        case 'mp3':
          return 'audio/mpeg';
        case 'm4a':
          return 'audio/mp4';
        case 'wav':
          return 'audio/wav';
        case 'aac':
          return 'audio/aac';
        case 'ogg':
          return 'audio/ogg';
        default:
          return 'audio/$extension';
      }
    }

    return 'application/octet-stream';
  }

  /// Check if file type is allowed
  bool _isAllowedFileType(String extension) {
    return allowedImageTypes.contains(extension) ||
        allowedDocumentTypes.contains(extension) ||
        allowedAudioTypes.contains(extension);
  }

  /// Create attachment model from platform file
  Future<FileAttachmentModel> _createAttachmentFromPlatformFile(
    PlatformFile file,
  ) async {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final mimeType = getMimeType(file.name);

    // Save file to local storage
    String localPath;
    if (file.bytes != null) {
      // Web platform or file with bytes
      localPath = await saveFileToLocal(file.bytes!, file.name);
    } else if (file.path != null) {
      // Mobile platform with file path
      final fileData = await File(file.path!).readAsBytes();
      localPath = await saveFileToLocal(fileData, file.name);
    } else {
      throw Exception('Unable to access file data');
    }

    return FileAttachmentModel(
      id: id,
      name: file.name,
      path: localPath,
      url: null, // Will be set when uploaded to cloud storage
      mimeType: mimeType,
      size: file.size,
      uploadedAt: DateTime.now(),
    );
  }

  /// Get all allowed file extensions
  List<String> getAllowedExtensions() {
    return [
      ...allowedImageTypes,
      ...allowedDocumentTypes,
      ...allowedAudioTypes,
    ];
  }

  /// Format file size for display
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}
