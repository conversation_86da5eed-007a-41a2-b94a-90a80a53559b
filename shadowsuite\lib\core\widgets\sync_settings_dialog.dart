import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../services/sync_service.dart';
import '../services/supabase_service.dart';

enum SyncMode {
  disabled,
  uploadOnly,
  downloadOnly,
  bidirectional,
}

class SyncSettingsDialog extends ConsumerStatefulWidget {
  const SyncSettingsDialog({super.key});

  @override
  ConsumerState<SyncSettingsDialog> createState() => _SyncSettingsDialogState();
}

class _SyncSettingsDialogState extends ConsumerState<SyncSettingsDialog> {
  SyncMode _syncMode = SyncMode.bidirectional;
  bool _autoSync = true;
  bool _syncOnWifiOnly = false;
  bool _isLoading = true;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _syncMode = SyncMode.values[prefs.getInt(AppConstants.keySyncMode) ?? 3];
      _autoSync = prefs.getBool(AppConstants.keyAutoSync) ?? true;
      _syncOnWifiOnly = prefs.getBool(AppConstants.keySyncWifiOnly) ?? false;
      _isLoading = false;
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(AppConstants.keySyncMode, _syncMode.index);
    await prefs.setBool(AppConstants.keyAutoSync, _autoSync);
    await prefs.setBool(AppConstants.keySyncWifiOnly, _syncOnWifiOnly);
  }

  Future<void> _performManualSync() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      final syncService = SyncService();
      await syncService.syncAll();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sync completed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sync failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  Future<void> _clearLocalData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Local Data'),
        content: const Text(
          'This will permanently delete all local data. '
          'Make sure your data is synced to the cloud first. '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // TODO: Implement clear local data
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Clear local data feature coming soon'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _clearCloudData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cloud Data'),
        content: const Text(
          'This will permanently delete all your data from the cloud. '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // TODO: Implement clear cloud data
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Clear cloud data feature coming soon'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Dialog(
        child: Padding(
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading sync settings...'),
            ],
          ),
        ),
      );
    }

    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.cloud_sync,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Sync Settings',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Sync mode
                    Text(
                      'Sync Mode',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...SyncMode.values.map((mode) {
                      return RadioListTile<SyncMode>(
                        title: Text(_getSyncModeTitle(mode)),
                        subtitle: Text(_getSyncModeDescription(mode)),
                        value: mode,
                        groupValue: _syncMode,
                        onChanged: (value) {
                          setState(() {
                            _syncMode = value!;
                          });
                        },
                      );
                    }),

                    const SizedBox(height: 24),

                    // Auto sync settings
                    SwitchListTile(
                      title: const Text('Auto Sync'),
                      subtitle: const Text('Automatically sync data in background'),
                      value: _autoSync,
                      onChanged: (value) {
                        setState(() {
                          _autoSync = value;
                        });
                      },
                    ),

                    SwitchListTile(
                      title: const Text('WiFi Only'),
                      subtitle: const Text('Only sync when connected to WiFi'),
                      value: _syncOnWifiOnly,
                      onChanged: (value) {
                        setState(() {
                          _syncOnWifiOnly = value;
                        });
                      },
                    ),

                    const SizedBox(height: 24),

                    // Manual sync
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isSyncing ? null : _performManualSync,
                        icon: _isSyncing
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.sync),
                        label: Text(_isSyncing ? 'Syncing...' : 'Sync Now'),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Data management
                    Text(
                      'Data Management',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    ListTile(
                      leading: const Icon(Icons.delete_sweep, color: Colors.orange),
                      title: const Text('Clear Local Data'),
                      subtitle: const Text('Delete all data stored on this device'),
                      onTap: _clearLocalData,
                    ),

                    ListTile(
                      leading: const Icon(Icons.cloud_off, color: Colors.red),
                      title: const Text('Clear Cloud Data'),
                      subtitle: const Text('Delete all data from the cloud'),
                      onTap: _clearCloudData,
                    ),
                  ],
                ),
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(24),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        await _saveSettings();
                        if (mounted) {
                          Navigator.of(context).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Sync settings saved'),
                              backgroundColor: Colors.green,
                            ),
                          );
                        }
                      },
                      child: const Text('Save'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSyncModeTitle(SyncMode mode) {
    switch (mode) {
      case SyncMode.disabled:
        return 'Disabled';
      case SyncMode.uploadOnly:
        return 'Upload Only';
      case SyncMode.downloadOnly:
        return 'Download Only';
      case SyncMode.bidirectional:
        return 'Bidirectional';
    }
  }

  String _getSyncModeDescription(SyncMode mode) {
    switch (mode) {
      case SyncMode.disabled:
        return 'No synchronization';
      case SyncMode.uploadOnly:
        return 'Only upload local changes to cloud';
      case SyncMode.downloadOnly:
        return 'Only download changes from cloud';
      case SyncMode.bidirectional:
        return 'Full two-way synchronization';
    }
  }
}

// Helper function to show sync settings dialog
void showSyncSettingsDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const SyncSettingsDialog(),
  );
}
