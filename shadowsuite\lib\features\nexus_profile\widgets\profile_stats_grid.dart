import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_nexus_model.dart';
import '../../../core/theme/futuristic_theme.dart';

class ProfileStatsGrid extends ConsumerWidget {
  final UserNexusModel profile;

  const ProfileStatsGrid({
    super.key,
    required this.profile,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: FuturisticTheme.primaryBlue,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              context,
              'Storage Usage',
              '${profile.formattedStorageUsed} / ${profile.formattedStorageLimit}',
              Icons.storage,
              FuturisticTheme.primaryBlue,
              progress: profile.storageUsagePercentage / 100,
              isWarning: profile.isStorageNearLimit,
            ),
            _buildStatCard(
              context,
              'Account Status',
              profile.isActive ? 'Active' : 'Inactive',
              Icons.account_circle,
              profile.isActive ? FuturisticTheme.primaryGreen : Colors.orange,
            ),
            _buildStatCard(
              context,
              'Subscription',
              profile.subscriptionTier.toUpperCase(),
              Icons.star,
              profile.isPremium ? Colors.amber : FuturisticTheme.primaryPurple,
            ),
            _buildStatCard(
              context,
              'Last Sync',
              _formatLastSync(profile.lastSyncAt),
              Icons.sync,
              FuturisticTheme.primaryBlue,
            ),
            _buildStatCard(
              context,
              'Verification',
              profile.isVerified ? 'Verified' : 'Unverified',
              Icons.verified_user,
              profile.isVerified ? FuturisticTheme.primaryGreen : Colors.orange,
            ),
            _buildStatCard(
              context,
              'Last Seen',
              _formatLastSeen(profile.lastSeenAt),
              Icons.access_time,
              FuturisticTheme.primaryPurple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color, {
    double? progress,
    bool isWarning = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.1),
            color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isWarning ? Colors.orange : color.withOpacity(0.3),
          width: isWarning ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and warning indicator
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
              if (isWarning)
                Icon(
                  Icons.warning,
                  color: Colors.orange,
                  size: 16,
                ),
            ],
          ),

          const SizedBox(height: 12),

          // Value
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isWarning ? Colors.orange : color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 4),

          // Title
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),

          // Progress bar (if provided)
          if (progress != null) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: color.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(
                isWarning ? Colors.orange : color,
              ),
              borderRadius: BorderRadius.circular(2),
            ),
          ],
        ],
      ),
    );
  }

  String _formatLastSync(DateTime? lastSync) {
    if (lastSync == null) return 'Never';
    
    final now = DateTime.now();
    final difference = now.difference(lastSync);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).floor()}w ago';
    }
  }

  String _formatLastSeen(DateTime? lastSeen) {
    if (lastSeen == null) return 'Unknown';
    
    final now = DateTime.now();
    final difference = now.difference(lastSeen);
    
    if (difference.inMinutes < 5) {
      return 'Online';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).floor()}w ago';
    }
  }
}
