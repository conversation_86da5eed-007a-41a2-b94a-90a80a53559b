com.example.shadowsuite.app-jetified-lifecycle-runtime-ktx-2.7.0-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\0d7ae9f044efe0bd8492f96588cc2ce8\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.shadowsuite.app-jetified-window-1.2.0-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\res
com.example.shadowsuite.app-jetified-savedstate-1.2.1-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\274b962bfc0ee3499460b07125b40653\transformed\jetified-savedstate-1.2.1\res
com.example.shadowsuite.app-jetified-fragment-ktx-1.7.1-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\2bba37c8e9c6495f9eb26a76e34301ba\transformed\jetified-fragment-ktx-1.7.1\res
com.example.shadowsuite.app-lifecycle-livedata-core-2.7.0-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\2e82a987f49d0f6484c5e02710942378\transformed\lifecycle-livedata-core-2.7.0\res
com.example.shadowsuite.app-jetified-lifecycle-viewmodel-ktx-2.7.0-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\322c68e59856509d4727dd3729f949bc\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.shadowsuite.app-jetified-datastore-core-release-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\3c00c34f73466195bccbeb63410c0399\transformed\jetified-datastore-core-release\res
com.example.shadowsuite.app-jetified-datastore-release-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\3d63b0fb941c9abc7885c3e08b0f44f6\transformed\jetified-datastore-release\res
com.example.shadowsuite.app-jetified-core-1.0.0-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\40dfcd36206381321d61bc4d0a504790\transformed\jetified-core-1.0.0\res
com.example.shadowsuite.app-sqlite-2.3.0-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\46c8201dea47db5528313d18e4d41817\transformed\sqlite-2.3.0\res
com.example.shadowsuite.app-jetified-startup-runtime-1.1.1-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\res
com.example.shadowsuite.app-jetified-appcompat-resources-1.2.0-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\5112bc7eb3743dbfb90799b72f598a76\transformed\jetified-appcompat-resources-1.2.0\res
com.example.shadowsuite.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\51aab5cd0fa4197e7c50f04a365e85eb\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.shadowsuite.app-jetified-lifecycle-service-2.7.0-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\5f9e8adf8fdcd943d478a3b4e8cee074\transformed\jetified-lifecycle-service-2.7.0\res
com.example.shadowsuite.app-jetified-lifecycle-livedata-core-ktx-2.7.0-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\661cfed71eb01d7f0c4bbaeae6faab4d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.shadowsuite.app-jetified-activity-ktx-1.9.3-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\6bf3bf9680e730373e2f78ba6a288247\transformed\jetified-activity-ktx-1.9.3\res
com.example.shadowsuite.app-jetified-flutter_sound_core-9.28.0-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\res
com.example.shadowsuite.app-core-1.13.1-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\res
com.example.shadowsuite.app-preference-1.2.1-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\744ef0616acd167077bd442bba141275\transformed\preference-1.2.1\res
com.example.shadowsuite.app-jetified-datastore-preferences-release-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\749ed3f27cabf52b3ef491eaf357cdb7\transformed\jetified-datastore-preferences-release\res
com.example.shadowsuite.app-jetified-profileinstaller-1.3.1-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\res
com.example.shadowsuite.app-jetified-window-java-1.2.0-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\88ea6bbee0c3d252da1a135eb4f3ca19\transformed\jetified-window-java-1.2.0\res
com.example.shadowsuite.app-core-runtime-2.2.0-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\9724ff6e8d4e6c9244bfb7005fae21c2\transformed\core-runtime-2.2.0\res
com.example.shadowsuite.app-transition-1.4.1-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\9bf0628666be1a9ac10b49d28bb02595\transformed\transition-1.4.1\res
com.example.shadowsuite.app-recyclerview-1.0.0-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\9e5757bbf3b5c6f98be61ba84aed2ced\transformed\recyclerview-1.0.0\res
com.example.shadowsuite.app-media-1.4.1-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\a42f03c2eee02ce9eb5a727d5ee33665\transformed\media-1.4.1\res
com.example.shadowsuite.app-browser-1.8.0-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\a9e90969c21e54abbd28d118be0111db\transformed\browser-1.8.0\res
com.example.shadowsuite.app-jetified-activity-1.9.3-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\aadc3611709663a05036e28336f6e814\transformed\jetified-activity-1.9.3\res
com.example.shadowsuite.app-sqlite-framework-2.3.0-28 C:\Users\<USER>\.gradle\caches\8.12\transforms\b370f28adf4d4823ed5e25cb6ccd96cc\transformed\sqlite-framework-2.3.0\res
com.example.shadowsuite.app-room-runtime-2.5.0-29 C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\res
com.example.shadowsuite.app-jetified-lifecycle-process-2.7.0-30 C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\res
com.example.shadowsuite.app-lifecycle-viewmodel-2.7.0-31 C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d3b67c81f17a9d01acb41ef7dc29fd\transformed\lifecycle-viewmodel-2.7.0\res
com.example.shadowsuite.app-jetified-savedstate-ktx-1.2.1-32 C:\Users\<USER>\.gradle\caches\8.12\transforms\cf782f0e0913fb55b1494a7adbb5fe28\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.shadowsuite.app-appcompat-1.2.0-33 C:\Users\<USER>\.gradle\caches\8.12\transforms\d02122062f42d0867eb9f03be4ddd728\transformed\appcompat-1.2.0\res
com.example.shadowsuite.app-jetified-core-ktx-1.13.1-34 C:\Users\<USER>\.gradle\caches\8.12\transforms\d6a04692cb28023b65a80b358730356b\transformed\jetified-core-ktx-1.13.1\res
com.example.shadowsuite.app-work-runtime-2.8.1-35 C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\res
com.example.shadowsuite.app-lifecycle-livedata-2.7.0-36 C:\Users\<USER>\.gradle\caches\8.12\transforms\dc9487afb3090e92844f4382c0195afd\transformed\lifecycle-livedata-2.7.0\res
com.example.shadowsuite.app-jetified-tracing-1.2.0-37 C:\Users\<USER>\.gradle\caches\8.12\transforms\dcb95f8119ec5b1845af3693aa8a7063\transformed\jetified-tracing-1.2.0\res
com.example.shadowsuite.app-coordinatorlayout-1.0.0-38 C:\Users\<USER>\.gradle\caches\8.12\transforms\ee991f1e2a54faf463391e906c8be955\transformed\coordinatorlayout-1.0.0\res
com.example.shadowsuite.app-slidingpanelayout-1.2.0-39 C:\Users\<USER>\.gradle\caches\8.12\transforms\f568acae4ccb53cd32952f50f20d1ff2\transformed\slidingpanelayout-1.2.0\res
com.example.shadowsuite.app-fragment-1.7.1-40 C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4c05eaffed710d6c61f97a8b8ac890\transformed\fragment-1.7.1\res
com.example.shadowsuite.app-jetified-annotation-experimental-1.4.0-41 C:\Users\<USER>\.gradle\caches\8.12\transforms\fe74d444a12af678d91d4bc25f88f447\transformed\jetified-annotation-experimental-1.4.0\res
com.example.shadowsuite.app-lifecycle-runtime-2.7.0-42 C:\Users\<USER>\.gradle\caches\8.12\transforms\fed144bef681fc8fb80134095669b372\transformed\lifecycle-runtime-2.7.0\res
com.example.shadowsuite.app-main-43 D:\apps\android\shadowsuite\android\app\src\main\res
com.example.shadowsuite.app-release-44 D:\apps\android\shadowsuite\android\app\src\release\res
com.example.shadowsuite.app-pngs-45 D:\apps\android\shadowsuite\build\app\generated\res\pngs\release
com.example.shadowsuite.app-resValues-46 D:\apps\android\shadowsuite\build\app\generated\res\resValues\release
com.example.shadowsuite.app-packageReleaseResources-47 D:\apps\android\shadowsuite\build\app\intermediates\incremental\release\packageReleaseResources\merged.dir
com.example.shadowsuite.app-packageReleaseResources-48 D:\apps\android\shadowsuite\build\app\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.example.shadowsuite.app-merged-not-compiled-resources-49 D:\apps\android\shadowsuite\build\app\intermediates\merged-not-compiled-resources\release
com.example.shadowsuite.app-release-50 D:\apps\android\shadowsuite\build\app\intermediates\merged_res\release\mergeReleaseResources
com.example.shadowsuite.app-release-51 D:\apps\android\shadowsuite\build\app_links\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-52 D:\apps\android\shadowsuite\build\audioplayers_android\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-53 D:\apps\android\shadowsuite\build\connectivity_plus\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-54 D:\apps\android\shadowsuite\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-55 D:\apps\android\shadowsuite\build\flutter_sound\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-56 D:\apps\android\shadowsuite\build\image_picker_android\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-57 D:\apps\android\shadowsuite\build\package_info_plus\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-58 D:\apps\android\shadowsuite\build\path_provider_android\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-59 D:\apps\android\shadowsuite\build\permission_handler_android\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-60 D:\apps\android\shadowsuite\build\record_android\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-61 D:\apps\android\shadowsuite\build\share_plus\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-62 D:\apps\android\shadowsuite\build\shared_preferences_android\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-63 D:\apps\android\shadowsuite\build\speech_to_text\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-64 D:\apps\android\shadowsuite\build\sqflite_android\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-65 D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\packaged_res\release\packageReleaseResources
com.example.shadowsuite.app-release-66 D:\apps\android\shadowsuite\build\workmanager\intermediates\packaged_res\release\packageReleaseResources
