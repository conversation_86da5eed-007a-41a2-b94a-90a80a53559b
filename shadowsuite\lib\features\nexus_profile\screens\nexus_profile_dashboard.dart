import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/nexus_profile_provider.dart';
import '../widgets/profile_hero_card.dart';
import '../widgets/profile_stats_grid.dart';
import '../widgets/profile_quick_actions.dart';
import '../widgets/profile_activity_feed.dart';
import '../widgets/profile_settings_panel.dart';
import '../../../core/theme/futuristic_theme.dart';

class NexusProfileDashboard extends ConsumerStatefulWidget {
  const NexusProfileDashboard({super.key});

  @override
  ConsumerState<NexusProfileDashboard> createState() => _NexusProfileDashboardState();
}

class _NexusProfileDashboardState extends ConsumerState<NexusProfileDashboard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(nexusProfileProvider);
    final profile = profileState.profile;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              FuturisticTheme.primaryBlue.withOpacity(0.05),
              FuturisticTheme.primaryPurple.withOpacity(0.05),
              Theme.of(context).colorScheme.background,
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildContent(context, profileState),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, NexusProfileState profileState) {
    if (profileState.isLoading) {
      return _buildLoadingState();
    }

    if (profileState.error != null) {
      return _buildErrorState(profileState.error!);
    }

    if (profileState.profile == null) {
      return _buildEmptyState();
    }

    return _buildProfileDashboard(profileState.profile!);
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  FuturisticTheme.primaryBlue.withOpacity(0.1),
                  FuturisticTheme.primaryPurple.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: FuturisticTheme.primaryBlue.withOpacity(0.3),
              ),
            ),
            child: Column(
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FuturisticTheme.primaryBlue,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Loading your profile...',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: FuturisticTheme.primaryBlue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.red.withOpacity(0.1),
              Colors.orange.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Profile Error',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                ref.read(nexusProfileProvider.notifier).clearError();
                // Retry loading
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: FuturisticTheme.primaryBlue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              FuturisticTheme.primaryBlue.withOpacity(0.1),
              FuturisticTheme.primaryPurple.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: FuturisticTheme.primaryBlue.withOpacity(0.3),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.person_add_outlined,
              size: 48,
              color: FuturisticTheme.primaryBlue,
            ),
            const SizedBox(height: 16),
            Text(
              'Create Your Profile',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: FuturisticTheme.primaryBlue,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Set up your profile to get started with ShadowSuite Nexus',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate to profile creation
              },
              icon: const Icon(Icons.add),
              label: const Text('Create Profile'),
              style: ElevatedButton.styleFrom(
                backgroundColor: FuturisticTheme.primaryBlue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileDashboard(profile) {
    return CustomScrollView(
      slivers: [
        // Hero Profile Card
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: ProfileHeroCard(profile: profile),
          ),
        ),

        // Stats Grid
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ProfileStatsGrid(profile: profile),
          ),
        ),

        // Quick Actions
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: ProfileQuickActions(profile: profile),
          ),
        ),

        // Activity Feed and Settings Panel
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Activity Feed (2/3 width)
                Expanded(
                  flex: 2,
                  child: ProfileActivityFeed(),
                ),
                const SizedBox(width: 16),
                // Settings Panel (1/3 width)
                Expanded(
                  flex: 1,
                  child: ProfileSettingsPanel(profile: profile),
                ),
              ],
            ),
          ),
        ),

        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 32),
        ),
      ],
    );
  }
}
