import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_nexus_model.dart';
import '../../../core/services/supabase_service.dart';

class NexusProfileService {
  static NexusProfileService? _instance;
  final SupabaseService _supabaseService = SupabaseService();
  
  NexusProfileService._internal();
  
  factory NexusProfileService() {
    _instance ??= NexusProfileService._internal();
    return _instance!;
  }

  SupabaseClient? get _client => _supabaseService.client;
  bool get isInitialized => _supabaseService.isInitialized;

  // PROFILE OPERATIONS
  Future<UserNexusModel?> getUserProfile(String authUserId) async {
    if (!isInitialized) throw Exception('Nexus service not initialized');
    
    try {
      debugPrint('🔍 Fetching user profile for: $authUserId');
      
      final response = await _client!
          .from('user_profiles')
          .select()
          .eq('auth_user_id', authUserId)
          .maybeSingle();
      
      if (response == null) {
        debugPrint('❌ No profile found for user: $authUserId');
        return null;
      }
      
      debugPrint('✅ Profile found for user: $authUserId');
      return UserNexusModel.fromJson(_convertDatabaseResponse(response));
    } catch (e) {
      debugPrint('❌ Error fetching user profile: $e');
      rethrow;
    }
  }

  Future<UserNexusModel> createUserProfile(UserNexusModel profile) async {
    if (!isInitialized) throw Exception('Nexus service not initialized');
    
    try {
      debugPrint('👤 Creating user profile for: ${profile.email}');
      
      final data = _convertToDatabase(profile.toJson());
      
      final response = await _client!
          .from('user_profiles')
          .insert(data)
          .select()
          .single();
      
      debugPrint('✅ Profile created successfully: ${response['id']}');
      return UserNexusModel.fromJson(_convertDatabaseResponse(response));
    } catch (e) {
      debugPrint('❌ Error creating user profile: $e');
      rethrow;
    }
  }

  Future<UserNexusModel> updateUserProfile(String profileId, Map<String, dynamic> updates) async {
    if (!isInitialized) throw Exception('Nexus service not initialized');
    
    try {
      debugPrint('🔄 Updating user profile: $profileId');
      
      final data = _convertToDatabase(updates);
      data['updated_at'] = DateTime.now().toIso8601String();
      
      final response = await _client!
          .from('user_profiles')
          .update(data)
          .eq('id', profileId)
          .select()
          .single();
      
      debugPrint('✅ Profile updated successfully: $profileId');
      return UserNexusModel.fromJson(_convertDatabaseResponse(response));
    } catch (e) {
      debugPrint('❌ Error updating user profile: $e');
      rethrow;
    }
  }

  Future<void> updateLastSeen(String authUserId) async {
    if (!isInitialized) return;
    
    try {
      await _client!
          .from('user_profiles')
          .update({'last_seen_at': DateTime.now().toIso8601String()})
          .eq('auth_user_id', authUserId);
      
      debugPrint('✅ Last seen updated for user: $authUserId');
    } catch (e) {
      debugPrint('❌ Error updating last seen: $e');
      // Don't rethrow - this is not critical
    }
  }

  Future<void> updateSyncStatus(String authUserId) async {
    if (!isInitialized) return;
    
    try {
      await _client!
          .from('user_profiles')
          .update({'last_sync_at': DateTime.now().toIso8601String()})
          .eq('auth_user_id', authUserId);
      
      debugPrint('✅ Sync status updated for user: $authUserId');
    } catch (e) {
      debugPrint('❌ Error updating sync status: $e');
      // Don't rethrow - this is not critical
    }
  }

  Future<void> updateStorageUsage(String authUserId, int storageUsed) async {
    if (!isInitialized) return;
    
    try {
      await _client!
          .from('user_profiles')
          .update({'total_storage_used': storageUsed})
          .eq('auth_user_id', authUserId);
      
      debugPrint('✅ Storage usage updated for user: $authUserId ($storageUsed bytes)');
    } catch (e) {
      debugPrint('❌ Error updating storage usage: $e');
      // Don't rethrow - this is not critical
    }
  }

  Future<List<Map<String, dynamic>>> getActivityLogs(String authUserId, {int limit = 50}) async {
    if (!isInitialized) throw Exception('Nexus service not initialized');
    
    try {
      // First get the user profile ID
      final profileResponse = await _client!
          .from('user_profiles')
          .select('id')
          .eq('auth_user_id', authUserId)
          .single();
      
      final profileId = profileResponse['id'];
      
      final response = await _client!
          .from('activity_logs')
          .select()
          .eq('user_id', profileId)
          .order('created_at', ascending: false)
          .limit(limit);
      
      debugPrint('✅ Fetched ${response.length} activity logs');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error fetching activity logs: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getSyncLogs(String authUserId, {int limit = 50}) async {
    if (!isInitialized) throw Exception('Nexus service not initialized');
    
    try {
      // First get the user profile ID
      final profileResponse = await _client!
          .from('user_profiles')
          .select('id')
          .eq('auth_user_id', authUserId)
          .single();
      
      final profileId = profileResponse['id'];
      
      final response = await _client!
          .from('sync_logs')
          .select()
          .eq('user_id', profileId)
          .order('created_at', ascending: false)
          .limit(limit);
      
      debugPrint('✅ Fetched ${response.length} sync logs');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error fetching sync logs: $e');
      rethrow;
    }
  }

  // REAL-TIME SUBSCRIPTIONS
  RealtimeChannel subscribeToProfile(String authUserId, Function(UserNexusModel) onUpdate) {
    if (!isInitialized) throw Exception('Nexus service not initialized');
    
    return _client!
        .channel('profile_$authUserId')
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'user_profiles',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'auth_user_id',
            value: authUserId,
          ),
          callback: (payload) {
            debugPrint('📡 Real-time profile update received');
            try {
              final updatedProfile = UserNexusModel.fromJson(
                _convertDatabaseResponse(payload.newRecord),
              );
              onUpdate(updatedProfile);
            } catch (e) {
              debugPrint('❌ Error processing real-time profile update: $e');
            }
          },
        )
        .subscribe();
  }

  // UTILITY METHODS
  Map<String, dynamic> _convertToDatabase(Map<String, dynamic> data) {
    final converted = Map<String, dynamic>.from(data);
    
    // Convert camelCase to snake_case for database
    final conversions = {
      'authUserId': 'auth_user_id',
      'fullName': 'full_name',
      'displayName': 'display_name',
      'avatarUrl': 'avatar_url',
      'coverImageUrl': 'cover_image_url',
      'dateOfBirth': 'date_of_birth',
      'languagePreference': 'language_preference',
      'themePreference': 'theme_preference',
      'notificationSettings': 'notification_settings',
      'privacySettings': 'privacy_settings',
      'appPreferences': 'app_preferences',
      'subscriptionTier': 'subscription_tier',
      'subscriptionExpiresAt': 'subscription_expires_at',
      'totalStorageUsed': 'total_storage_used',
      'storageLimit': 'storage_limit',
      'isVerified': 'is_verified',
      'isActive': 'is_active',
      'isPremium': 'is_premium',
      'lastSeenAt': 'last_seen_at',
      'lastSyncAt': 'last_sync_at',
      'deviceInfo': 'device_info',
      'createdAt': 'created_at',
      'updatedAt': 'updated_at',
      'socialLinks': 'social_links',
    };
    
    for (final entry in conversions.entries) {
      if (converted.containsKey(entry.key)) {
        converted[entry.value] = converted.remove(entry.key);
      }
    }
    
    return converted;
  }

  Map<String, dynamic> _convertDatabaseResponse(Map<String, dynamic> data) {
    final converted = Map<String, dynamic>.from(data);
    
    // Convert snake_case to camelCase for model
    final conversions = {
      'auth_user_id': 'authUserId',
      'full_name': 'fullName',
      'display_name': 'displayName',
      'avatar_url': 'avatarUrl',
      'cover_image_url': 'coverImageUrl',
      'date_of_birth': 'dateOfBirth',
      'language_preference': 'languagePreference',
      'theme_preference': 'themePreference',
      'notification_settings': 'notificationSettings',
      'privacy_settings': 'privacySettings',
      'app_preferences': 'appPreferences',
      'subscription_tier': 'subscriptionTier',
      'subscription_expires_at': 'subscriptionExpiresAt',
      'total_storage_used': 'totalStorageUsed',
      'storage_limit': 'storageLimit',
      'is_verified': 'isVerified',
      'is_active': 'isActive',
      'is_premium': 'isPremium',
      'last_seen_at': 'lastSeenAt',
      'last_sync_at': 'lastSyncAt',
      'device_info': 'deviceInfo',
      'created_at': 'createdAt',
      'updated_at': 'updatedAt',
      'social_links': 'socialLinks',
    };
    
    for (final entry in conversions.entries) {
      if (converted.containsKey(entry.key)) {
        converted[entry.value] = converted.remove(entry.key);
      }
    }
    
    return converted;
  }
}
