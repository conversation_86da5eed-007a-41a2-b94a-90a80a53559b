import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:table_calendar/table_calendar.dart';
import '../providers/money_flow_provider.dart';
import '../models/transaction_model.dart';
import '../models/category_model.dart';
import '../models/account_model.dart';
import 'create_transaction_screen.dart';

class TransactionsTab extends ConsumerStatefulWidget {
  const TransactionsTab({super.key});

  @override
  ConsumerState<TransactionsTab> createState() => _TransactionsTabState();
}

class _TransactionsTabState extends ConsumerState<TransactionsTab> {
  TransactionType? _selectedType;
  String? _selectedCategoryId;
  String? _selectedAccountId;
  String _searchQuery = '';
  DateTime? _startDate;
  DateTime? _endDate;
  String _sortBy = 'date'; // date, amount, title
  bool _sortAscending = false;
  bool _isSelectionMode = false;
  Set<String> _selectedTransactions = <String>{};

  @override
  Widget build(BuildContext context) {
    final filter = TransactionFilter(
      type: _selectedType,
      categoryId: _selectedCategoryId,
      accountId: _selectedAccountId,
      searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
      startDate: _startDate,
      endDate: _endDate,
    );

    final transactions = ref.watch(filteredTransactionsProvider(filter));
    final categoriesAsync = ref.watch(categoriesProvider);
    final accountsAsync = ref.watch(accountsProvider);

    // Apply sorting
    final sortedTransactions = _applySorting(transactions);

    return Scaffold(
      appBar: _isSelectionMode ? _buildSelectionAppBar(context) : null,
      body: Column(
        children: [
          // Search and filters
          _buildSearchAndFilters(context, categoriesAsync, accountsAsync),

          // Sort and bulk actions bar
          _buildActionBar(context, sortedTransactions),

          // Transactions list
          Expanded(child: _buildTransactionsList(context, sortedTransactions)),
        ],
      ),
      floatingActionButton: _isSelectionMode
          ? null
          : FloatingActionButton(
              onPressed: () => _navigateToCreateTransaction(context),
              child: const Icon(Icons.add),
            ),
    );
  }

  Widget _buildSearchAndFilters(
    BuildContext context,
    AsyncValue<List<dynamic>> categoriesAsync,
    AsyncValue<List<dynamic>> accountsAsync,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            decoration: const InputDecoration(
              hintText: 'Search transactions...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 12),

          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: _selectedType == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedType = selected ? null : _selectedType;
                    });
                  },
                ),
                const SizedBox(width: 8),
                FilterChip(
                  label: const Text('Income'),
                  selected: _selectedType == TransactionType.income,
                  onSelected: (selected) {
                    setState(() {
                      _selectedType = selected ? TransactionType.income : null;
                    });
                  },
                ),
                const SizedBox(width: 8),
                FilterChip(
                  label: const Text('Expense'),
                  selected: _selectedType == TransactionType.expense,
                  onSelected: (selected) {
                    setState(() {
                      _selectedType = selected ? TransactionType.expense : null;
                    });
                  },
                ),
                const SizedBox(width: 8),
                FilterChip(
                  label: const Text('Transfer'),
                  selected: _selectedType == TransactionType.transfer,
                  onSelected: (selected) {
                    setState(() {
                      _selectedType = selected
                          ? TransactionType.transfer
                          : null;
                    });
                  },
                ),
                const SizedBox(width: 8),
                FilterChip(
                  label: Text(_getDateRangeLabel()),
                  selected: _startDate != null || _endDate != null,
                  onSelected: (selected) {
                    if (selected) {
                      _showDateRangePicker(context);
                    } else {
                      setState(() {
                        _startDate = null;
                        _endDate = null;
                      });
                    }
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(
    BuildContext context,
    List<dynamic> transactions,
  ) {
    if (transactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No transactions found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Add your first transaction to get started',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: transactions.length,
      itemBuilder: (context, index) {
        final transaction = transactions[index];
        return _buildTransactionCard(context, transaction);
      },
    );
  }

  Widget _buildTransactionCard(BuildContext context, dynamic transaction) {
    final isSelected = _selectedTransactions.contains(transaction.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: isSelected ? Theme.of(context).colorScheme.primaryContainer : null,
      child: ListTile(
        leading: _isSelectionMode
            ? Checkbox(
                value: isSelected,
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      _selectedTransactions.add(transaction.id);
                    } else {
                      _selectedTransactions.remove(transaction.id);
                    }
                  });
                },
              )
            : CircleAvatar(
                backgroundColor: _getTransactionColor(
                  transaction.type,
                ).withValues(alpha: 0.1),
                child: Icon(
                  _getTransactionIcon(transaction.type),
                  color: _getTransactionColor(transaction.type),
                ),
              ),
        title: Text(
          transaction.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (transaction.description != null) Text(transaction.description!),
            const SizedBox(height: 4),
            Text(
              '${transaction.date.day}/${transaction.date.month}/${transaction.date.year}',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
            if (transaction.tags.isNotEmpty) ...[
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                children: transaction.tags
                    .take(3)
                    .map<Widget>(
                      (tag) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          tag,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.blue[700],
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${transaction.type == TransactionType.expense ? '-' : '+'}\$${transaction.amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _getTransactionColor(transaction.type),
                fontSize: 16,
              ),
            ),
            Text(
              transaction.currency,
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ),
        onTap: () {
          if (_isSelectionMode) {
            setState(() {
              if (_selectedTransactions.contains(transaction.id)) {
                _selectedTransactions.remove(transaction.id);
              } else {
                _selectedTransactions.add(transaction.id);
              }
            });
          } else {
            _showTransactionDetails(context, transaction);
          }
        },
        onLongPress: () {
          if (!_isSelectionMode) {
            setState(() {
              _isSelectionMode = true;
              _selectedTransactions.add(transaction.id);
            });
          }
        },
      ),
    );
  }

  void _showTransactionDetails(BuildContext context, dynamic transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(transaction.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Amount: \$${transaction.amount.toStringAsFixed(2)}'),
            Text('Type: ${transaction.type.displayName}'),
            Text(
              'Date: ${transaction.date.day}/${transaction.date.month}/${transaction.date.year}',
            ),
            if (transaction.description != null)
              Text('Description: ${transaction.description}'),
            if (transaction.tags.isNotEmpty)
              Text('Tags: ${transaction.tags.join(', ')}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToEditTransaction(context, transaction);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  void _navigateToCreateTransaction(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CreateTransactionScreen()),
    );
  }

  void _navigateToEditTransaction(
    BuildContext context,
    TransactionModel transaction,
  ) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            CreateTransactionScreen(editingTransaction: transaction),
      ),
    );
  }

  IconData _getTransactionIcon(TransactionType transactionType) {
    switch (transactionType) {
      case TransactionType.income:
        return Icons.add;
      case TransactionType.expense:
        return Icons.remove;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }

  Color _getTransactionColor(TransactionType transactionType) {
    switch (transactionType) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }

  List<dynamic> _applySorting(List<dynamic> transactions) {
    final sorted = List<dynamic>.from(transactions);

    switch (_sortBy) {
      case 'date':
        sorted.sort(
          (a, b) => _sortAscending
              ? a.date.compareTo(b.date)
              : b.date.compareTo(a.date),
        );
        break;
      case 'amount':
        sorted.sort(
          (a, b) => _sortAscending
              ? a.amount.compareTo(b.amount)
              : b.amount.compareTo(a.amount),
        );
        break;
      case 'title':
        sorted.sort(
          (a, b) => _sortAscending
              ? a.title.compareTo(b.title)
              : b.title.compareTo(a.title),
        );
        break;
    }

    return sorted;
  }

  PreferredSizeWidget _buildSelectionAppBar(BuildContext context) {
    return AppBar(
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () {
          setState(() {
            _isSelectionMode = false;
            _selectedTransactions.clear();
          });
        },
      ),
      title: Text('${_selectedTransactions.length} selected'),
      actions: [
        IconButton(
          icon: const Icon(Icons.select_all),
          onPressed: _toggleSelectAll,
        ),
        IconButton(
          icon: const Icon(Icons.delete),
          onPressed: _selectedTransactions.isNotEmpty
              ? _deleteSelectedTransactions
              : null,
        ),
        IconButton(
          icon: const Icon(Icons.share),
          onPressed: _selectedTransactions.isNotEmpty
              ? _exportSelectedTransactions
              : null,
        ),
      ],
    );
  }

  Widget _buildActionBar(BuildContext context, List<dynamic> transactions) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Sort dropdown
          DropdownButton<String>(
            value: _sortBy,
            items: const [
              DropdownMenuItem(value: 'date', child: Text('Sort by Date')),
              DropdownMenuItem(value: 'amount', child: Text('Sort by Amount')),
              DropdownMenuItem(value: 'title', child: Text('Sort by Title')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _sortBy = value;
                });
              }
            },
          ),

          // Sort direction
          IconButton(
            icon: Icon(
              _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
            ),
            onPressed: () {
              setState(() {
                _sortAscending = !_sortAscending;
              });
            },
          ),

          const Spacer(),

          // Transaction count
          Text(
            '${transactions.length} transactions',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),

          const SizedBox(width: 16),

          // Bulk select toggle
          IconButton(
            icon: const Icon(Icons.checklist),
            onPressed: () {
              setState(() {
                _isSelectionMode = !_isSelectionMode;
                if (!_isSelectionMode) {
                  _selectedTransactions.clear();
                }
              });
            },
          ),
        ],
      ),
    );
  }

  void _toggleSelectAll() {
    final filter = TransactionFilter(
      type: _selectedType,
      categoryId: _selectedCategoryId,
      accountId: _selectedAccountId,
      searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
      startDate: _startDate,
      endDate: _endDate,
    );
    final transactions = ref.read(filteredTransactionsProvider(filter));

    setState(() {
      if (_selectedTransactions.length == transactions.length) {
        _selectedTransactions.clear();
      } else {
        _selectedTransactions = transactions.map((t) => t.id).toSet();
      }
    });
  }

  void _deleteSelectedTransactions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transactions'),
        content: Text(
          'Are you sure you want to delete ${_selectedTransactions.length} transactions? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              for (final id in _selectedTransactions) {
                ref.read(transactionsProvider.notifier).deleteTransaction(id);
              }
              setState(() {
                _isSelectionMode = false;
                _selectedTransactions.clear();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    '${_selectedTransactions.length} transactions deleted',
                  ),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _exportSelectedTransactions() {
    // TODO: Implement CSV export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon!')),
    );
  }

  String _getDateRangeLabel() {
    if (_startDate != null && _endDate != null) {
      return '${_startDate!.day}/${_startDate!.month} - ${_endDate!.day}/${_endDate!.month}';
    } else if (_startDate != null) {
      return 'From ${_startDate!.day}/${_startDate!.month}';
    } else if (_endDate != null) {
      return 'Until ${_endDate!.day}/${_endDate!.month}';
    }
    return 'Date Range';
  }

  void _showDateRangePicker(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Date Range'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Start Date'),
              subtitle: Text(
                _startDate?.toString().split(' ')[0] ?? 'Not selected',
              ),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _startDate ?? DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _startDate = date;
                  });
                }
              },
            ),
            ListTile(
              title: const Text('End Date'),
              subtitle: Text(
                _endDate?.toString().split(' ')[0] ?? 'Not selected',
              ),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _endDate ?? DateTime.now(),
                  firstDate: _startDate ?? DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _endDate = date;
                  });
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _startDate = null;
                _endDate = null;
              });
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }
}
