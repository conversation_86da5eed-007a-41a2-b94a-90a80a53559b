import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/recurring_transaction_model.dart';
import '../models/transaction_model.dart';
import '../providers/money_flow_provider.dart';

class CreateRecurringTransactionScreen extends ConsumerStatefulWidget {
  final RecurringTransactionModel? editingRecurringTransaction;

  const CreateRecurringTransactionScreen({
    super.key,
    this.editingRecurringTransaction,
  });

  @override
  ConsumerState<CreateRecurringTransactionScreen> createState() => _CreateRecurringTransactionScreenState();
}

class _CreateRecurringTransactionScreenState extends ConsumerState<CreateRecurringTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  TransactionType _selectedType = TransactionType.expense;
  String? _selectedAccountId;
  String? _selectedCategoryId;
  RecurrenceFrequency _selectedFrequency = RecurrenceFrequency.monthly;
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  int? _maxOccurrences;
  bool _autoExecute = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.editingRecurringTransaction != null) {
      final rt = widget.editingRecurringTransaction!;
      _titleController.text = rt.title;
      _amountController.text = rt.amount.toString();
      _descriptionController.text = rt.description ?? '';
      _selectedType = rt.type;
      _selectedAccountId = rt.accountId;
      _selectedCategoryId = rt.categoryId;
      _selectedFrequency = rt.frequency;
      _startDate = rt.startDate;
      _endDate = rt.endDate;
      _maxOccurrences = rt.maxOccurrences;
      _autoExecute = rt.autoExecute;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _saveRecurringTransaction() {
    if (_formKey.currentState!.validate()) {
      if (_selectedAccountId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select an account')),
        );
        return;
      }

      final recurringTransaction = RecurringTransactionModel(
        id: widget.editingRecurringTransaction?.id ?? 
            DateTime.now().millisecondsSinceEpoch.toString(),
        userId: 'demo-user-id',
        accountId: _selectedAccountId!,
        type: _selectedType,
        amount: double.parse(_amountController.text),
        categoryId: _selectedCategoryId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        frequency: _selectedFrequency,
        startDate: _startDate,
        endDate: _endDate,
        maxOccurrences: _maxOccurrences,
        nextDue: _calculateNextDue(),
        autoExecute: _autoExecute,
        createdAt: widget.editingRecurringTransaction?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.editingRecurringTransaction != null) {
        ref.read(recurringTransactionsProvider.notifier).updateRecurringTransaction(recurringTransaction);
      } else {
        ref.read(recurringTransactionsProvider.notifier).addRecurringTransaction(recurringTransaction);
      }

      Navigator.of(context).pop();
    }
  }

  DateTime _calculateNextDue() {
    switch (_selectedFrequency) {
      case RecurrenceFrequency.daily:
        return _startDate.add(const Duration(days: 1));
      case RecurrenceFrequency.weekly:
        return _startDate.add(const Duration(days: 7));
      case RecurrenceFrequency.biweekly:
        return _startDate.add(const Duration(days: 14));
      case RecurrenceFrequency.monthly:
        return DateTime(_startDate.year, _startDate.month + 1, _startDate.day);
      case RecurrenceFrequency.quarterly:
        return DateTime(_startDate.year, _startDate.month + 3, _startDate.day);
      case RecurrenceFrequency.yearly:
        return DateTime(_startDate.year + 1, _startDate.month, _startDate.day);
    }
  }

  @override
  Widget build(BuildContext context) {
    final accountsAsync = ref.watch(accountsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.editingRecurringTransaction != null 
              ? 'Edit Recurring Transaction' 
              : 'Add Recurring Transaction',
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveRecurringTransaction,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Transaction type selector
              _buildTypeSelector(),
              const SizedBox(height: 16),

              // Title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Title *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.title),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Amount
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'Amount *',
                  border: OutlineInputBorder(),
                  prefixText: '\$ ',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Please enter a valid amount';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Account selector
              _buildAccountSelector(accountsAsync),
              const SizedBox(height: 16),

              // Category selector
              _buildCategorySelector(categoriesAsync),
              const SizedBox(height: 16),

              // Frequency selector
              _buildFrequencySelector(),
              const SizedBox(height: 16),

              // Start date
              _buildDateSelector(),
              const SizedBox(height: 16),

              // End conditions
              _buildEndConditions(),
              const SizedBox(height: 16),

              // Auto execute switch
              _buildAutoExecuteSwitch(),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Transaction Type',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ChoiceChip(
                    label: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.add_circle_outline, size: 16),
                        SizedBox(width: 4),
                        Text('Income'),
                      ],
                    ),
                    selected: _selectedType == TransactionType.income,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedType = TransactionType.income;
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ChoiceChip(
                    label: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.remove_circle_outline, size: 16),
                        SizedBox(width: 4),
                        Text('Expense'),
                      ],
                    ),
                    selected: _selectedType == TransactionType.expense,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedType = TransactionType.expense;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSelector(AsyncValue<List<dynamic>> accountsAsync) {
    return accountsAsync.when(
      data: (accounts) => DropdownButtonFormField<String>(
        value: _selectedAccountId,
        decoration: const InputDecoration(
          labelText: 'Account *',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.account_balance),
        ),
        items: accounts.map((account) {
          return DropdownMenuItem<String>(
            value: account.id,
            child: Text(account.name),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedAccountId = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return 'Please select an account';
          }
          return null;
        },
      ),
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => Text('Error loading accounts: $error'),
    );
  }

  Widget _buildCategorySelector(AsyncValue<List<dynamic>> categoriesAsync) {
    return categoriesAsync.when(
      data: (categories) {
        final filteredCategories = categories
            .where((category) => 
                (_selectedType == TransactionType.income && category.type.name == 'income') ||
                (_selectedType == TransactionType.expense && category.type.name == 'expense'))
            .toList();

        return DropdownButtonFormField<String>(
          value: _selectedCategoryId,
          decoration: const InputDecoration(
            labelText: 'Category (Optional)',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.category),
          ),
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('No Category'),
            ),
            ...filteredCategories.map((category) {
              return DropdownMenuItem<String>(
                value: category.id,
                child: Text(category.name),
              );
            }),
          ],
          onChanged: (value) {
            setState(() {
              _selectedCategoryId = value;
            });
          },
        );
      },
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => Text('Error loading categories: $error'),
    );
  }

  Widget _buildFrequencySelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Frequency',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<RecurrenceFrequency>(
              value: _selectedFrequency,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.repeat),
              ),
              items: RecurrenceFrequency.values.map((frequency) {
                return DropdownMenuItem<RecurrenceFrequency>(
                  value: frequency,
                  child: Text(frequency.displayName),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedFrequency = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Start Date',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: Text(_startDate.toString().split(' ')[0]),
              trailing: const Icon(Icons.edit),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _startDate,
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
                );
                if (date != null) {
                  setState(() {
                    _startDate = date;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEndConditions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'End Conditions (Optional)',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            CheckboxListTile(
              title: const Text('Set End Date'),
              value: _endDate != null,
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    _endDate = _startDate.add(const Duration(days: 365));
                  } else {
                    _endDate = null;
                  }
                });
              },
            ),
            if (_endDate != null)
              ListTile(
                leading: const Icon(Icons.event),
                title: Text('End Date: ${_endDate!.toString().split(' ')[0]}'),
                trailing: const Icon(Icons.edit),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _endDate!,
                    firstDate: _startDate,
                    lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
                  );
                  if (date != null) {
                    setState(() {
                      _endDate = date;
                    });
                  }
                },
              ),
            CheckboxListTile(
              title: const Text('Set Maximum Occurrences'),
              value: _maxOccurrences != null,
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    _maxOccurrences = 12;
                  } else {
                    _maxOccurrences = null;
                  }
                });
              },
            ),
            if (_maxOccurrences != null)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextFormField(
                  initialValue: _maxOccurrences.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Maximum Occurrences',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _maxOccurrences = int.tryParse(value);
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutoExecuteSwitch() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Auto Execute',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Automatically create transactions when due',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ),
            Switch(
              value: _autoExecute,
              onChanged: (value) {
                setState(() {
                  _autoExecute = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
