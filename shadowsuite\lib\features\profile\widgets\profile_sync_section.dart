import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/futuristic_theme.dart';
import '../../../core/models/user_model.dart';

class ProfileSyncSection extends ConsumerStatefulWidget {
  final UserModel user;

  const ProfileSyncSection({
    super.key,
    required this.user,
  });

  @override
  ConsumerState<ProfileSyncSection> createState() => _ProfileSyncSectionState();
}

class _ProfileSyncSectionState extends ConsumerState<ProfileSyncSection> {
  bool _isAutoSyncEnabled = true;
  bool _isSyncing = false;
  DateTime? _lastSyncTime;

  @override
  void initState() {
    super.initState();
    _lastSyncTime = DateTime.now().subtract(const Duration(minutes: 5));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: FuturisticTheme.primaryGreen.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: FuturisticTheme.primaryGreen.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.cloud_sync_outlined,
                color: FuturisticTheme.primaryGreen,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Data Sync',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: FuturisticTheme.primaryGreen,
                ),
              ),
              const Spacer(),
              _buildSyncStatusIndicator(),
            ],
          ),
          const SizedBox(height: 20),

          // Sync Status
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: FuturisticTheme.primaryGreen.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: FuturisticTheme.primaryGreen.withOpacity(0.1),
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.cloud_done,
                      color: FuturisticTheme.primaryGreen,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Sync Status',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      _isSyncing ? 'Syncing...' : 'Up to date',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _isSyncing ? Colors.orange : FuturisticTheme.primaryGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                if (_lastSyncTime != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        color: Colors.grey[600],
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Last sync: ${_formatSyncTime(_lastSyncTime!)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Auto Sync Setting
          _buildSyncOption(
            'Auto Sync',
            'Automatically sync data when connected',
            Icons.sync,
            _isAutoSyncEnabled,
            (value) {
              setState(() {
                _isAutoSyncEnabled = value;
              });
            },
          ),

          // Sync Options
          _buildSyncActionTile(
            'Sync Now',
            'Manually sync all data',
            Icons.sync,
            _isSyncing ? null : _performManualSync,
          ),

          _buildSyncActionTile(
            'Download Data',
            'Download latest data from cloud',
            Icons.cloud_download,
            () => _performDataDownload(),
          ),

          _buildSyncActionTile(
            'Upload Data',
            'Upload local data to cloud',
            Icons.cloud_upload,
            () => _performDataUpload(),
          ),

          const SizedBox(height: 16),

          // Sync Statistics
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Sync Statistics',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                _buildStatRow('Memos synced', '24'),
                _buildStatRow('Transactions synced', '156'),
                _buildStatRow('Athkar sessions synced', '12'),
                _buildStatRow('Last full sync', '2 hours ago'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncStatusIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _isSyncing 
            ? Colors.orange.withOpacity(0.1)
            : FuturisticTheme.primaryGreen.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isSyncing 
              ? Colors.orange.withOpacity(0.3)
              : FuturisticTheme.primaryGreen.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_isSyncing)
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
              ),
            )
          else
            Icon(
              Icons.check_circle,
              color: FuturisticTheme.primaryGreen,
              size: 12,
            ),
          const SizedBox(width: 4),
          Text(
            _isSyncing ? 'Syncing' : 'Synced',
            style: TextStyle(
              color: _isSyncing ? Colors.orange : FuturisticTheme.primaryGreen,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncOption(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: FuturisticTheme.primaryGreen.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: FuturisticTheme.primaryGreen,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        trailing: Switch(
          value: value,
          onChanged: onChanged,
          activeColor: FuturisticTheme.primaryGreen,
        ),
      ),
    );
  }

  Widget _buildSyncActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback? onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: FuturisticTheme.primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: FuturisticTheme.primaryBlue,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
        enabled: onTap != null,
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _formatSyncTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Future<void> _performManualSync() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      // Simulate sync process
      await Future.delayed(const Duration(seconds: 3));
      
      setState(() {
        _lastSyncTime = DateTime.now();
        _isSyncing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sync completed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isSyncing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sync failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _performDataDownload() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Download started')),
    );
  }

  void _performDataUpload() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Upload started')),
    );
  }
}
