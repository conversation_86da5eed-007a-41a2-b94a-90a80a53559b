import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../features/money_flow/screens/overview_tab.dart';
import '../../../features/money_flow/screens/accounts_tab.dart';
import '../../../features/money_flow/screens/categories_tab.dart';
import '../../../features/money_flow/screens/transactions_tab.dart';
import '../../../features/money_flow/screens/budgets_tab.dart';

class MoneyFlowScreen extends ConsumerStatefulWidget {
  const MoneyFlowScreen({super.key});

  @override
  ConsumerState<MoneyFlowScreen> createState() => _MoneyFlowScreenState();
}

class _MoneyFlowScreenState extends ConsumerState<MoneyFlowScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Money Flow'),
        automaticallyImplyLeading: false,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Dashboard', icon: Icon(Icons.dashboard)),
            Tab(text: 'Accounts', icon: Icon(Icons.account_balance)),
            Tab(text: 'Categories', icon: Icon(Icons.category)),
            Tab(text: 'Transactions', icon: Icon(Icons.receipt_long)),
            Tab(text: 'Budgets', icon: Icon(Icons.pie_chart)),
            Tab(text: 'Reports', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          OverviewTab(),
          AccountsTab(),
          CategoriesTab(),
          TransactionsTab(),
          BudgetsTab(),
          _ReportsTab(),
        ],
      ),
    );
  }
}

// Placeholder for Reports tab
class _ReportsTab extends StatelessWidget {
  const _ReportsTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.analytics, size: 64, color: Colors.purple),
          SizedBox(height: 16),
          Text(
            'Reports',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Advanced reporting features coming soon...',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }
}
