import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../features/money_flow/screens/overview_tab.dart';
import '../../../features/money_flow/screens/accounts_tab.dart';
import '../../../features/money_flow/screens/categories_tab.dart';
import '../../../features/money_flow/screens/transactions_tab.dart';
import '../../../features/money_flow/screens/recurring_transactions_tab.dart';
import '../../../features/money_flow/screens/budgets_tab.dart';
import '../../../features/money_flow/screens/reports_tab.dart';

class MoneyFlowScreen extends ConsumerStatefulWidget {
  const MoneyFlowScreen({super.key});

  @override
  ConsumerState<MoneyFlowScreen> createState() => _MoneyFlowScreenState();
}

class _MoneyFlowScreenState extends ConsumerState<MoneyFlowScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Money Flow'),
        automaticallyImplyLeading: false,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Dashboard', icon: Icon(Icons.dashboard)),
            Tab(text: 'Accounts', icon: Icon(Icons.account_balance)),
            Tab(text: 'Categories', icon: Icon(Icons.category)),
            Tab(text: 'Transactions', icon: Icon(Icons.receipt_long)),
            Tab(text: 'Recurring', icon: Icon(Icons.repeat)),
            Tab(text: 'Budgets', icon: Icon(Icons.pie_chart)),
            Tab(text: 'Reports', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          const OverviewTab(),
          const AccountsTab(),
          const CategoriesTab(),
          const TransactionsTab(),
          const RecurringTransactionsTab(),
          const BudgetsTab(),
          const ReportsTab(),
        ],
      ),
    );
  }
}
