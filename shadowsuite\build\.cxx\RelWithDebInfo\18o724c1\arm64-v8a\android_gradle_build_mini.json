{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\apps\\android\\shadowsuite\\build\\.cxx\\RelWithDebInfo\\18o724c1\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\apps\\android\\shadowsuite\\build\\.cxx\\RelWithDebInfo\\18o724c1\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}