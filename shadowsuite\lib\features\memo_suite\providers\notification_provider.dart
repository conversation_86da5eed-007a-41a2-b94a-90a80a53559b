import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/notification_service.dart';

// Notification service provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

// Notifications stream provider
final notificationsProvider = StreamProvider<List<NotificationData>>((ref) {
  final service = ref.watch(notificationServiceProvider);
  return service.notificationsStream;
});

// Unread count provider
final unreadNotificationsCountProvider = Provider<int>((ref) {
  final notificationsAsync = ref.watch(notificationsProvider);
  return notificationsAsync.when(
    data: (notifications) => notifications.where((n) => !n.isRead).length,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

// Notification settings provider
final notificationSettingsProvider = StateNotifierProvider<NotificationSettingsNotifier, NotificationSettings>((ref) {
  return NotificationSettingsNotifier(ref.watch(notificationServiceProvider));
});

class NotificationSettings {
  final bool remindersEnabled;
  final bool dueDateNotificationsEnabled;
  final bool overdueNotificationsEnabled;
  final bool syncNotificationsEnabled;
  final int reminderMinutesBefore;

  const NotificationSettings({
    required this.remindersEnabled,
    required this.dueDateNotificationsEnabled,
    required this.overdueNotificationsEnabled,
    required this.syncNotificationsEnabled,
    required this.reminderMinutesBefore,
  });

  NotificationSettings copyWith({
    bool? remindersEnabled,
    bool? dueDateNotificationsEnabled,
    bool? overdueNotificationsEnabled,
    bool? syncNotificationsEnabled,
    int? reminderMinutesBefore,
  }) {
    return NotificationSettings(
      remindersEnabled: remindersEnabled ?? this.remindersEnabled,
      dueDateNotificationsEnabled: dueDateNotificationsEnabled ?? this.dueDateNotificationsEnabled,
      overdueNotificationsEnabled: overdueNotificationsEnabled ?? this.overdueNotificationsEnabled,
      syncNotificationsEnabled: syncNotificationsEnabled ?? this.syncNotificationsEnabled,
      reminderMinutesBefore: reminderMinutesBefore ?? this.reminderMinutesBefore,
    );
  }
}

class NotificationSettingsNotifier extends StateNotifier<NotificationSettings> {
  final NotificationService _notificationService;

  NotificationSettingsNotifier(this._notificationService) : super(
    NotificationSettings(
      remindersEnabled: _notificationService.remindersEnabled,
      dueDateNotificationsEnabled: _notificationService.dueDateNotificationsEnabled,
      overdueNotificationsEnabled: _notificationService.overdueNotificationsEnabled,
      syncNotificationsEnabled: _notificationService.syncNotificationsEnabled,
      reminderMinutesBefore: _notificationService.reminderMinutesBefore,
    ),
  );

  Future<void> updateSettings({
    bool? remindersEnabled,
    bool? dueDateNotificationsEnabled,
    bool? overdueNotificationsEnabled,
    bool? syncNotificationsEnabled,
    int? reminderMinutesBefore,
  }) async {
    await _notificationService.updateSettings(
      remindersEnabled: remindersEnabled,
      dueDateNotificationsEnabled: dueDateNotificationsEnabled,
      overdueNotificationsEnabled: overdueNotificationsEnabled,
      syncNotificationsEnabled: syncNotificationsEnabled,
      reminderMinutesBefore: reminderMinutesBefore,
    );

    state = state.copyWith(
      remindersEnabled: remindersEnabled,
      dueDateNotificationsEnabled: dueDateNotificationsEnabled,
      overdueNotificationsEnabled: overdueNotificationsEnabled,
      syncNotificationsEnabled: syncNotificationsEnabled,
      reminderMinutesBefore: reminderMinutesBefore,
    );
  }
}

// Notification actions provider
final notificationActionsProvider = Provider<NotificationActions>((ref) {
  return NotificationActions(ref.watch(notificationServiceProvider));
});

class NotificationActions {
  final NotificationService _service;

  NotificationActions(this._service);

  Future<void> markAsRead(String notificationId) async {
    await _service.markAsRead(notificationId);
  }

  Future<void> markAllAsRead() async {
    await _service.markAllAsRead();
  }

  Future<void> deleteNotification(String notificationId) async {
    await _service.deleteNotification(notificationId);
  }

  Future<void> clearAllNotifications() async {
    await _service.clearAllNotifications();
  }

  Future<void> scheduleReminderForMemo(memo) async {
    await _service.scheduleReminderForMemo(memo);
  }

  Future<void> scheduleReminderForTodo(memo, todo) async {
    await _service.scheduleReminderForTodo(memo, todo);
  }

  Future<void> cancelReminderForMemo(String memoId) async {
    await _service.cancelReminderForMemo(memoId);
  }

  Future<void> cancelReminderForTodo(String todoId) async {
    await _service.cancelReminderForTodo(todoId);
  }

  Future<void> notifySyncStatus(String message, {bool isError = false}) async {
    await _service.notifySyncStatus(message, isError: isError);
  }

  Future<void> addGeneralNotification(String title, String message) async {
    await _service.addGeneralNotification(title, message);
  }
}
