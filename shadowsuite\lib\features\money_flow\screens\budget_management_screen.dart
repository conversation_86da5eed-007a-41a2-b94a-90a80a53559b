import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/budget_model.dart';
import '../models/category_model.dart';
import '../providers/money_flow_provider.dart';

class BudgetManagementScreen extends ConsumerStatefulWidget {
  const BudgetManagementScreen({super.key});

  @override
  ConsumerState<BudgetManagementScreen> createState() =>
      _BudgetManagementScreenState();
}

class _BudgetManagementScreenState
    extends ConsumerState<BudgetManagementScreen> {
  @override
  Widget build(BuildContext context) {
    final budgetsAsync = ref.watch(budgetsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Budget Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateBudgetDialog(context, categoriesAsync),
          ),
        ],
      ),
      body: budgetsAsync.when(
        data: (budgets) => _buildBudgetsList(context, budgets),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading budgets: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(budgetsProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBudgetsList(BuildContext context, List<BudgetModel> budgets) {
    if (budgets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.pie_chart, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'No budgets created yet',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            const Text(
              'Create your first budget to track spending',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showCreateBudgetDialog(
                context,
                ref.read(categoriesProvider),
              ),
              icon: const Icon(Icons.add),
              label: const Text('Create Budget'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: budgets.length,
      itemBuilder: (context, index) {
        final budget = budgets[index];
        return _buildBudgetCard(context, budget);
      },
    );
  }

  Widget _buildBudgetCard(BuildContext context, BudgetModel budget) {
    final progress = budget.progress;
    final isOverBudget = budget.isOverBudget;
    final isNearLimit = budget.isNearLimit;

    Color progressColor = Colors.green;
    if (isOverBudget) {
      progressColor = Colors.red;
    } else if (isNearLimit) {
      progressColor = Colors.orange;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        budget.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Consumer(
                        builder: (context, ref, child) {
                          final categoriesAsync = ref.watch(categoriesProvider);
                          return categoriesAsync.when(
                            data: (categories) {
                              final category = categories.firstWhere(
                                (cat) => cat.id == budget.categoryId,
                                orElse: () => CategoryModel(
                                  id: '',
                                  name: 'Unknown',
                                  icon: '❓',
                                  type: CategoryType.expense,
                                  userId: '',
                                  colorValue: 0xFF9E9E9E,
                                  createdAt: DateTime.now(),
                                  updatedAt: DateTime.now(),
                                ),
                              );
                              return Text(
                                category.name,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              );
                            },
                            loading: () => Text(
                              'Loading...',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                            error: (_, __) => Text(
                              'Unknown Category',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) =>
                      _handleBudgetAction(context, budget, value.toString()),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Spent: \$${budget.spent.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: progressColor,
                      ),
                    ),
                    Text(
                      'Budget: \$${budget.amount.toStringAsFixed(2)}',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress.clamp(0.0, 1.0),
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${(progress * 100).round()}% used',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      isOverBudget
                          ? 'Over by \$${(budget.spent - budget.amount).toStringAsFixed(2)}'
                          : 'Remaining: \$${budget.remaining.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: isOverBudget ? Colors.red : Colors.grey[600],
                        fontWeight: isOverBudget
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Period and status
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  '${budget.startDate.day}/${budget.startDate.month} - ${budget.endDate.day}/${budget.endDate.month}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                const Spacer(),
                if (isOverBudget)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Over Budget',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                else if (isNearLimit)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Near Limit',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                else
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'On Track',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateBudgetDialog(
    BuildContext context,
    AsyncValue<List<CategoryModel>> categoriesAsync,
  ) {
    categoriesAsync.when(
      data: (categories) => _showBudgetForm(context, categories),
      loading: () => ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Loading categories...'))),
      error: (_, __) => ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Error loading categories'))),
    );
  }

  void _showBudgetForm(
    BuildContext context,
    List<CategoryModel> categories, [
    BudgetModel? editingBudget,
  ]) {
    showDialog(
      context: context,
      builder: (context) => _BudgetFormDialog(
        categories: categories,
        editingBudget: editingBudget,
        onSave: (budget) {
          if (editingBudget != null) {
            ref.read(budgetsProvider.notifier).updateBudget(budget);
          } else {
            ref.read(budgetsProvider.notifier).addBudget(budget);
          }
        },
      ),
    );
  }

  void _handleBudgetAction(
    BuildContext context,
    BudgetModel budget,
    String action,
  ) {
    switch (action) {
      case 'edit':
        final categoriesAsync = ref.read(categoriesProvider);
        categoriesAsync.when(
          data: (categories) => _showBudgetForm(context, categories, budget),
          loading: () => ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Loading categories...')),
          ),
          error: (_, __) => ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Error loading categories')),
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(context, budget);
        break;
    }
  }

  void _showDeleteConfirmation(BuildContext context, BudgetModel budget) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Budget'),
        content: Text('Are you sure you want to delete "${budget.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(budgetsProvider.notifier).deleteBudget(budget.id);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class _BudgetFormDialog extends StatefulWidget {
  final List<CategoryModel> categories;
  final BudgetModel? editingBudget;
  final Function(BudgetModel) onSave;

  const _BudgetFormDialog({
    required this.categories,
    this.editingBudget,
    required this.onSave,
  });

  @override
  State<_BudgetFormDialog> createState() => _BudgetFormDialogState();
}

class _BudgetFormDialogState extends State<_BudgetFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();

  CategoryModel? _selectedCategory;
  DateTime _startDate = DateTime.now();
  DateTime _endDate = DateTime.now().add(const Duration(days: 30));
  double _alertThreshold = 0.8;
  bool _alertEnabled = true;

  @override
  void initState() {
    super.initState();
    if (widget.editingBudget != null) {
      final budget = widget.editingBudget!;
      _nameController.text = budget.name;
      _amountController.text = budget.amount.toString();
      _selectedCategory = widget.categories.firstWhere(
        (cat) => cat.id == budget.categoryId,
        orElse: () => widget.categories.first,
      );
      _startDate = budget.startDate;
      _endDate = budget.endDate;
      _alertThreshold = budget.alertThreshold;
      _alertEnabled = budget.alertEnabled;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.editingBudget != null ? 'Edit Budget' : 'Create Budget',
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Budget Name *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter budget name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                DropdownButtonFormField<CategoryModel>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Category *',
                    border: OutlineInputBorder(),
                  ),
                  items: widget.categories
                      .where((cat) => cat.type == CategoryType.expense)
                      .map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Row(
                            children: [
                              Text(category.icon),
                              const SizedBox(width: 8),
                              Text(category.name),
                            ],
                          ),
                        );
                      })
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Please select a category';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _amountController,
                  decoration: const InputDecoration(
                    labelText: 'Budget Amount *',
                    border: OutlineInputBorder(),
                    prefixText: '\$ ',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter amount';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return 'Please enter valid amount';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _startDate,
                            firstDate: DateTime(2020),
                            lastDate: DateTime(2030),
                          );
                          if (date != null) {
                            setState(() {
                              _startDate = date;
                              if (_endDate.isBefore(_startDate)) {
                                _endDate = _startDate.add(
                                  const Duration(days: 30),
                                );
                              }
                            });
                          }
                        },
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Start Date',
                            border: OutlineInputBorder(),
                          ),
                          child: Text(
                            '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _endDate,
                            firstDate: _startDate,
                            lastDate: DateTime(2030),
                          );
                          if (date != null) {
                            setState(() {
                              _endDate = date;
                            });
                          }
                        },
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'End Date',
                            border: OutlineInputBorder(),
                          ),
                          child: Text(
                            '${_endDate.day}/${_endDate.month}/${_endDate.year}',
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                SwitchListTile(
                  title: const Text('Enable Alerts'),
                  subtitle: const Text(
                    'Get notified when approaching budget limit',
                  ),
                  value: _alertEnabled,
                  onChanged: (value) {
                    setState(() {
                      _alertEnabled = value;
                    });
                  },
                ),

                if (_alertEnabled) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Alert at ${(_alertThreshold * 100).round()}% of budget',
                  ),
                  Slider(
                    value: _alertThreshold,
                    min: 0.5,
                    max: 0.95,
                    divisions: 9,
                    label: '${(_alertThreshold * 100).round()}%',
                    onChanged: (value) {
                      setState(() {
                        _alertThreshold = value;
                      });
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveBudget,
          child: Text(widget.editingBudget != null ? 'Update' : 'Create'),
        ),
      ],
    );
  }

  void _saveBudget() {
    if (_formKey.currentState!.validate()) {
      final budget = BudgetModel(
        id:
            widget.editingBudget?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        userId: 'demo-user-id',
        name: _nameController.text.trim(),
        categoryId: _selectedCategory!.id,
        amount: double.parse(_amountController.text),
        spent: widget.editingBudget?.spent ?? 0.0,
        period: BudgetPeriod.monthly,
        startDate: _startDate,
        endDate: _endDate,
        alertThreshold: _alertThreshold,
        alertEnabled: _alertEnabled,
        isActive: widget.editingBudget?.isActive ?? true,
        createdAt: widget.editingBudget?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      widget.onSave(budget);
      Navigator.of(context).pop();
    }
  }
}
