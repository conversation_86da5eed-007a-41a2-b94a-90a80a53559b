import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/money_flow_provider.dart';
import '../models/recurring_transaction_model.dart';
import '../models/transaction_model.dart';
import 'create_recurring_transaction_screen.dart';

class RecurringTransactionsTab extends ConsumerStatefulWidget {
  const RecurringTransactionsTab({super.key});

  @override
  ConsumerState<RecurringTransactionsTab> createState() =>
      _RecurringTransactionsTabState();
}

class _RecurringTransactionsTabState
    extends ConsumerState<RecurringTransactionsTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final recurringTransactionsAsync = ref.watch(recurringTransactionsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Recurring Transactions'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Active', icon: Icon(Icons.repeat)),
            Tab(text: 'Due', icon: Icon(Icons.schedule)),
            Tab(text: 'History', icon: Icon(Icons.history)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToCreateRecurringTransaction(context),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildActiveTab(context, recurringTransactionsAsync),
          _buildDueTab(context, recurringTransactionsAsync),
          _buildHistoryTab(context, recurringTransactionsAsync),
        ],
      ),
    );
  }

  Widget _buildActiveTab(
    BuildContext context,
    AsyncValue<List<RecurringTransactionModel>> recurringTransactionsAsync,
  ) {
    return recurringTransactionsAsync.when(
      data: (recurringTransactions) {
        final activeTransactions = recurringTransactions
            .where((rt) => rt.isActive && !rt.shouldStop)
            .toList();

        if (activeTransactions.isEmpty) {
          return _buildEmptyState(
            context,
            'No Active Recurring Transactions',
            'Set up recurring transactions to automate your finances',
            Icons.repeat,
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: activeTransactions.length,
          itemBuilder: (context, index) {
            final recurringTransaction = activeTransactions[index];
            return _buildRecurringTransactionCard(
              context,
              recurringTransaction,
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildDueTab(
    BuildContext context,
    AsyncValue<List<RecurringTransactionModel>> recurringTransactionsAsync,
  ) {
    return recurringTransactionsAsync.when(
      data: (recurringTransactions) {
        final dueTransactions = recurringTransactions
            .where((rt) => rt.isActive && rt.isDue && !rt.shouldStop)
            .toList();

        if (dueTransactions.isEmpty) {
          return _buildEmptyState(
            context,
            'No Due Transactions',
            'All recurring transactions are up to date',
            Icons.check_circle,
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: dueTransactions.length,
          itemBuilder: (context, index) {
            final recurringTransaction = dueTransactions[index];
            return _buildDueTransactionCard(context, recurringTransaction);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildHistoryTab(
    BuildContext context,
    AsyncValue<List<RecurringTransactionModel>> recurringTransactionsAsync,
  ) {
    return recurringTransactionsAsync.when(
      data: (recurringTransactions) {
        final executedTransactions = recurringTransactions
            .where((rt) => rt.executionCount > 0)
            .toList();

        if (executedTransactions.isEmpty) {
          return _buildEmptyState(
            context,
            'No Execution History',
            'Recurring transactions will appear here after execution',
            Icons.history,
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: executedTransactions.length,
          itemBuilder: (context, index) {
            final recurringTransaction = executedTransactions[index];
            return _buildHistoryCard(context, recurringTransaction);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildEmptyState(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToCreateRecurringTransaction(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Recurring Transaction'),
          ),
        ],
      ),
    );
  }

  Widget _buildRecurringTransactionCard(
    BuildContext context,
    RecurringTransactionModel recurringTransaction,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getTransactionColor(
            recurringTransaction.type,
          ).withValues(alpha: 0.1),
          child: Icon(
            _getTransactionIcon(recurringTransaction.type),
            color: _getTransactionColor(recurringTransaction.type),
          ),
        ),
        title: Text(
          recurringTransaction.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${recurringTransaction.frequency.displayName} • Next: ${_formatDate(recurringTransaction.nextDue)}',
            ),
            if (recurringTransaction.description != null)
              Text(recurringTransaction.description!),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${recurringTransaction.type == TransactionType.expense ? '-' : '+'}\$${recurringTransaction.amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _getTransactionColor(recurringTransaction.type),
                fontSize: 16,
              ),
            ),
            if (recurringTransaction.autoExecute)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'AUTO',
                  style: TextStyle(fontSize: 10, color: Colors.green),
                ),
              ),
          ],
        ),
        onTap: () =>
            _showRecurringTransactionDetails(context, recurringTransaction),
      ),
    );
  }

  Widget _buildDueTransactionCard(
    BuildContext context,
    RecurringTransactionModel recurringTransaction,
  ) {
    final isOverdue = recurringTransaction.isOverdue;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: isOverdue
          ? Colors.red.withValues(alpha: 0.1)
          : Colors.orange.withValues(alpha: 0.1),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isOverdue
              ? Colors.red.withValues(alpha: 0.2)
              : Colors.orange.withValues(alpha: 0.2),
          child: Icon(
            isOverdue ? Icons.warning : Icons.schedule,
            color: isOverdue ? Colors.red : Colors.orange,
          ),
        ),
        title: Text(
          recurringTransaction.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          isOverdue
              ? 'Overdue since ${_formatDate(recurringTransaction.nextDue)}'
              : 'Due ${_formatDate(recurringTransaction.nextDue)}',
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${recurringTransaction.type == TransactionType.expense ? '-' : '+'}\$${recurringTransaction.amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _getTransactionColor(recurringTransaction.type),
                fontSize: 16,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () =>
                  _executeRecurringTransaction(context, recurringTransaction),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                minimumSize: const Size(60, 32),
              ),
              child: const Text('Execute', style: TextStyle(fontSize: 12)),
            ),
          ],
        ),
        onTap: () =>
            _showRecurringTransactionDetails(context, recurringTransaction),
      ),
    );
  }

  Widget _buildHistoryCard(
    BuildContext context,
    RecurringTransactionModel recurringTransaction,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.grey.withValues(alpha: 0.1),
          child: const Icon(Icons.history, color: Colors.grey),
        ),
        title: Text(recurringTransaction.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${recurringTransaction.frequency.displayName} • Executed ${recurringTransaction.executionCount} times',
            ),
            if (recurringTransaction.lastExecuted != null)
              Text('Last: ${_formatDate(recurringTransaction.lastExecuted!)}'),
          ],
        ),
        trailing: Text(
          '${recurringTransaction.type == TransactionType.expense ? '-' : '+'}\$${recurringTransaction.amount.toStringAsFixed(2)}',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: _getTransactionColor(recurringTransaction.type),
          ),
        ),
        onTap: () =>
            _showRecurringTransactionDetails(context, recurringTransaction),
      ),
    );
  }

  void _showRecurringTransactionDetails(
    BuildContext context,
    RecurringTransactionModel recurringTransaction,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(recurringTransaction.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Amount: \$${recurringTransaction.amount.toStringAsFixed(2)}'),
            Text('Type: ${recurringTransaction.type.displayName}'),
            Text('Frequency: ${recurringTransaction.frequency.displayName}'),
            Text('Next Due: ${_formatDate(recurringTransaction.nextDue)}'),
            Text(
              'Auto Execute: ${recurringTransaction.autoExecute ? 'Yes' : 'No'}',
            ),
            Text('Executions: ${recurringTransaction.executionCount}'),
            if (recurringTransaction.description != null)
              Text('Description: ${recurringTransaction.description}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (recurringTransaction.isDue)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _executeRecurringTransaction(context, recurringTransaction);
              },
              child: const Text('Execute Now'),
            ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToEditRecurringTransaction(
                context,
                recurringTransaction,
              );
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  void _executeRecurringTransaction(
    BuildContext context,
    RecurringTransactionModel recurringTransaction,
  ) {
    // Create the actual transaction
    final transaction = recurringTransaction.toTransaction();
    ref.read(transactionsProvider.notifier).addTransaction(transaction);

    // Update the recurring transaction
    ref
        .read(recurringTransactionsProvider.notifier)
        .executeRecurringTransaction(recurringTransaction.id);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${recurringTransaction.title} executed successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _navigateToCreateRecurringTransaction(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateRecurringTransactionScreen(),
      ),
    );
  }

  void _navigateToEditRecurringTransaction(
    BuildContext context,
    RecurringTransactionModel recurringTransaction,
  ) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateRecurringTransactionScreen(
          editingRecurringTransaction: recurringTransaction,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  IconData _getTransactionIcon(TransactionType transactionType) {
    switch (transactionType) {
      case TransactionType.income:
        return Icons.add;
      case TransactionType.expense:
        return Icons.remove;
      case TransactionType.transfer:
        return Icons.swap_horiz;
    }
  }

  Color _getTransactionColor(TransactionType transactionType) {
    switch (transactionType) {
      case TransactionType.income:
        return Colors.green;
      case TransactionType.expense:
        return Colors.red;
      case TransactionType.transfer:
        return Colors.blue;
    }
  }
}
