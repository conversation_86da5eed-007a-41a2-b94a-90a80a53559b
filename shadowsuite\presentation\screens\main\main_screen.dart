import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/providers/localization_provider.dart';
import '../dashboard/dashboard_screen.dart';
import '../memo_suite/memo_suite_screen.dart';
import '../athkar_pro/athkar_pro_screen.dart';
import '../money_flow/money_flow_screen.dart';
import '../profile/profile_screen.dart';
import '../settings/settings_screen.dart';

// Navigation provider
final navigationIndexProvider = StateProvider<int>((ref) => AppConstants.dashboardIndex);

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(navigationIndexProvider);
    final translate = ref.read(translationProvider);
    final isRTL = ref.watch(isRTLProvider);

    // Navigation destinations
    final destinations = [
      NavigationRailDestination(
        icon: const Icon(Icons.dashboard_outlined),
        selectedIcon: const Icon(Icons.dashboard),
        label: Text(translate('dashboard')),
      ),
      NavigationRailDestination(
        icon: const Icon(Icons.mic_outlined),
        selectedIcon: const Icon(Icons.mic),
        label: Text(translate('memo_suite')),
      ),
      NavigationRailDestination(
        icon: const Icon(Icons.auto_stories_outlined),
        selectedIcon: const Icon(Icons.auto_stories),
        label: Text(translate('athkar_pro')),
      ),
      NavigationRailDestination(
        icon: const Icon(Icons.account_balance_wallet_outlined),
        selectedIcon: const Icon(Icons.account_balance_wallet),
        label: Text(translate('money_flow')),
      ),
      NavigationRailDestination(
        icon: const Icon(Icons.person_outlined),
        selectedIcon: const Icon(Icons.person),
        label: Text(translate('profile')),
      ),
      NavigationRailDestination(
        icon: const Icon(Icons.settings_outlined),
        selectedIcon: const Icon(Icons.settings),
        label: Text(translate('settings')),
      ),
    ];

    // Screen widgets
    final screens = [
      const DashboardScreen(),
      const MemoSuiteScreen(),
      const AthkarProScreen(),
      const MoneyFlowScreen(),
      const ProfileScreen(),
      const SettingsScreen(),
    ];

    return Scaffold(
      body: Row(
        children: [
          // Navigation Rail
          NavigationRail(
            selectedIndex: selectedIndex,
            onDestinationSelected: (index) {
              ref.read(navigationIndexProvider.notifier).state = index;
            },
            labelType: NavigationRailLabelType.all,
            destinations: destinations,
            leading: Padding(
              padding: const EdgeInsets.symmetric(vertical: AppConstants.defaultPadding),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: Theme.of(context).primaryColor,
                    child: const Icon(
                      Icons.security,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'SS',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Vertical Divider
          const VerticalDivider(thickness: 1, width: 1),
          
          // Main Content
          Expanded(
            child: screens[selectedIndex],
          ),
        ],
      ),
    );
  }
}
