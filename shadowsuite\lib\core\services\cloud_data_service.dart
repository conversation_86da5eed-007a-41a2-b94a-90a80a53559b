import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import 'supabase_service.dart';

/// Cloud-first data service that works directly with Supabase
class CloudDataService {
  static CloudDataService? _instance;
  final SupabaseService _supabaseService = SupabaseService();
  
  CloudDataService._internal();
  
  factory CloudDataService() {
    _instance ??= CloudDataService._internal();
    return _instance!;
  }

  bool get isInitialized => _supabaseService.isInitialized;
  SupabaseClient? get client => _supabaseService.client;

  // MEMO OPERATIONS
  Future<List<Map<String, dynamic>>> getMemos(String userId) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('memos')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);
      
      debugPrint('✅ Fetched ${response.length} memos from cloud');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error fetching memos: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createMemo(Map<String, dynamic> memoData) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('memos')
          .insert(memoData)
          .select()
          .single();
      
      debugPrint('✅ Created memo in cloud: ${response['id']}');
      return response;
    } catch (e) {
      debugPrint('❌ Error creating memo: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> updateMemo(String memoId, Map<String, dynamic> updates) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('memos')
          .update(updates)
          .eq('id', memoId)
          .select()
          .single();
      
      debugPrint('✅ Updated memo in cloud: $memoId');
      return response;
    } catch (e) {
      debugPrint('❌ Error updating memo: $e');
      rethrow;
    }
  }

  Future<void> deleteMemo(String memoId) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      await client!.from('memos').delete().eq('id', memoId);
      debugPrint('✅ Deleted memo from cloud: $memoId');
    } catch (e) {
      debugPrint('❌ Error deleting memo: $e');
      rethrow;
    }
  }

  // TRANSACTION OPERATIONS
  Future<List<Map<String, dynamic>>> getTransactions(String userId) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('transactions')
          .select()
          .eq('user_id', userId)
          .order('transaction_date', ascending: false);
      
      debugPrint('✅ Fetched ${response.length} transactions from cloud');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error fetching transactions: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createTransaction(Map<String, dynamic> transactionData) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('transactions')
          .insert(transactionData)
          .select()
          .single();
      
      debugPrint('✅ Created transaction in cloud: ${response['id']}');
      return response;
    } catch (e) {
      debugPrint('❌ Error creating transaction: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> updateTransaction(String transactionId, Map<String, dynamic> updates) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('transactions')
          .update(updates)
          .eq('id', transactionId)
          .select()
          .single();
      
      debugPrint('✅ Updated transaction in cloud: $transactionId');
      return response;
    } catch (e) {
      debugPrint('❌ Error updating transaction: $e');
      rethrow;
    }
  }

  Future<void> deleteTransaction(String transactionId) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      await client!.from('transactions').delete().eq('id', transactionId);
      debugPrint('✅ Deleted transaction from cloud: $transactionId');
    } catch (e) {
      debugPrint('❌ Error deleting transaction: $e');
      rethrow;
    }
  }

  // ATHKAR SESSION OPERATIONS
  Future<List<Map<String, dynamic>>> getAthkarSessions(String userId) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('athkar_sessions')
          .select()
          .eq('user_id', userId)
          .order('session_date', ascending: false);
      
      debugPrint('✅ Fetched ${response.length} athkar sessions from cloud');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error fetching athkar sessions: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createAthkarSession(Map<String, dynamic> sessionData) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('athkar_sessions')
          .insert(sessionData)
          .select()
          .single();
      
      debugPrint('✅ Created athkar session in cloud: ${response['id']}');
      return response;
    } catch (e) {
      debugPrint('❌ Error creating athkar session: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> updateAthkarSession(String sessionId, Map<String, dynamic> updates) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('athkar_sessions')
          .update(updates)
          .eq('id', sessionId)
          .select()
          .single();
      
      debugPrint('✅ Updated athkar session in cloud: $sessionId');
      return response;
    } catch (e) {
      debugPrint('❌ Error updating athkar session: $e');
      rethrow;
    }
  }

  Future<void> deleteAthkarSession(String sessionId) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      await client!.from('athkar_sessions').delete().eq('id', sessionId);
      debugPrint('✅ Deleted athkar session from cloud: $sessionId');
    } catch (e) {
      debugPrint('❌ Error deleting athkar session: $e');
      rethrow;
    }
  }

  // ACCOUNT OPERATIONS
  Future<List<Map<String, dynamic>>> getAccounts(String userId) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('accounts')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);
      
      debugPrint('✅ Fetched ${response.length} accounts from cloud');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error fetching accounts: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createAccount(Map<String, dynamic> accountData) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('accounts')
          .insert(accountData)
          .select()
          .single();
      
      debugPrint('✅ Created account in cloud: ${response['id']}');
      return response;
    } catch (e) {
      debugPrint('❌ Error creating account: $e');
      rethrow;
    }
  }

  // CATEGORY OPERATIONS
  Future<List<Map<String, dynamic>>> getCategories(String userId) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('categories')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);
      
      debugPrint('✅ Fetched ${response.length} categories from cloud');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error fetching categories: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createCategory(Map<String, dynamic> categoryData) async {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    try {
      final response = await client!
          .from('categories')
          .insert(categoryData)
          .select()
          .single();
      
      debugPrint('✅ Created category in cloud: ${response['id']}');
      return response;
    } catch (e) {
      debugPrint('❌ Error creating category: $e');
      rethrow;
    }
  }

  // REAL-TIME SUBSCRIPTIONS
  RealtimeChannel subscribeToMemos(String userId, Function(List<Map<String, dynamic>>) onData) {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    return client!
        .channel('memos_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'memos',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) {
            debugPrint('📡 Real-time memo update received');
            // Refetch memos and call callback
            getMemos(userId).then(onData).catchError((e) {
              debugPrint('❌ Error in real-time memo callback: $e');
            });
          },
        )
        .subscribe();
  }

  RealtimeChannel subscribeToTransactions(String userId, Function(List<Map<String, dynamic>>) onData) {
    if (!isInitialized) throw Exception('Cloud service not initialized');
    
    return client!
        .channel('transactions_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'transactions',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) {
            debugPrint('📡 Real-time transaction update received');
            // Refetch transactions and call callback
            getTransactions(userId).then(onData).catchError((e) {
              debugPrint('❌ Error in real-time transaction callback: $e');
            });
          },
        )
        .subscribe();
  }
}
