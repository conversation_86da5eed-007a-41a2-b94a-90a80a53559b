import 'package:json_annotation/json_annotation.dart';

part 'todo_item_model.g.dart';

enum Priority {
  low,
  medium,
  high,
  urgent,
}

@JsonSerializable()
class TodoItemModel {
  final String id;
  final String text;
  final bool isCompleted;
  final DateTime? dueDate;
  final Priority priority;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TodoItemModel({
    required this.id,
    required this.text,
    this.isCompleted = false,
    this.dueDate,
    this.priority = Priority.medium,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TodoItemModel.fromJson(Map<String, dynamic> json) =>
      _$TodoItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$TodoItemModelToJson(this);

  TodoItemModel copyWith({
    String? id,
    String? text,
    bool? isCompleted,
    DateTime? dueDate,
    Priority? priority,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TodoItemModel(
      id: id ?? this.id,
      text: text ?? this.text,
      isCompleted: isCompleted ?? this.isCompleted,
      dueDate: dueDate ?? this.dueDate,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TodoItemModel &&
        other.id == id &&
        other.text == text &&
        other.isCompleted == isCompleted &&
        other.dueDate == dueDate &&
        other.priority == priority;
  }

  @override
  int get hashCode => id.hashCode;
}
