import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dhikr_model.dart';
import '../providers/dhikr_provider.dart';

class AthkarCreationScreen extends ConsumerStatefulWidget {
  final DhikrModel? dhikr; // For editing existing dhikr

  const AthkarCreationScreen({super.key, this.dhikr});

  @override
  ConsumerState<AthkarCreationScreen> createState() =>
      _AthkarCreationScreenState();
}

class _AthkarCreationScreenState extends ConsumerState<AthkarCreationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _arabicController = TextEditingController();
  final _transliterationController = TextEditingController();
  final _translationController = TextEditingController();
  final _targetCountController = TextEditingController();
  final _sourceController = TextEditingController();
  final _benefitsController = TextEditingController();

  DhikrCategory _selectedCategory = DhikrCategory.general;
  List<String> _tags = [];
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.dhikr != null) {
      final dhikr = widget.dhikr!;
      _arabicController.text = dhikr.arabicText;
      _transliterationController.text = dhikr.transliteration;
      _translationController.text = dhikr.translation;
      _targetCountController.text = dhikr.targetCount.toString();
      _sourceController.text = dhikr.source ?? '';
      _benefitsController.text = dhikr.benefits.join(', ');
      // Convert string category to enum
      _selectedCategory = DhikrCategory.values.firstWhere(
        (cat) => cat.name == dhikr.category,
        orElse: () => DhikrCategory.general,
      );
      _tags = []; // DhikrModel doesn't have tags, so initialize empty
      _isActive = true; // DhikrModel doesn't have isActive, so default to true
    } else {
      _targetCountController.text = '33';
    }
  }

  @override
  void dispose() {
    _arabicController.dispose();
    _transliterationController.dispose();
    _translationController.dispose();
    _targetCountController.dispose();
    _sourceController.dispose();
    _benefitsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.dhikr != null ? 'Edit Dhikr' : 'Create Dhikr'),
        actions: [TextButton(onPressed: _saveDhikr, child: const Text('Save'))],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Arabic text section
              _buildArabicSection(),
              const SizedBox(height: 24),

              // Translation section
              _buildTranslationSection(),
              const SizedBox(height: 24),

              // Configuration section
              _buildConfigurationSection(),
              const SizedBox(height: 24),

              // Additional information section
              _buildAdditionalInfoSection(),
              const SizedBox(height: 24),

              // Tags section
              _buildTagsSection(),
              const SizedBox(height: 24),

              // Settings section
              _buildSettingsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildArabicSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.text_fields, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Arabic Text',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _arabicController,
              decoration: const InputDecoration(
                labelText: 'Arabic Text *',
                border: OutlineInputBorder(),
                hintText: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
                helperText: 'Enter the dhikr in Arabic script',
              ),
              textDirection: TextDirection.rtl,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                height: 1.5,
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter Arabic text';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.green[700], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Tip: Use Arabic keyboard or copy-paste from reliable Islamic sources',
                      style: TextStyle(color: Colors.green[700], fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTranslationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.translate, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Translation & Pronunciation',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _transliterationController,
              decoration: const InputDecoration(
                labelText: 'Transliteration *',
                border: OutlineInputBorder(),
                hintText: 'Subhan Allahi wa bihamdihi',
                helperText: 'Pronunciation guide in English letters',
              ),
              style: const TextStyle(fontStyle: FontStyle.italic, fontSize: 16),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter transliteration';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _translationController,
              decoration: const InputDecoration(
                labelText: 'Translation *',
                border: OutlineInputBorder(),
                hintText: 'Glory be to Allah and praise be to Him',
                helperText: 'Meaning in English',
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter translation';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Configuration',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _targetCountController,
                    decoration: const InputDecoration(
                      labelText: 'Target Count *',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.repeat),
                      helperText: 'Recommended repetitions',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter target count';
                      }
                      final number = int.tryParse(value.trim());
                      if (number == null || number <= 0) {
                        return 'Please enter a valid positive number';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<DhikrCategory>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                    ),
                    items: DhikrCategory.values.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category.displayName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedCategory = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Additional Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _sourceController,
              decoration: const InputDecoration(
                labelText: 'Source (Optional)',
                border: OutlineInputBorder(),
                hintText: 'Quran 2:255, Sahih Bukhari 123, etc.',
                helperText:
                    'Reference from Quran, Hadith, or Islamic literature',
              ),
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _benefitsController,
              decoration: const InputDecoration(
                labelText: 'Benefits (Optional)',
                border: OutlineInputBorder(),
                hintText: 'Spiritual benefits and rewards',
                helperText: 'Describe the spiritual benefits of this dhikr',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTagsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Icons.tag, color: Colors.teal),
                    const SizedBox(width: 8),
                    Text(
                      'Tags',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: _addTag,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Tag'),
                ),
              ],
            ),
            const SizedBox(height: 8),

            if (_tags.isEmpty)
              Text(
                'No tags added. Tags help organize and search your dhikr.',
                style: TextStyle(color: Colors.grey[600]),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _tags
                    .map(
                      (tag) => Chip(
                        label: Text(tag),
                        onDeleted: () => _removeTag(tag),
                        deleteIcon: const Icon(Icons.close, size: 18),
                      ),
                    )
                    .toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.tune, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Active'),
              subtitle: const Text('Include this dhikr in daily practice'),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  void _addTag() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('Add Tag'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: 'Tag name',
              border: OutlineInputBorder(),
              hintText: 'morning, protection, gratitude',
            ),
            textCapitalization: TextCapitalization.words,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final tag = controller.text.trim().toLowerCase();
                if (tag.isNotEmpty && !_tags.contains(tag)) {
                  setState(() {
                    _tags.add(tag);
                  });
                }
                Navigator.of(context).pop();
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  void _saveDhikr() {
    if (_formKey.currentState!.validate()) {
      // Convert benefits text to list
      final benefitsList = _benefitsController.text.trim().isNotEmpty
          ? _benefitsController.text
                .trim()
                .split(',')
                .map((e) => e.trim())
                .toList()
          : <String>[];

      final dhikr = DhikrModel(
        id:
            widget.dhikr?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        arabicText: _arabicController.text.trim(),
        transliteration: _transliterationController.text.trim(),
        translation: _translationController.text.trim(),
        category: _selectedCategory.name, // Convert enum to string
        targetCount: int.parse(_targetCountController.text.trim()),
        currentCount: widget.dhikr?.currentCount ?? 0,
        source: _sourceController.text.trim().isNotEmpty
            ? _sourceController.text.trim()
            : null,
        benefits: benefitsList,
        createdAt: widget.dhikr?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.dhikr != null) {
        ref.read(dhikrListProvider.notifier).updateDhikr(dhikr);
      } else {
        ref.read(dhikrListProvider.notifier).addDhikr(dhikr);
      }

      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            widget.dhikr != null ? 'Dhikr updated!' : 'Dhikr created!',
          ),
        ),
      );
    }
  }
}
