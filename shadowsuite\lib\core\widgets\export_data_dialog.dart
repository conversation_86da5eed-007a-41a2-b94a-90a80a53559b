import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:file_picker/file_picker.dart';  // Temporarily disabled for APK build
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../services/database_service.dart';
import '../constants/app_constants.dart';

class ExportDataDialog extends ConsumerStatefulWidget {
  const ExportDataDialog({super.key});

  @override
  ConsumerState<ExportDataDialog> createState() => _ExportDataDialogState();
}

class _ExportDataDialogState extends ConsumerState<ExportDataDialog> {
  bool _isExporting = false;
  final Map<String, bool> _selectedTables = {
    'memos': true,
    'accounts': true,
    'categories': true,
    'transactions': true,
    'budgets': true,
    'athkar_routines': true,
    'athkar_steps': true,
  };

  Future<void> _exportData() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // Use app documents directory for export (file picker temporarily disabled)
      final directory = await getApplicationDocumentsDirectory();
      final selectedDirectory = directory.path;

      final databaseService = DatabaseService();
      final db = await databaseService.database;

      final exportData = <String, dynamic>{
        'export_info': {
          'app_name': AppConstants.appName,
          'app_version': AppConstants.appVersion,
          'export_date': DateTime.now().toIso8601String(),
          'exported_tables': _selectedTables.entries
              .where((entry) => entry.value)
              .map((entry) => entry.key)
              .toList(),
        },
        'data': <String, dynamic>{},
      };

      // Export selected tables
      for (final entry in _selectedTables.entries) {
        if (entry.value) {
          final tableName = entry.key;
          final records = await db.query(tableName);
          exportData['data'][tableName] = records;
        }
      }

      // Create export file
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'shadowsuite_export_$timestamp.json';
      final filePath = '$selectedDirectory/$fileName';

      final file = File(filePath);
      await file.writeAsString(
        const JsonEncoder.withIndent('  ').convert(exportData),
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Data exported successfully to $fileName'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Open Folder',
              onPressed: () {
                // TODO: Open file explorer to the export location
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.download,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Export Data',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Select data to export:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Table selection
                    ..._selectedTables.entries.map((entry) {
                      return CheckboxListTile(
                        title: Text(_getTableDisplayName(entry.key)),
                        subtitle: Text(_getTableDescription(entry.key)),
                        value: entry.value,
                        onChanged: (value) {
                          setState(() {
                            _selectedTables[entry.key] = value ?? false;
                          });
                        },
                      );
                    }),

                    const SizedBox(height: 24),

                    // Export info
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.surfaceVariant.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.info,
                                size: 20,
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurfaceVariant,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Export Information',
                                style: Theme.of(context).textTheme.titleSmall
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '• Data will be exported as a JSON file\n'
                            '• File includes metadata and timestamps\n'
                            '• You can import this file later\n'
                            '• Personal data is included - keep secure',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(24),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isExporting
                          ? null
                          : () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed:
                          _isExporting || !_selectedTables.values.any((v) => v)
                          ? null
                          : _exportData,
                      icon: _isExporting
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.download),
                      label: Text(_isExporting ? 'Exporting...' : 'Export'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getTableDisplayName(String tableName) {
    switch (tableName) {
      case 'memos':
        return 'Memo Suite';
      case 'accounts':
        return 'Money Flow Accounts';
      case 'categories':
        return 'Money Flow Categories';
      case 'transactions':
        return 'Money Flow Transactions';
      case 'budgets':
        return 'Money Flow Budgets';
      case 'athkar_routines':
        return 'Athkar Pro Routines';
      case 'athkar_steps':
        return 'Athkar Pro Steps';
      default:
        return tableName;
    }
  }

  String _getTableDescription(String tableName) {
    switch (tableName) {
      case 'memos':
        return 'Text notes, voice memos, and file attachments';
      case 'accounts':
        return 'Bank accounts and financial accounts';
      case 'categories':
        return 'Income and expense categories';
      case 'transactions':
        return 'Financial transactions and transfers';
      case 'budgets':
        return 'Budget plans and tracking';
      case 'athkar_routines':
        return 'Islamic prayer routines and schedules';
      case 'athkar_steps':
        return 'Individual dhikr steps and prayers';
      default:
        return 'Application data';
    }
  }
}

// Helper function to show export data dialog
void showExportDataDialog(BuildContext context) {
  showDialog(context: context, builder: (context) => const ExportDataDialog());
}
