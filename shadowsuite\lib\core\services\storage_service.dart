import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import '../constants/app_constants.dart';

/// Universal storage service that works on both web and mobile
class StorageService {
  static StorageService? _instance;
  Database? _database;
  SharedPreferences? _prefs;
  bool _isInitialized = false;

  StorageService._internal();

  factory StorageService() {
    _instance ??= StorageService._internal();
    return _instance!;
  }

  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kIsWeb) {
        // For web, use SharedPreferences
        _prefs = await SharedPreferences.getInstance();
        await _ensureWebTablesExist();
        debugPrint('✅ Web storage (SharedPreferences) initialized');
      } else {
        // For mobile, use SQLite
        await _initMobileDatabase();
        debugPrint('✅ Mobile storage (SQLite) initialized');
      }
      _isInitialized = true;

      // Verify storage is working
      await _verifyStorageIntegrity();
    } catch (e) {
      debugPrint('❌ Storage initialization failed: $e');
      rethrow;
    }
  }

  Future<void> _ensureWebTablesExist() async {
    // Ensure essential tables exist in web storage
    final tables = ['users', 'memos', 'transactions', 'athkar_sessions'];
    for (final table in tables) {
      final key = 'table_$table';
      if (!_prefs!.containsKey(key)) {
        await _prefs!.setString(key, '[]');
        debugPrint('Created web table: $table');
      }
    }
  }

  Future<void> _verifyStorageIntegrity() async {
    try {
      // Test basic operations
      if (kIsWeb) {
        final testKey = 'test_${DateTime.now().millisecondsSinceEpoch}';
        await _prefs!.setString(testKey, 'test_value');
        final value = _prefs!.getString(testKey);
        await _prefs!.remove(testKey);

        if (value != 'test_value') {
          throw Exception('Web storage integrity test failed');
        }
      } else {
        await _database!.rawQuery(
          'SELECT COUNT(*) FROM sqlite_master WHERE type="table"',
        );
      }

      debugPrint('✅ Storage integrity verified');
    } catch (e) {
      debugPrint('❌ Storage integrity check failed: $e');
      throw Exception('Storage integrity verification failed: $e');
    }
  }

  Future<void> _initMobileDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, AppConstants.databaseName);

    _database = await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create essential tables for mobile
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        display_name TEXT,
        avatar_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        preferences TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE memos (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        title TEXT NOT NULL,
        content TEXT,
        type TEXT NOT NULL DEFAULT 'text',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0
      )
    ''');

    // Add other essential tables as needed
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations
  }

  // Universal CRUD operations
  Future<void> insert(String table, Map<String, dynamic> data) async {
    if (kIsWeb) {
      await _insertWeb(table, data);
    } else {
      await _insertMobile(table, data);
    }
  }

  Future<List<Map<String, dynamic>>> query(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
  }) async {
    if (kIsWeb) {
      return await _queryWeb(
        table,
        where: where,
        whereArgs: whereArgs,
        orderBy: orderBy,
      );
    } else {
      return await _queryMobile(
        table,
        where: where,
        whereArgs: whereArgs,
        orderBy: orderBy,
      );
    }
  }

  Future<void> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    if (kIsWeb) {
      await _updateWeb(table, data, where: where, whereArgs: whereArgs);
    } else {
      await _updateMobile(table, data, where: where, whereArgs: whereArgs);
    }
  }

  Future<void> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    if (kIsWeb) {
      await _deleteWeb(table, where: where, whereArgs: whereArgs);
    } else {
      await _deleteMobile(table, where: where, whereArgs: whereArgs);
    }
  }

  // Web implementations using SharedPreferences
  Future<void> _insertWeb(String table, Map<String, dynamic> data) async {
    final tableData = await _getTableDataWeb(table);
    tableData.add(data);
    await _saveTableDataWeb(table, tableData);
  }

  Future<List<Map<String, dynamic>>> _queryWeb(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
  }) async {
    final tableData = await _getTableDataWeb(table);

    // Simple filtering (basic implementation)
    if (where != null && whereArgs != null) {
      // Basic where clause support (id = ?, user_id = ?, etc.)
      final parts = where.split(' = ');
      if (parts.length == 2) {
        final field = parts[0].trim();
        final value = whereArgs.first;
        return tableData.where((row) => row[field] == value).toList();
      }
    }

    return tableData;
  }

  Future<void> _updateWeb(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final tableData = await _getTableDataWeb(table);

    if (where != null && whereArgs != null) {
      final parts = where.split(' = ');
      if (parts.length == 2) {
        final field = parts[0].trim();
        final value = whereArgs.first;

        for (int i = 0; i < tableData.length; i++) {
          if (tableData[i][field] == value) {
            tableData[i] = {...tableData[i], ...data};
            break;
          }
        }

        await _saveTableDataWeb(table, tableData);
      }
    }
  }

  Future<void> _deleteWeb(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final tableData = await _getTableDataWeb(table);

    if (where != null && whereArgs != null) {
      final parts = where.split(' = ');
      if (parts.length == 2) {
        final field = parts[0].trim();
        final value = whereArgs.first;

        tableData.removeWhere((row) => row[field] == value);
        await _saveTableDataWeb(table, tableData);
      }
    }
  }

  Future<List<Map<String, dynamic>>> _getTableDataWeb(String table) async {
    final jsonString = _prefs?.getString('table_$table') ?? '[]';
    final List<dynamic> jsonList = json.decode(jsonString);
    return jsonList.cast<Map<String, dynamic>>();
  }

  Future<void> _saveTableDataWeb(
    String table,
    List<Map<String, dynamic>> data,
  ) async {
    final jsonString = json.encode(data);
    await _prefs?.setString('table_$table', jsonString);
  }

  // Mobile implementations using SQLite
  Future<void> _insertMobile(String table, Map<String, dynamic> data) async {
    await _database?.insert(table, data);
  }

  Future<List<Map<String, dynamic>>> _queryMobile(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
  }) async {
    return await _database?.query(
          table,
          where: where,
          whereArgs: whereArgs,
          orderBy: orderBy,
        ) ??
        [];
  }

  Future<void> _updateMobile(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    await _database?.update(table, data, where: where, whereArgs: whereArgs);
  }

  Future<void> _deleteMobile(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    await _database?.delete(table, where: where, whereArgs: whereArgs);
  }

  Future<void> close() async {
    await _database?.close();
    _database = null;
    _isInitialized = false;
  }

  Future<void> clear() async {
    if (kIsWeb) {
      await _prefs?.clear();
    } else {
      // For mobile, drop all tables and recreate
      await close();
      await _initMobileDatabase();
    }
  }
}
