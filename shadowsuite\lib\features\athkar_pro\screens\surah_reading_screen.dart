import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/quran_model.dart';
import '../providers/bookmark_provider.dart';

class SurahReadingScreen extends ConsumerStatefulWidget {
  final SurahModel surah;

  const SurahReadingScreen({super.key, required this.surah});

  @override
  ConsumerState<SurahReadingScreen> createState() => _SurahReadingScreenState();
}

class _SurahReadingScreenState extends ConsumerState<SurahReadingScreen> {
  final ScrollController _scrollController = ScrollController();
  int _currentVerseIndex = 0;
  bool _showTranslation = true;
  bool _showTransliteration = false;
  double _fontSize = 18.0;
  String _selectedTranslation = 'english';

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.surah.nameEnglish),
            Text(
              widget.surah.nameArabic,
              style: const TextStyle(fontSize: 14),
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_border),
            onPressed: _toggleBookmark,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showReadingSettings,
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('Share Surah'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'audio',
                child: Row(
                  children: [
                    Icon(Icons.play_arrow),
                    SizedBox(width: 8),
                    Text('Audio Recitation'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'search',
                child: Row(
                  children: [
                    Icon(Icons.search),
                    SizedBox(width: 8),
                    Text('Search in Surah'),
                  ],
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
      ),
      body: Column(
        children: [
          // Surah header
          _buildSurahHeader(),

          // Reading progress
          _buildProgressIndicator(),

          // Verses
          Expanded(child: _buildVersesList()),
        ],
      ),
      bottomNavigationBar: _buildBottomControls(),
    );
  }

  Widget _buildSurahHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: Colors.green.withValues(alpha: 0.3)),
        ),
      ),
      child: Column(
        children: [
          Text(
            widget.surah.nameArabic,
            style: const TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 8),
          Text(
            '${widget.surah.nameEnglish} (${widget.surah.nameTransliteration})',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
          Text(
            '${widget.surah.meaning} • ${widget.surah.versesCount} verses • ${widget.surah.revelationType}',
            style: TextStyle(color: Colors.grey[600], fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final progress = widget.surah.readingProgress;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Reading Progress',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: TextStyle(
                  color: Colors.green[700],
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
          ),
        ],
      ),
    );
  }

  Widget _buildVersesList() {
    // Mock verses for demonstration
    final verses = _getMockVerses();

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: verses.length,
      itemBuilder: (context, index) {
        final verse = verses[index];
        return _buildVerseCard(verse, index + 1);
      },
    );
  }

  Widget _buildVerseCard(VerseModel verse, int verseNumber) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Verse number and actions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Verse $verseNumber',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Consumer(
                      builder: (context, ref, child) {
                        final isBookmarked = ref.watch(
                          verseBookmarkProvider(
                            '${widget.surah.number}:$verseNumber',
                          ),
                        );
                        return IconButton(
                          icon: Icon(
                            isBookmarked
                                ? Icons.bookmark
                                : Icons.bookmark_border,
                            color: isBookmarked ? Colors.orange : Colors.grey,
                          ),
                          onPressed: () => _toggleVerseBookmark(verseNumber),
                        );
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.share, color: Colors.grey),
                      onPressed: () => _shareVerse(verse, verseNumber),
                    ),
                    IconButton(
                      icon: const Icon(Icons.play_arrow, color: Colors.grey),
                      onPressed: () => _playVerseAudio(verse),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Arabic text
            Text(
              verse.textArabic,
              style: TextStyle(
                fontSize: _fontSize + 4,
                fontWeight: FontWeight.w500,
                height: 1.8,
              ),
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
            ),

            if (_showTransliteration && verse.transliteration != null) ...[
              const SizedBox(height: 12),
              Text(
                verse.transliteration!,
                style: TextStyle(
                  fontSize: _fontSize - 2,
                  fontStyle: FontStyle.italic,
                  color: Colors.blue[700],
                ),
              ),
            ],

            if (_showTranslation && verse.translations.isNotEmpty) ...[
              const SizedBox(height: 12),
              ...verse.translations.map(
                (translation) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Text(
                    translation.text,
                    style: TextStyle(
                      fontSize: _fontSize,
                      color: Colors.grey[700],
                      height: 1.5,
                    ),
                  ),
                ),
              ),
            ],

            // Keywords/tags
            if (verse.keywords.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children: verse.keywords
                    .map(
                      (keyword) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          keyword,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.blue[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            icon: const Icon(Icons.remove),
            onPressed: () {
              setState(() {
                _fontSize = (_fontSize - 1).clamp(12.0, 24.0);
              });
            },
          ),
          Text(
            'Font Size: ${_fontSize.round()}',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              setState(() {
                _fontSize = (_fontSize + 1).clamp(12.0, 24.0);
              });
            },
          ),
          const VerticalDivider(),
          IconButton(
            icon: Icon(
              _showTranslation ? Icons.translate : Icons.translate_outlined,
              color: _showTranslation ? Colors.blue : Colors.grey,
            ),
            onPressed: () {
              setState(() {
                _showTranslation = !_showTranslation;
              });
            },
          ),
          IconButton(
            icon: Icon(
              _showTransliteration
                  ? Icons.text_fields
                  : Icons.text_fields_outlined,
              color: _showTransliteration ? Colors.blue : Colors.grey,
            ),
            onPressed: () {
              setState(() {
                _showTransliteration = !_showTransliteration;
              });
            },
          ),
        ],
      ),
    );
  }

  void _toggleBookmark() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${widget.surah.nameEnglish} bookmark toggled')),
    );
  }

  void _showReadingSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Reading Settings',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Show Translation'),
              value: _showTranslation,
              onChanged: (value) {
                setState(() {
                  _showTranslation = value;
                });
                Navigator.pop(context);
              },
            ),
            SwitchListTile(
              title: const Text('Show Transliteration'),
              value: _showTransliteration,
              onChanged: (value) {
                setState(() {
                  _showTransliteration = value;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _shareSurah();
        break;
      case 'audio':
        _playAudioRecitation();
        break;
      case 'search':
        _searchInSurah();
        break;
    }
  }

  void _toggleVerseBookmark(int verseNumber) {
    ref
        .read(bookmarksProvider.notifier)
        .toggleVerseBookmark(widget.surah.number, verseNumber);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Verse $verseNumber bookmark toggled')),
    );
  }

  void _shareVerse(VerseModel verse, int verseNumber) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Sharing verse $verseNumber')));
  }

  void _playVerseAudio(VerseModel verse) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Playing audio for verse ${verse.number}')),
    );
  }

  void _shareSurah() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Sharing ${widget.surah.nameEnglish}')),
    );
  }

  void _playAudioRecitation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Audio recitation feature coming soon!')),
    );
  }

  void _searchInSurah() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Search in Surah feature coming soon!')),
    );
  }

  List<VerseModel> _getMockVerses() {
    // Mock verses for Al-Fatiha
    if (widget.surah.number == 1) {
      return [
        VerseModel(
          number: 1,
          textArabic: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
          textUthmani: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
          translations: [
            TranslationModel(
              id: 'en_1',
              language: 'English',
              translatorName: 'Sahih International',
              text:
                  'In the name of Allah, the Entirely Merciful, the Especially Merciful.',
            ),
          ],
          transliteration: 'Bismillahi\'r-rahmani\'r-rahim',
          keywords: ['bismillah', 'mercy', 'compassion'],
        ),
        VerseModel(
          number: 2,
          textArabic: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
          textUthmani: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
          translations: [
            TranslationModel(
              id: 'en_2',
              language: 'English',
              translatorName: 'Sahih International',
              text: '[All] praise is [due] to Allah, Lord of the worlds -',
            ),
          ],
          transliteration: 'Alhamdu lillahi rabbi\'l-\'alamin',
          keywords: ['praise', 'lord', 'worlds'],
        ),
        // Add more verses as needed
      ];
    }

    // Return empty list for other surahs for now
    return [];
  }
}
