import 'package:json_annotation/json_annotation.dart';

part 'athkar_model.g.dart';

@JsonSerializable()
class AthkarRoutineModel {
  final String id;
  final String userId;
  final String name;
  final String? description;
  final List<AthkarStepModel> steps;
  final int targetCount;
  final int currentCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final bool isSynced;
  final String? reminderTime;
  final List<String> reminderDays;

  const AthkarRoutineModel({
    required this.id,
    required this.userId,
    required this.name,
    this.description,
    this.steps = const [],
    this.targetCount = 1,
    this.currentCount = 0,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.isSynced = false,
    this.reminderTime,
    this.reminderDays = const [],
  });

  factory AthkarRoutineModel.fromJson(Map<String, dynamic> json) =>
      _$AthkarRoutineModelFromJson(json);

  Map<String, dynamic> toJson() => _$AthkarRoutineModelToJson(this);

  AthkarRoutineModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? description,
    List<AthkarStepModel>? steps,
    int? targetCount,
    int? currentCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? isSynced,
    String? reminderTime,
    List<String>? reminderDays,
  }) {
    return AthkarRoutineModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      description: description ?? this.description,
      steps: steps ?? this.steps,
      targetCount: targetCount ?? this.targetCount,
      currentCount: currentCount ?? this.currentCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      isSynced: isSynced ?? this.isSynced,
      reminderTime: reminderTime ?? this.reminderTime,
      reminderDays: reminderDays ?? this.reminderDays,
    );
  }

  double get progressPercentage {
    if (targetCount == 0) return 0.0;
    return (currentCount / targetCount).clamp(0.0, 1.0);
  }

  bool get isCompleted => currentCount >= targetCount;
}

@JsonSerializable()
class AthkarStepModel {
  final String id;
  final String routineId;
  final String arabicText;
  final String? transliteration;
  final String? translation;
  final int repetitions;
  final int currentRepetitions;
  final int order;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AthkarStepModel({
    required this.id,
    required this.routineId,
    required this.arabicText,
    this.transliteration,
    this.translation,
    this.repetitions = 1,
    this.currentRepetitions = 0,
    this.order = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AthkarStepModel.fromJson(Map<String, dynamic> json) =>
      _$AthkarStepModelFromJson(json);

  Map<String, dynamic> toJson() => _$AthkarStepModelToJson(this);

  AthkarStepModel copyWith({
    String? id,
    String? routineId,
    String? arabicText,
    String? transliteration,
    String? translation,
    int? repetitions,
    int? currentRepetitions,
    int? order,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AthkarStepModel(
      id: id ?? this.id,
      routineId: routineId ?? this.routineId,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      repetitions: repetitions ?? this.repetitions,
      currentRepetitions: currentRepetitions ?? this.currentRepetitions,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  double get progressPercentage {
    if (repetitions == 0) return 0.0;
    return (currentRepetitions / repetitions).clamp(0.0, 1.0);
  }

  bool get isCompleted => currentRepetitions >= repetitions;
}
