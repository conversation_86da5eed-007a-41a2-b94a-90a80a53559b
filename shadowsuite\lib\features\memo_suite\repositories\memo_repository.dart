import 'dart:convert';
import '../../../core/services/database_service.dart';
import '../../../core/models/memo_model.dart';

class MemoRepository {
  final DatabaseService _databaseService = DatabaseService();
  static const String _userId = 'demo-user-id';

  Future<List<MemoModel>> getAllMemos() async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'memos',
      where: 'user_id = ?',
      whereArgs: [_userId],
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => _memoFromMap(map)).toList();
  }

  Future<MemoModel?> getMemoById(String id) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'memos',
      where: 'id = ? AND user_id = ?',
      whereArgs: [id, _userId],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return _memoFromMap(maps.first);
    }
    return null;
  }

  Future<void> insertMemo(MemoModel memo) async {
    final db = await _databaseService.database;
    await db.insert('memos', _memoToMap(memo));
  }

  Future<void> updateMemo(MemoModel memo) async {
    final db = await _databaseService.database;
    await db.update(
      'memos',
      _memoToMap(memo),
      where: 'id = ? AND user_id = ?',
      whereArgs: [memo.id, _userId],
    );
  }

  Future<void> deleteMemo(String id) async {
    final db = await _databaseService.database;
    await db.delete(
      'memos',
      where: 'id = ? AND user_id = ?',
      whereArgs: [id, _userId],
    );
  }

  Future<List<MemoModel>> searchMemos(String query) async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'memos',
      where: 'user_id = ? AND (title LIKE ? OR description LIKE ? OR transcription LIKE ?)',
      whereArgs: [_userId, '%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => _memoFromMap(map)).toList();
  }

  MemoModel _memoFromMap(Map<String, dynamic> map) {
    return MemoModel(
      id: map['id'],
      userId: map['user_id'],
      title: map['title'],
      description: map['description'],
      transcription: map['transcription'],
      audioFilePath: map['audio_file_path'],
      audioUrl: map['audio_url'],
      duration: map['duration'] ?? 0,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
      isSynced: map['is_synced'] == 1,
      tags: map['tags'] != null ? List<String>.from(jsonDecode(map['tags'])) : [],
    );
  }

  Map<String, dynamic> _memoToMap(MemoModel memo) {
    return {
      'id': memo.id,
      'user_id': memo.userId,
      'title': memo.title,
      'description': memo.description,
      'transcription': memo.transcription,
      'audio_file_path': memo.audioFilePath,
      'audio_url': memo.audioUrl,
      'duration': memo.duration,
      'created_at': memo.createdAt.toIso8601String(),
      'updated_at': memo.updatedAt.toIso8601String(),
      'is_synced': memo.isSynced ? 1 : 0,
      'tags': memo.tags.isNotEmpty ? jsonEncode(memo.tags) : null,
    };
  }
}
