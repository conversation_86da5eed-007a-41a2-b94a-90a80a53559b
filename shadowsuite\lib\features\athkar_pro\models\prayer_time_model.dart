class PrayerTimeModel {
  final String id;
  final String name;
  final DateTime time;
  final bool isCompleted;
  final DateTime date;
  final String? location;
  final double? latitude;
  final double? longitude;

  const PrayerTimeModel({
    required this.id,
    required this.name,
    required this.time,
    this.isCompleted = false,
    required this.date,
    this.location,
    this.latitude,
    this.longitude,
  });

  PrayerTimeModel copyWith({
    String? id,
    String? name,
    DateTime? time,
    bool? isCompleted,
    DateTime? date,
    String? location,
    double? latitude,
    double? longitude,
  }) {
    return PrayerTimeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      time: time ?? this.time,
      isCompleted: isCompleted ?? this.isCompleted,
      date: date ?? this.date,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'time': time.toIso8601String(),
      'isCompleted': isCompleted,
      'date': date.toIso8601String(),
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory PrayerTimeModel.fromJson(Map<String, dynamic> json) {
    return PrayerTimeModel(
      id: json['id'],
      name: json['name'],
      time: DateTime.parse(json['time']),
      isCompleted: json['isCompleted'] ?? false,
      date: DateTime.parse(json['date']),
      location: json['location'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'PrayerTimeModel(id: $id, name: $name, time: $time, isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrayerTimeModel &&
        other.id == id &&
        other.name == name &&
        other.time == time &&
        other.isCompleted == isCompleted &&
        other.date == date;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        time.hashCode ^
        isCompleted.hashCode ^
        date.hashCode;
  }
}

enum PrayerName {
  fajr,
  dhuhr,
  asr,
  maghrib,
  isha,
}

extension PrayerNameExtension on PrayerName {
  String get displayName {
    switch (this) {
      case PrayerName.fajr:
        return 'Fajr';
      case PrayerName.dhuhr:
        return 'Dhuhr';
      case PrayerName.asr:
        return 'Asr';
      case PrayerName.maghrib:
        return 'Maghrib';
      case PrayerName.isha:
        return 'Isha';
    }
  }

  String get arabicName {
    switch (this) {
      case PrayerName.fajr:
        return 'الفجر';
      case PrayerName.dhuhr:
        return 'الظهر';
      case PrayerName.asr:
        return 'العصر';
      case PrayerName.maghrib:
        return 'المغرب';
      case PrayerName.isha:
        return 'العشاء';
    }
  }
}
