class PrayerTimeModel {
  final String id;
  final String name;
  final DateTime time;
  final bool isCompleted;
  final DateTime date;
  final String? location;
  final double? latitude;
  final double? longitude;

  const PrayerTimeModel({
    required this.id,
    required this.name,
    required this.time,
    this.isCompleted = false,
    required this.date,
    this.location,
    this.latitude,
    this.longitude,
  });

  PrayerTimeModel copyWith({
    String? id,
    String? name,
    DateTime? time,
    bool? isCompleted,
    DateTime? date,
    String? location,
    double? latitude,
    double? longitude,
  }) {
    return PrayerTimeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      time: time ?? this.time,
      isCompleted: isCompleted ?? this.isCompleted,
      date: date ?? this.date,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'time': time.toIso8601String(),
      'isCompleted': isCompleted,
      'date': date.toIso8601String(),
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory PrayerTimeModel.fromJson(Map<String, dynamic> json) {
    return PrayerTimeModel(
      id: json['id'],
      name: json['name'],
      time: DateTime.parse(json['time']),
      isCompleted: json['isCompleted'] ?? false,
      date: DateTime.parse(json['date']),
      location: json['location'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'PrayerTimeModel(id: $id, name: $name, time: $time, isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrayerTimeModel &&
        other.id == id &&
        other.name == name &&
        other.time == time &&
        other.isCompleted == isCompleted &&
        other.date == date;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        time.hashCode ^
        isCompleted.hashCode ^
        date.hashCode;
  }
}

enum PrayerName { fajr, dhuhr, asr, maghrib, isha }

extension PrayerNameExtension on PrayerName {
  String get displayName {
    switch (this) {
      case PrayerName.fajr:
        return 'Fajr';
      case PrayerName.dhuhr:
        return 'Dhuhr';
      case PrayerName.asr:
        return 'Asr';
      case PrayerName.maghrib:
        return 'Maghrib';
      case PrayerName.isha:
        return 'Isha';
    }
  }

  String get arabicName {
    switch (this) {
      case PrayerName.fajr:
        return 'الفجر';
      case PrayerName.dhuhr:
        return 'الظهر';
      case PrayerName.asr:
        return 'العصر';
      case PrayerName.maghrib:
        return 'المغرب';
      case PrayerName.isha:
        return 'العشاء';
    }
  }
}

enum CalculationMethod {
  muslimWorldLeague,
  isna,
  egyptianGeneralAuthority,
  ummAlQura,
  universityOfIslamicSciences,
  custom,
}

extension CalculationMethodExtension on CalculationMethod {
  String get displayName {
    switch (this) {
      case CalculationMethod.muslimWorldLeague:
        return 'Muslim World League';
      case CalculationMethod.isna:
        return 'Islamic Society of North America (ISNA)';
      case CalculationMethod.egyptianGeneralAuthority:
        return 'Egyptian General Authority of Survey';
      case CalculationMethod.ummAlQura:
        return 'Umm Al-Qura University, Makkah';
      case CalculationMethod.universityOfIslamicSciences:
        return 'University of Islamic Sciences, Karachi';
      case CalculationMethod.custom:
        return 'Custom';
    }
  }

  Map<String, double> get parameters {
    switch (this) {
      case CalculationMethod.muslimWorldLeague:
        return {'fajrAngle': 18.0, 'ishaAngle': 17.0};
      case CalculationMethod.isna:
        return {'fajrAngle': 15.0, 'ishaAngle': 15.0};
      case CalculationMethod.egyptianGeneralAuthority:
        return {'fajrAngle': 19.5, 'ishaAngle': 17.5};
      case CalculationMethod.ummAlQura:
        return {'fajrAngle': 18.5, 'ishaInterval': 90.0};
      case CalculationMethod.universityOfIslamicSciences:
        return {'fajrAngle': 18.0, 'ishaAngle': 18.0};
      case CalculationMethod.custom:
        return {'fajrAngle': 18.0, 'ishaAngle': 17.0};
    }
  }
}

class LocationModel {
  final String id;
  final String name;
  final String country;
  final String city;
  final double latitude;
  final double longitude;
  final String timezone;
  final bool isCurrentLocation;

  const LocationModel({
    required this.id,
    required this.name,
    required this.country,
    required this.city,
    required this.latitude,
    required this.longitude,
    required this.timezone,
    this.isCurrentLocation = false,
  });

  LocationModel copyWith({
    String? id,
    String? name,
    String? country,
    String? city,
    double? latitude,
    double? longitude,
    String? timezone,
    bool? isCurrentLocation,
  }) {
    return LocationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      country: country ?? this.country,
      city: city ?? this.city,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      timezone: timezone ?? this.timezone,
      isCurrentLocation: isCurrentLocation ?? this.isCurrentLocation,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'country': country,
      'city': city,
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
      'is_current_location': isCurrentLocation,
    };
  }

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      id: json['id'],
      name: json['name'],
      country: json['country'],
      city: json['city'],
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      timezone: json['timezone'],
      isCurrentLocation: json['is_current_location'] ?? false,
    );
  }

  @override
  String toString() {
    return 'LocationModel(name: $name, city: $city, country: $country)';
  }
}
