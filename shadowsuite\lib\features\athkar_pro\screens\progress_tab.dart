import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/prayer_provider.dart';
import '../providers/dhikr_provider.dart';

class ProgressTab extends ConsumerWidget {
  const ProgressTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prayerStats = ref.watch(prayerStatsProvider);
    final dhikrStats = ref.watch(dhikrStatsProvider);
    final prayerStreak = ref.watch(prayerStreakProvider);

    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.trending_up, size: 32, color: Colors.blue),
                        const SizedBox(width: 12),
                        Text(
                          'Progress & Statistics',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Track your spiritual journey and achievements',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Overall progress
            _buildOverallProgressCard(context, prayerStats, dhikrStats, prayerStreak),
            const SizedBox(height: 20),

            // Prayer progress
            _buildPrayerProgressCard(context, prayerStats, prayerStreak),
            const SizedBox(height: 20),

            // Dhikr progress
            _buildDhikrProgressCard(context, dhikrStats),
            const SizedBox(height: 20),

            // Achievements
            _buildAchievementsSection(context, prayerStats, dhikrStats, prayerStreak),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallProgressCard(
    BuildContext context,
    PrayerStatistics prayerStats,
    DhikrStatistics dhikrStats,
    int prayerStreak,
  ) {
    final overallProgress = (prayerStats.completionPercentage + dhikrStats.completionPercentage) / 2;

    return Card(
      color: Colors.blue.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.dashboard, color: Colors.blue[700]),
                const SizedBox(width: 8),
                Text(
                  'Today\'s Overall Progress',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.blue[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      CircularProgressIndicator(
                        value: overallProgress,
                        strokeWidth: 8,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[700]!),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${(overallProgress * 100).round()}%',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                      Text(
                        'Complete',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.blue[600],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      _buildProgressItem(
                        context,
                        'Prayers',
                        '${prayerStats.completedToday}/${prayerStats.totalToday}',
                        prayerStats.completionPercentage,
                        Colors.green,
                      ),
                      const SizedBox(height: 12),
                      _buildProgressItem(
                        context,
                        'Dhikr',
                        '${dhikrStats.completedToday}/${dhikrStats.totalDhikr}',
                        dhikrStats.completionPercentage,
                        Colors.orange,
                      ),
                      const SizedBox(height: 12),
                      _buildProgressItem(
                        context,
                        'Streak',
                        '$prayerStreak days',
                        prayerStreak > 0 ? 1.0 : 0.0,
                        Colors.red,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressItem(
    BuildContext context,
    String label,
    String value,
    double progress,
    Color color,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ],
          ),
        ),
        const SizedBox(width: 12),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildPrayerProgressCard(BuildContext context, PrayerStatistics stats, int streak) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.access_time, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Prayer Progress',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Completed Today',
                    '${stats.completedToday}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Remaining',
                    '${stats.remainingToday}',
                    Icons.schedule,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Streak',
                    '$streak days',
                    Icons.local_fire_department,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: stats.completionPercentage,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            const SizedBox(height: 8),
            Text(
              '${(stats.completionPercentage * 100).round()}% of daily prayers completed',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDhikrProgressCard(BuildContext context, DhikrStatistics stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.beenhere, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Dhikr Progress',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Completed',
                    '${stats.completedToday}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Total Count',
                    '${stats.totalCount}',
                    Icons.repeat,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Target',
                    '${stats.targetCount}',
                    Icons.flag,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: stats.progressPercentage,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
            ),
            const SizedBox(height: 8),
            Text(
              '${(stats.progressPercentage * 100).round()}% of target count reached',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementsSection(
    BuildContext context,
    PrayerStatistics prayerStats,
    DhikrStatistics dhikrStats,
    int prayerStreak,
  ) {
    final achievements = _getAchievements(prayerStats, dhikrStats, prayerStreak);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Achievements',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...achievements.map((achievement) => Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: achievement['color'].withValues(alpha: 0.1),
              child: Icon(
                achievement['icon'],
                color: achievement['color'],
              ),
            ),
            title: Text(achievement['title']),
            subtitle: Text(achievement['description']),
            trailing: achievement['unlocked']
                ? const Icon(Icons.check_circle, color: Colors.green)
                : const Icon(Icons.lock, color: Colors.grey),
          ),
        )),
      ],
    );
  }

  List<Map<String, dynamic>> _getAchievements(
    PrayerStatistics prayerStats,
    DhikrStatistics dhikrStats,
    int prayerStreak,
  ) {
    return [
      {
        'title': 'First Prayer',
        'description': 'Complete your first prayer',
        'icon': Icons.access_time,
        'color': Colors.green,
        'unlocked': prayerStats.completedToday > 0,
      },
      {
        'title': 'Daily Prayers',
        'description': 'Complete all 5 daily prayers',
        'icon': Icons.check_circle_outline,
        'color': Colors.blue,
        'unlocked': prayerStats.isAllCompleted,
      },
      {
        'title': 'First Dhikr',
        'description': 'Complete your first dhikr',
        'icon': Icons.beenhere,
        'color': Colors.orange,
        'unlocked': dhikrStats.completedToday > 0,
      },
      {
        'title': 'Dhikr Master',
        'description': 'Complete all daily dhikr',
        'icon': Icons.star,
        'color': Colors.purple,
        'unlocked': dhikrStats.isAllCompleted,
      },
      {
        'title': 'Consistent Worshipper',
        'description': 'Maintain a 7-day prayer streak',
        'icon': Icons.local_fire_department,
        'color': Colors.red,
        'unlocked': prayerStreak >= 7,
      },
      {
        'title': 'Dedicated Believer',
        'description': 'Maintain a 30-day prayer streak',
        'icon': Icons.emoji_events,
        'color': Colors.amber,
        'unlocked': prayerStreak >= 30,
      },
    ];
  }
}
