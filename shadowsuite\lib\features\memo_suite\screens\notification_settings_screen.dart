import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/notification_provider.dart';

class NotificationSettingsScreen extends ConsumerWidget {
  const NotificationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settings = ref.watch(notificationSettingsProvider);
    final settingsNotifier = ref.watch(notificationSettingsProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // General Settings Section
          _buildSectionHeader('General Notifications'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Enable Reminders'),
                  subtitle: const Text('Get reminded about your memos'),
                  value: settings.remindersEnabled,
                  onChanged: (value) {
                    settingsNotifier.updateSettings(remindersEnabled: value);
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('Due Date Notifications'),
                  subtitle: const Text('Get notified when tasks are due'),
                  value: settings.dueDateNotificationsEnabled,
                  onChanged: (value) {
                    settingsNotifier.updateSettings(dueDateNotificationsEnabled: value);
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('Overdue Notifications'),
                  subtitle: const Text('Get notified about overdue tasks'),
                  value: settings.overdueNotificationsEnabled,
                  onChanged: (value) {
                    settingsNotifier.updateSettings(overdueNotificationsEnabled: value);
                  },
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('Sync Status Notifications'),
                  subtitle: const Text('Get notified about sync status'),
                  value: settings.syncNotificationsEnabled,
                  onChanged: (value) {
                    settingsNotifier.updateSettings(syncNotificationsEnabled: value);
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Timing Settings Section
          _buildSectionHeader('Timing Settings'),
          Card(
            child: Column(
              children: [
                ListTile(
                  title: const Text('Reminder Time'),
                  subtitle: Text('Remind me ${settings.reminderMinutesBefore} minutes before due time'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () => _showReminderTimeDialog(context, settings, settingsNotifier),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Test Notifications Section
          _buildSectionHeader('Test Notifications'),
          Card(
            child: Column(
              children: [
                ListTile(
                  title: const Text('Test Reminder'),
                  subtitle: const Text('Send a test reminder notification'),
                  trailing: const Icon(Icons.send),
                  onTap: () => _sendTestNotification(context, ref, 'reminder'),
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Test Due Date'),
                  subtitle: const Text('Send a test due date notification'),
                  trailing: const Icon(Icons.send),
                  onTap: () => _sendTestNotification(context, ref, 'dueDate'),
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Test Sync Status'),
                  subtitle: const Text('Send a test sync notification'),
                  trailing: const Icon(Icons.send),
                  onTap: () => _sendTestNotification(context, ref, 'sync'),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Information Section
          _buildSectionHeader('Information'),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[600]),
                      const SizedBox(width: 8),
                      const Text(
                        'About Notifications',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '• Reminder notifications help you stay on top of your tasks\n'
                    '• Due date notifications alert you when tasks are approaching their deadline\n'
                    '• Overdue notifications remind you about tasks that have passed their due date\n'
                    '• Sync notifications keep you informed about data synchronization status',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, top: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.deepPurple,
        ),
      ),
    );
  }

  void _showReminderTimeDialog(
    BuildContext context,
    NotificationSettings settings,
    NotificationSettingsNotifier settingsNotifier,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reminder Time'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('How many minutes before the due time should we remind you?'),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: settings.reminderMinutesBefore,
              decoration: const InputDecoration(
                labelText: 'Minutes before',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 5, child: Text('5 minutes')),
                DropdownMenuItem(value: 10, child: Text('10 minutes')),
                DropdownMenuItem(value: 15, child: Text('15 minutes')),
                DropdownMenuItem(value: 30, child: Text('30 minutes')),
                DropdownMenuItem(value: 60, child: Text('1 hour')),
                DropdownMenuItem(value: 120, child: Text('2 hours')),
                DropdownMenuItem(value: 1440, child: Text('1 day')),
              ],
              onChanged: (value) {
                if (value != null) {
                  settingsNotifier.updateSettings(reminderMinutesBefore: value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _sendTestNotification(BuildContext context, WidgetRef ref, String type) {
    final actions = ref.read(notificationActionsProvider);
    
    switch (type) {
      case 'reminder':
        actions.addGeneralNotification(
          'Test Reminder',
          'This is a test reminder notification. Your notifications are working correctly!',
        );
        break;
      case 'dueDate':
        actions.addGeneralNotification(
          'Test Due Date',
          'This is a test due date notification. Task "Sample Task" is due soon!',
        );
        break;
      case 'sync':
        actions.notifySyncStatus('Test sync notification - All data synchronized successfully!');
        break;
    }
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Test $type notification sent!'),
        action: SnackBarAction(
          label: 'View',
          onPressed: () {
            Navigator.of(context).pop(); // Go back to previous screen
          },
        ),
      ),
    );
  }
}
