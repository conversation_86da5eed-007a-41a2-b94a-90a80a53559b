import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../../../core/models/memo_model.dart';
import '../providers/memo_provider.dart';
import '../services/audio_service.dart';

class CreateVoiceMemoScreen extends ConsumerStatefulWidget {
  final MemoModel? editingMemo;

  const CreateVoiceMemoScreen({super.key, this.editingMemo});

  @override
  ConsumerState<CreateVoiceMemoScreen> createState() => _CreateVoiceMemoScreenState();
}

class _CreateVoiceMemoScreenState extends ConsumerState<CreateVoiceMemoScreen> {
  final _titleController = TextEditingController();
  final _transcriptionController = TextEditingController();
  final _categoryController = TextEditingController();
  final _tagsController = TextEditingController();
  
  final AudioService _audioService = AudioService();
  
  bool _isRecording = false;
  bool _isPaused = false;
  bool _hasRecording = false;
  Duration _recordingDuration = Duration.zero;
  Timer? _timer;
  String? _audioFilePath;
  String? _transcription;
  List<String> _tags = [];
  bool _isPinned = false;
  bool _autoTranscribe = true;

  @override
  void initState() {
    super.initState();
    if (widget.editingMemo != null) {
      _initializeFromMemo(widget.editingMemo!);
    }
  }

  void _initializeFromMemo(MemoModel memo) {
    _titleController.text = memo.title;
    _transcriptionController.text = memo.transcription ?? '';
    _categoryController.text = memo.category ?? '';
    _tagsController.text = memo.tags.join(', ');
    _tags = List.from(memo.tags);
    _isPinned = memo.isPinned;
    _audioFilePath = memo.audioFilePath;
    _transcription = memo.transcription;
    _recordingDuration = Duration(seconds: memo.duration);
    _hasRecording = memo.audioFilePath != null;
  }

  @override
  void dispose() {
    _timer?.cancel();
    _titleController.dispose();
    _transcriptionController.dispose();
    _categoryController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  void _startRecording() async {
    try {
      await _audioService.startRecording();
      setState(() {
        _isRecording = true;
        _isPaused = false;
        _recordingDuration = Duration.zero;
      });
      
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingDuration = Duration(seconds: _recordingDuration.inSeconds + 1);
        });
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to start recording: $e')),
      );
    }
  }

  void _pauseRecording() async {
    try {
      await _audioService.pauseRecording();
      setState(() {
        _isPaused = true;
      });
      _timer?.cancel();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to pause recording: $e')),
      );
    }
  }

  void _resumeRecording() async {
    try {
      await _audioService.resumeRecording();
      setState(() {
        _isPaused = false;
      });
      
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingDuration = Duration(seconds: _recordingDuration.inSeconds + 1);
        });
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to resume recording: $e')),
      );
    }
  }

  void _stopRecording() async {
    try {
      final filePath = await _audioService.stopRecording();
      _timer?.cancel();
      
      setState(() {
        _isRecording = false;
        _isPaused = false;
        _hasRecording = true;
        _audioFilePath = filePath;
      });

      if (_autoTranscribe) {
        _transcribeAudio();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to stop recording: $e')),
      );
    }
  }

  void _transcribeAudio() async {
    if (_audioFilePath == null) return;
    
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Transcribing audio...'),
            ],
          ),
        ),
      );

      final transcription = await _audioService.transcribeAudio(_audioFilePath!);
      
      Navigator.of(context).pop(); // Close loading dialog
      
      setState(() {
        _transcription = transcription;
        _transcriptionController.text = transcription;
      });
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Transcription failed: $e')),
      );
    }
  }

  void _playRecording() async {
    if (_audioFilePath == null) return;
    
    try {
      await _audioService.playAudio(_audioFilePath!);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to play recording: $e')),
      );
    }
  }

  void _deleteRecording() {
    setState(() {
      _hasRecording = false;
      _audioFilePath = null;
      _transcription = null;
      _transcriptionController.clear();
      _recordingDuration = Duration.zero;
    });
  }

  void _updateTags() {
    final tagsText = _tagsController.text;
    _tags = tagsText
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
  }

  void _saveMemo() {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a title')),
      );
      return;
    }

    if (!_hasRecording) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please record audio first')),
      );
      return;
    }

    _updateTags();

    final memo = MemoModel(
      id: widget.editingMemo?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      userId: 'demo-user-id',
      title: _titleController.text.trim(),
      transcription: _transcriptionController.text.trim().isEmpty 
          ? null 
          : _transcriptionController.text.trim(),
      audioFilePath: _audioFilePath,
      duration: _recordingDuration.inSeconds,
      type: MemoType.voice,
      todoItems: const [],
      attachments: const [],
      createdAt: widget.editingMemo?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
      tags: _tags,
      category: _categoryController.text.trim().isEmpty 
          ? null 
          : _categoryController.text.trim(),
      isPinned: _isPinned,
      isArchived: widget.editingMemo?.isArchived ?? false,
    );

    if (widget.editingMemo != null) {
      ref.read(memosProvider.notifier).updateMemo(memo);
    } else {
      ref.read(memosProvider.notifier).addMemo(memo);
    }

    Navigator.of(context).pop();
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.editingMemo != null ? 'Edit Voice Memo' : 'Create Voice Memo'),
        actions: [
          IconButton(
            icon: Icon(_isPinned ? Icons.push_pin : Icons.push_pin_outlined),
            onPressed: () {
              setState(() {
                _isPinned = !_isPinned;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveMemo,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Voice Memo Title *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.mic),
              ),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 24),

            // Recording section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Recording status
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _isRecording 
                            ? Colors.red.withValues(alpha: 0.1)
                            : _hasRecording 
                                ? Colors.green.withValues(alpha: 0.1)
                                : Colors.grey.withValues(alpha: 0.1),
                        border: Border.all(
                          color: _isRecording 
                              ? Colors.red
                              : _hasRecording 
                                  ? Colors.green
                                  : Colors.grey,
                          width: 3,
                        ),
                      ),
                      child: Icon(
                        _isRecording 
                            ? Icons.mic
                            : _hasRecording 
                                ? Icons.check
                                : Icons.mic_none,
                        size: 48,
                        color: _isRecording 
                            ? Colors.red
                            : _hasRecording 
                                ? Colors.green
                                : Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Duration display
                    Text(
                      _formatDuration(_recordingDuration),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'monospace',
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Status text
                    Text(
                      _isRecording 
                          ? (_isPaused ? 'Recording Paused' : 'Recording...')
                          : _hasRecording 
                              ? 'Recording Complete'
                              : 'Ready to Record',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Recording controls
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        if (!_isRecording && !_hasRecording)
                          ElevatedButton.icon(
                            onPressed: _startRecording,
                            icon: const Icon(Icons.fiber_manual_record),
                            label: const Text('Start'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                          ),

                        if (_isRecording) ...[
                          ElevatedButton.icon(
                            onPressed: _isPaused ? _resumeRecording : _pauseRecording,
                            icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause),
                            label: Text(_isPaused ? 'Resume' : 'Pause'),
                          ),
                          ElevatedButton.icon(
                            onPressed: _stopRecording,
                            icon: const Icon(Icons.stop),
                            label: const Text('Stop'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],

                        if (_hasRecording && !_isRecording) ...[
                          ElevatedButton.icon(
                            onPressed: _playRecording,
                            icon: const Icon(Icons.play_arrow),
                            label: const Text('Play'),
                          ),
                          ElevatedButton.icon(
                            onPressed: _deleteRecording,
                            icon: const Icon(Icons.delete),
                            label: const Text('Delete'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Transcription section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Transcription',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        Row(
                          children: [
                            Switch(
                              value: _autoTranscribe,
                              onChanged: (value) {
                                setState(() {
                                  _autoTranscribe = value;
                                });
                              },
                            ),
                            const Text('Auto'),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      height: 150,
                      child: TextField(
                        controller: _transcriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Transcription (Optional)',
                          border: OutlineInputBorder(),
                          alignLabelWithHint: true,
                          helperText: 'Edit or add transcription manually',
                        ),
                        maxLines: null,
                        expands: true,
                        textAlignVertical: TextAlignVertical.top,
                      ),
                    ),
                    if (_hasRecording && !_autoTranscribe) ...[
                      const SizedBox(height: 12),
                      ElevatedButton.icon(
                        onPressed: _transcribeAudio,
                        icon: const Icon(Icons.transcribe),
                        label: const Text('Transcribe Audio'),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Category and tags
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _categoryController,
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _tagsController,
                    decoration: const InputDecoration(
                      labelText: 'Tags',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.tag),
                    ),
                    onChanged: (value) => _updateTags(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Save button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _saveMemo,
                icon: const Icon(Icons.save),
                label: Text(widget.editingMemo != null ? 'Update Voice Memo' : 'Save Voice Memo'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
