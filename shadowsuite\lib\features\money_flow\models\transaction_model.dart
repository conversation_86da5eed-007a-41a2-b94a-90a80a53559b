enum TransactionType {
  income,
  expense,
  transfer,
}

extension TransactionTypeExtension on TransactionType {
  String get displayName {
    switch (this) {
      case TransactionType.income:
        return 'Income';
      case TransactionType.expense:
        return 'Expense';
      case TransactionType.transfer:
        return 'Transfer';
    }
  }

  String get icon {
    switch (this) {
      case TransactionType.income:
        return '💰';
      case TransactionType.expense:
        return '💸';
      case TransactionType.transfer:
        return '🔄';
    }
  }
}

class TransactionModel {
  final String id;
  final String userId;
  final String accountId;
  final String? toAccountId; // For transfers
  final TransactionType type;
  final double amount;
  final String currency;
  final String categoryId;
  final String title;
  final String? description;
  final DateTime date;
  final List<String> tags;
  final String? receiptPath;
  final String? location;
  final bool isRecurring;
  final String? recurringPattern;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const TransactionModel({
    required this.id,
    required this.userId,
    required this.accountId,
    this.toAccountId,
    required this.type,
    required this.amount,
    this.currency = 'USD',
    required this.categoryId,
    required this.title,
    this.description,
    required this.date,
    this.tags = const [],
    this.receiptPath,
    this.location,
    this.isRecurring = false,
    this.recurringPattern,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  TransactionModel copyWith({
    String? id,
    String? userId,
    String? accountId,
    String? toAccountId,
    TransactionType? type,
    double? amount,
    String? currency,
    String? categoryId,
    String? title,
    String? description,
    DateTime? date,
    List<String>? tags,
    String? receiptPath,
    String? location,
    bool? isRecurring,
    String? recurringPattern,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      accountId: accountId ?? this.accountId,
      toAccountId: toAccountId ?? this.toAccountId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      categoryId: categoryId ?? this.categoryId,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      tags: tags ?? this.tags,
      receiptPath: receiptPath ?? this.receiptPath,
      location: location ?? this.location,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringPattern: recurringPattern ?? this.recurringPattern,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'account_id': accountId,
      'to_account_id': toAccountId,
      'type': type.name,
      'amount': amount,
      'currency': currency,
      'category_id': categoryId,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'tags': tags,
      'receipt_path': receiptPath,
      'location': location,
      'is_recurring': isRecurring,
      'recurring_pattern': recurringPattern,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_synced': isSynced,
    };
  }

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      id: json['id'],
      userId: json['user_id'],
      accountId: json['account_id'],
      toAccountId: json['to_account_id'],
      type: TransactionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TransactionType.expense,
      ),
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] ?? 'USD',
      categoryId: json['category_id'],
      title: json['title'],
      description: json['description'],
      date: DateTime.parse(json['date']),
      tags: List<String>.from(json['tags'] ?? []),
      receiptPath: json['receipt_path'],
      location: json['location'],
      isRecurring: json['is_recurring'] ?? false,
      recurringPattern: json['recurring_pattern'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      isSynced: json['is_synced'] ?? false,
    );
  }

  @override
  String toString() {
    return 'TransactionModel(id: $id, title: $title, type: $type, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionModel &&
        other.id == id &&
        other.title == title &&
        other.amount == amount &&
        other.date == date;
  }

  @override
  int get hashCode {
    return id.hashCode ^ title.hashCode ^ amount.hashCode ^ date.hashCode;
  }
}
