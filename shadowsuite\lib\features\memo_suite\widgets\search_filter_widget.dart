import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/search_provider.dart';

class SearchFilterWidget extends ConsumerStatefulWidget {
  const SearchFilterWidget({super.key});

  @override
  ConsumerState<SearchFilterWidget> createState() => _SearchFilterWidgetState();
}

class _SearchFilterWidgetState extends ConsumerState<SearchFilterWidget> {
  final _searchController = TextEditingController();
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      ref.read(searchQueryProvider.notifier).state = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final searchQuery = ref.watch(searchQueryProvider);
    final filterType = ref.watch(searchFilterProvider);
    final sortOption = ref.watch(sortOptionProvider);
    final categoryFilter = ref.watch(categoryFilterProvider);
    final tagFilter = ref.watch(tagFilterProvider);
    final showArchived = ref.watch(showArchivedProvider);
    final availableCategories = ref.watch(availableCategoriesProvider);
    final availableTags = ref.watch(availableTagsProvider);

    // Update search controller if needed
    if (_searchController.text != searchQuery) {
      _searchController.text = searchQuery;
    }

    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search memos...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              ref.read(searchQueryProvider.notifier).state = '';
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: Icon(
                  _showFilters ? Icons.filter_list : Icons.filter_list_outlined,
                  color: _showFilters ? Theme.of(context).primaryColor : null,
                ),
                onPressed: () {
                  setState(() {
                    _showFilters = !_showFilters;
                  });
                },
              ),
              PopupMenuButton<SortOption>(
                icon: const Icon(Icons.sort),
                tooltip: 'Sort options',
                onSelected: (option) {
                  ref.read(sortOptionProvider.notifier).state = option;
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: SortOption.dateNewest,
                    child: Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          color: sortOption == SortOption.dateNewest
                              ? Theme.of(context).primaryColor
                              : null,
                        ),
                        const SizedBox(width: 8),
                        const Text('Newest First'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: SortOption.dateOldest,
                    child: Row(
                      children: [
                        Icon(
                          Icons.history,
                          color: sortOption == SortOption.dateOldest
                              ? Theme.of(context).primaryColor
                              : null,
                        ),
                        const SizedBox(width: 8),
                        const Text('Oldest First'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: SortOption.titleAZ,
                    child: Row(
                      children: [
                        Icon(
                          Icons.sort_by_alpha,
                          color: sortOption == SortOption.titleAZ
                              ? Theme.of(context).primaryColor
                              : null,
                        ),
                        const SizedBox(width: 8),
                        const Text('Title A-Z'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: SortOption.titleZA,
                    child: Row(
                      children: [
                        Icon(
                          Icons.sort_by_alpha,
                          color: sortOption == SortOption.titleZA
                              ? Theme.of(context).primaryColor
                              : null,
                        ),
                        const SizedBox(width: 8),
                        const Text('Title Z-A'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: SortOption.priority,
                    child: Row(
                      children: [
                        Icon(
                          Icons.push_pin,
                          color: sortOption == SortOption.priority
                              ? Theme.of(context).primaryColor
                              : null,
                        ),
                        const SizedBox(width: 8),
                        const Text('Pinned First'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Filter options
        if (_showFilters)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Type filter
                const Text(
                  'Memo Type',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: SearchFilterType.values.map((type) {
                    return FilterChip(
                      label: Text(_getFilterTypeLabel(type)),
                      selected: filterType == type,
                      onSelected: (selected) {
                        ref.read(searchFilterProvider.notifier).state = type;
                      },
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),

                // Category filter
                if (availableCategories.isNotEmpty) ...[
                  const Text(
                    'Category',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String?>(
                    value: categoryFilter,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    hint: const Text('All Categories'),
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Categories'),
                      ),
                      ...availableCategories.map((category) {
                        return DropdownMenuItem<String?>(
                          value: category,
                          child: Text(category),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      ref.read(categoryFilterProvider.notifier).state = value;
                    },
                  ),
                  const SizedBox(height: 16),
                ],

                // Tag filter
                if (availableTags.isNotEmpty) ...[
                  const Text(
                    'Tag',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String?>(
                    value: tagFilter,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    hint: const Text('All Tags'),
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Tags'),
                      ),
                      ...availableTags.map((tag) {
                        return DropdownMenuItem<String?>(
                          value: tag,
                          child: Text(tag),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      ref.read(tagFilterProvider.notifier).state = value;
                    },
                  ),
                  const SizedBox(height: 16),
                ],

                // Show archived toggle
                SwitchListTile(
                  title: const Text('Show Archived'),
                  subtitle: const Text('Include archived memos in results'),
                  value: showArchived,
                  onChanged: (value) {
                    ref.read(showArchivedProvider.notifier).state = value;
                  },
                  dense: true,
                ),

                // Clear filters button
                const SizedBox(height: 8),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () {
                      ref.read(searchQueryProvider.notifier).state = '';
                      ref.read(searchFilterProvider.notifier).state = SearchFilterType.all;
                      ref.read(categoryFilterProvider.notifier).state = null;
                      ref.read(tagFilterProvider.notifier).state = null;
                      ref.read(showArchivedProvider.notifier).state = false;
                      _searchController.clear();
                    },
                    icon: const Icon(Icons.clear_all),
                    label: const Text('Clear All Filters'),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  String _getFilterTypeLabel(SearchFilterType type) {
    switch (type) {
      case SearchFilterType.all:
        return 'All';
      case SearchFilterType.text:
        return 'Text';
      case SearchFilterType.voice:
        return 'Voice';
      case SearchFilterType.todo:
        return 'Todo';
      case SearchFilterType.mixed:
        return 'Mixed';
    }
  }
}
