import 'dart:async';
import 'dart:io';
// Audio packages temporarily disabled for APK build
// import 'package:audioplayers/audioplayers.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
// import 'package:record/record.dart';
// import 'package:speech_to_text/speech_to_text.dart' as stt;

// Stub classes for disabled audio functionality
enum PlayerState { stopped, playing, paused, completed }

class AudioRecorder {
  Future<void> start(dynamic config, {String? path}) async {}
  Future<String?> stop() async => null;
  Future<bool> isRecording() async => false;
  Future<void> pause() async {}
  Future<void> resume() async {}
  Future<bool> isPaused() async => false;
  Future<void> dispose() async {}
}

class AudioPlayer {
  Stream<PlayerState> get onPlayerStateChanged =>
      Stream.value(PlayerState.stopped);
  Stream<Duration> get onPositionChanged => Stream.value(Duration.zero);
  Stream<Duration> get onDurationChanged => Stream.value(Duration.zero);
  Future<void> play(dynamic source) async {}
  Future<void> pause() async {}
  Future<void> resume() async {}
  Future<void> stop() async {}
  Future<void> setSource(dynamic source) async {}
  Future<Duration?> getDuration() async => null;
  Future<void> dispose() async {}
}

class RecordConfig {
  RecordConfig({dynamic encoder, int? sampleRate, int? bitRate});
}

class AudioEncoder {
  static const aacLc = 'aacLc';
}

class UrlSource {
  final String url;
  UrlSource(this.url);
}

class DeviceFileSource {
  final String path;
  DeviceFileSource(this.path);
}

class LocaleInfo {
  final String localeId;
  LocaleInfo(this.localeId);
}

class SpeechToText {
  Future<bool> initialize({
    Function(dynamic)? onError,
    Function(String)? onStatus,
  }) async => false;
  Future<void> listen({
    Function(dynamic)? onResult,
    Duration? listenFor,
    Duration? pauseFor,
    bool? partialResults,
    String? localeId,
    Function(double)? onSoundLevelChange,
  }) async {}
  Future<void> stop() async {}
  bool get isListening => false;
  bool get isAvailable => false;
  Future<List<LocaleInfo>> locales() async => [];
}

class AudioService {
  static AudioService? _instance;
  final AudioRecorder _recorder = AudioRecorder();
  final AudioPlayer _player = AudioPlayer();
  final SpeechToText _speechToText = SpeechToText();

  Timer? _recordingTimer;
  int _recordingDuration = 0;
  bool _isInitialized = false;

  AudioService._internal();

  factory AudioService() {
    _instance ??= AudioService._internal();
    return _instance!;
  }

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _speechToText.initialize(
        onError: (error) => print('Speech recognition error: $error'),
        onStatus: (status) => print('Speech recognition status: $status'),
      );
      _isInitialized = true;
    } catch (e) {
      print('Error initializing audio service: $e');
    }
  }

  Future<bool> requestPermissions() async {
    final microphoneStatus = await Permission.microphone.request();
    return microphoneStatus == PermissionStatus.granted;
  }

  Future<bool> get hasPermissions async {
    final microphoneStatus = await Permission.microphone.status;
    return microphoneStatus == PermissionStatus.granted;
  }

  Future<String?> startRecording() async {
    try {
      if (!await hasPermissions) {
        final granted = await requestPermissions();
        if (!granted) return null;
      }

      // Use application documents directory for permanent storage
      final appDir = await getApplicationDocumentsDirectory();
      final audioDir = Directory(path.join(appDir.path, 'voice_memos'));
      if (!await audioDir.exists()) {
        await audioDir.create(recursive: true);
      }

      final fileName = 'recording_${DateTime.now().millisecondsSinceEpoch}.m4a';
      final filePath = path.join(audioDir.path, fileName);

      final config = RecordConfig(
        encoder: AudioEncoder.aacLc,
        bitRate: 128000,
        sampleRate: 44100,
      );

      await _recorder.start(config, path: filePath);

      _recordingDuration = 0;
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        _recordingDuration++;
      });

      return filePath;
    } catch (e) {
      print('Error starting recording: $e');
      return null;
    }
  }

  Future<void> pauseRecording() async {
    try {
      await _recorder.pause();
      _recordingTimer?.cancel();
    } catch (e) {
      print('Error pausing recording: $e');
    }
  }

  Future<void> resumeRecording() async {
    try {
      await _recorder.resume();
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        _recordingDuration++;
      });
    } catch (e) {
      print('Error resuming recording: $e');
    }
  }

  Future<String?> stopRecording() async {
    try {
      _recordingTimer?.cancel();
      final path = await _recorder.stop();
      return path;
    } catch (e) {
      print('Error stopping recording: $e');
      return null;
    }
  }

  Future<bool> get isRecording async {
    return await _recorder.isRecording();
  }

  Future<bool> get isPaused async {
    return await _recorder.isPaused();
  }

  int get recordingDuration => _recordingDuration;

  String get formattedDuration {
    final minutes = _recordingDuration ~/ 60;
    final seconds = _recordingDuration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // Audio playback methods
  Future<bool> isAudioFileAccessible(String filePath) async {
    try {
      if (filePath.startsWith('http')) {
        return true; // Assume URL is accessible
      } else {
        final file = File(filePath);
        return await file.exists();
      }
    } catch (e) {
      print('Error checking audio file accessibility: $e');
      return false;
    }
  }

  Future<void> playAudio(String filePath) async {
    try {
      await _player.stop();

      // Check if file is accessible before trying to play
      if (!await isAudioFileAccessible(filePath)) {
        throw Exception('Audio file not found or not accessible: $filePath');
      }

      if (filePath.startsWith('http')) {
        await _player.play(UrlSource(filePath));
      } else {
        await _player.play(DeviceFileSource(filePath));
      }
    } catch (e) {
      print('Error playing audio: $e');
      rethrow; // Re-throw so the UI can handle the error
    }
  }

  Future<void> pauseAudio() async {
    try {
      await _player.pause();
    } catch (e) {
      print('Error pausing audio: $e');
    }
  }

  Future<void> resumeAudio() async {
    try {
      await _player.resume();
    } catch (e) {
      print('Error resuming audio: $e');
    }
  }

  Future<void> stopAudio() async {
    try {
      await _player.stop();
    } catch (e) {
      print('Error stopping audio: $e');
    }
  }

  Future<void> stopPlayback() async {
    await stopAudio();
  }

  Stream<Duration> get positionStream => _player.onPositionChanged;
  Stream<Duration> get durationStream => _player.onDurationChanged;
  Stream<PlayerState> get playerStateStream => _player.onPlayerStateChanged;

  // Speech-to-text methods
  Future<String?> transcribeAudio(String filePath) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (!_speechToText.isAvailable) {
        print('Speech recognition not available');
        return null;
      }

      final completer = Completer<String?>();
      String transcription = '';

      await _speechToText.listen(
        onResult: (result) {
          transcription = result.recognizedWords;
          if (result.finalResult) {
            completer.complete(transcription.isNotEmpty ? transcription : null);
          }
        },
        listenFor: const Duration(minutes: 5),
        pauseFor: const Duration(seconds: 3),
        partialResults: true,
        localeId: 'en_US',
        onSoundLevelChange: (level) {
          // Handle sound level changes if needed
        },
      );

      // Start playing the audio file for transcription
      await playAudio(filePath);

      return await completer.future;
    } catch (e) {
      print('Error transcribing audio: $e');
      return null;
    }
  }

  Future<bool> get isSpeechAvailable async {
    if (!_isInitialized) {
      await initialize();
    }
    return _speechToText.isAvailable;
  }

  Future<List<String>> get availableLocales async {
    if (!_isInitialized) {
      await initialize();
    }

    final locales = await _speechToText.locales();
    return locales.map((locale) => locale.localeId).toList();
  }

  Future<int> getAudioDuration(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return 0;

      // Create a temporary player to get duration
      final tempPlayer = AudioPlayer();
      Duration? duration;

      final completer = Completer<int>();

      tempPlayer.onDurationChanged.listen((d) {
        duration = d;
        completer.complete(d.inSeconds);
      });

      if (filePath.startsWith('http')) {
        await tempPlayer.setSource(UrlSource(filePath));
      } else {
        await tempPlayer.setSource(DeviceFileSource(filePath));
      }

      final result = await completer.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () => 0,
      );

      await tempPlayer.dispose();
      return result;
    } catch (e) {
      print('Error getting audio duration: $e');
      return 0;
    }
  }

  void dispose() {
    _recordingTimer?.cancel();
    _recorder.dispose();
    _player.dispose();
  }
}
