# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.1.0"
  }
  digests {
    sha256: "\326\371\033{\0170l\312)\237\354t\373|4\344\207Mo^\305\271%\240\264\336!\220\036\021\234?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.20"
  }
  digests {
    sha256: "\343\230\266ywb\'\030\277\030\377\231\2679\307\331\332\006\0173\373E\212.% 2!\301j\360\020"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.20"
  }
  digests {
    sha256: "\257\036\304\f;\225\032\375\314\f*\001s\307\270\027c\305(\034-[\257\277\n\205D\242L]\314\f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "armeabi_v7a_release"
    version: "1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7"
  }
  digests {
    sha256: "h\'\206\262*\220h\366`\001\240\320\0337\277\024\315\317\236Y\260\235v\b\213>\037(\221\0330\207"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "arm64_v8a_release"
    version: "1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7"
  }
  digests {
    sha256: "\356\n\006\223\370\325\033g\001c\025\356\233\301\345\213\006q\217R\026=G\325-\334\035x\240\2138\023"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "x86_64_release"
    version: "1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7"
  }
  digests {
    sha256: "\375K\233\241ze\214\v\263\336\250\207\271\344\355\216\357\314\241n\330#\\\304=\216<|0\221\223\317"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.microg"
    artifactId: "safe-parcel"
    version: "1.7.0"
  }
  digests {
    sha256: "b\237^\277\333Px\326y\024!\2434\232\331\267\321\251\377m\b\314\023\266\241{\377\276\217\227V\330"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.flutter"
    artifactId: "flutter_embedding_release"
    version: "1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7"
  }
  digests {
    sha256: "\265\307%\006`\360&z\2501G\220$!\2733\307\022!\355_{\251ci\341x\003E^N\005"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.1"
  }
  digests {
    sha256: "\031\272P\320\224\3076\216\336\033L\317\021\225\316\270>5\227\a6Y?\202>Z\367\026\370\320]p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.1"
  }
  digests {
    sha256: ",\'\336\031\2255gP\005U0fYzK \372\036\352|\"\212\264\357k2\265\3769\312\037Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.7.1"
  }
  digests {
    sha256: "}\336\276\235\203\n\004\22673\334\263@\312\264\272\301\346\235\374\247\227\246\325\232\247\274V(!P\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.1"
  }
  digests {
    sha256: "f\274,\023\275\221\373\216\240\236D[!\367\204\323\345\323\251\354\233\030\f\320~\016\222\b\302\3719\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.1"
  }
  digests {
    sha256: "\237w\312\242\273\354|\356\372\\\223\036/\"<+8jYN\340D-\026\215\376\257\034\205\n\353\235"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.7.1"
  }
  digests {
    sha256: "\217\254\271\a\030\317R^\257\376m\033\326\024]\377\025mj\345\035<\037\202\002\315\005\216\370\201\366*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window-java"
    version: "1.2.0"
  }
  digests {
    sha256: "\343\336\373^\343\205wWP]z\326bu\334:\v\001Ai0\033\211\326\344\356\300;\016E\274\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.2.0"
  }
  digests {
    sha256: "\2504\302\027\331s\021\'wS=\257\022\201\332\263;\327x\372\335-f\276\2749\265\376\201\250\220\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window.extensions.core"
    artifactId: "core"
    version: "1.0.0"
  }
  digests {
    sha256: "1\000\205K-\300\336\\M\210\264~\034]\263\b\001w\f\202\035\312(\030\224C\267bO@\b\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.getkeepsafe.relinker"
    artifactId: "relinker"
    version: "1.4.5"
  }
  digests {
    sha256: "\260;M\021:\237\357y@\n\350T\301wW\302\221W%\346\377\321\326\021\026\352\332\353V\0237J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-android"
    version: "1.1.3"
  }
  digests {
    sha256: "z\235o`!\215w_\3549\250X@C5\235\373jcZa\326\206\027\252n\230&\330\232\001;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\260\257x\371\b\203@z\346?\321HW\304\346.w\037>\236?\202\340\227q[M\2518\215\340\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: ")\025\006\264\374\343\230$\227\223\343\' v5\275M\362Q\201\331S\230\b\327\030\032J\275\bu\365"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.4.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.4.0"
  }
  digests {
    sha256: "\0019\354zPm\273\325L\255b)\033\001\234\270PSK\340\227\310\306l\020\000\325\373\350\355\357>"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\220\333\\\004\261Ho\255*\017\342\220 \3769D\300\372\f]6\361A\036\336\241\231\000t*E\023"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core"
    version: "1.1.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: "\3139\341\";C$\3270\305s\242\274\352:50\236\275\223X\373\3460\211\227\005\243\373\312\312\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-proto"
    version: "1.1.3"
  }
  digests {
    sha256: "chja/9\270\312`;\353\3437mU\373l\236\342\212JQs\357\244\234\211\313\314\250\241\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-external-protobuf"
    version: "1.1.3"
  }
  digests {
    sha256: "\374\263\363s\317t4&\310^\273\r<\306D\256\372\362o9\267\372U\273\220#\215A\266\211\354\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.1.0"
  }
  digests {
    sha256: "\215r\231\274\244L\263\275\361\177U\225vj\313\364Y\374\201\376\342#\350hl\306\254\323\244*\265\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.1.0"
  }
  digests {
    sha256: "\031\224M2\264eQ\241|4~!\211K\225\203\177\275{\252\257\311\342\b\'\2244O\"/sa"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.0.0"
  }
  digests {
    sha256: "\006\225o\261\254\001@\'\312\235+@F\232KB\252a\264\225{\261\030H\341\3775\'\001\253EH"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\345\b\306\225H\224\2237M\224+\367\264\356\002\253\367W\035%\252\304\306\"\345}l\325\315)\353s"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.4.1"
  }
  digests {
    sha256: "6\322\215\236\303:\214d18B\274\351\234\225sm\245\262zk:Q69\005\r\350/\aW&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
  }
}
library {
  digests {
    sha256: "\204\252\2412(I\302\020\000\300\321\b\247Cr\353\362n7\260\316&G\021\252\237\242\321.L\200\210"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 41
  library_dep_index: 7
  library_dep_index: 25
  library_dep_index: 34
  library_dep_index: 49
  library_dep_index: 52
}
library_dependencies {
  library_index: 11
  library_dep_index: 7
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 29
}
library_dependencies {
  library_index: 12
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 2
}
library_dependencies {
  library_index: 13
  library_dep_index: 14
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
  library_dep_index: 15
}
library_dependencies {
  library_index: 15
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 13
}
library_dependencies {
  library_index: 16
  library_dep_index: 7
  library_dep_index: 11
  library_dep_index: 11
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 29
}
library_dependencies {
  library_index: 17
  library_dep_index: 7
  library_dep_index: 18
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 29
}
library_dependencies {
  library_index: 18
  library_dep_index: 7
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 11
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 27
  library_dep_index: 29
}
library_dependencies {
  library_index: 19
  library_dep_index: 7
}
library_dependencies {
  library_index: 20
  library_dep_index: 7
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 7
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 23
}
library_dependencies {
  library_index: 22
  library_dep_index: 7
  library_dep_index: 23
}
library_dependencies {
  library_index: 24
  library_dep_index: 7
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 7
}
library_dependencies {
  library_index: 26
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 11
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 29
}
library_dependencies {
  library_index: 27
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 0
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 28
  library_dep_index: 26
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 29
  library_dep_index: 7
  library_dep_index: 18
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 30
  library_dep_index: 7
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 29
}
library_dependencies {
  library_index: 31
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 29
}
library_dependencies {
  library_index: 32
  library_dep_index: 7
  library_dep_index: 33
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 29
}
library_dependencies {
  library_index: 33
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 7
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 22
  library_dep_index: 37
  library_dep_index: 18
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 33
}
library_dependencies {
  library_index: 35
  library_dep_index: 0
}
library_dependencies {
  library_index: 36
  library_dep_index: 7
}
library_dependencies {
  library_index: 37
  library_dep_index: 7
}
library_dependencies {
  library_index: 38
  library_dep_index: 7
  library_dep_index: 36
}
library_dependencies {
  library_index: 39
  library_dep_index: 7
  library_dep_index: 19
  library_dep_index: 11
  library_dep_index: 0
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 39
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
  library_dep_index: 7
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 33
  library_dep_index: 26
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 44
  library_dep_index: 21
  library_dep_index: 39
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 47
}
library_dependencies {
  library_index: 42
  library_dep_index: 7
  library_dep_index: 36
  library_dep_index: 34
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 21
  library_dep_index: 39
  library_dep_index: 25
  library_dep_index: 0
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 33
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 0
  library_dep_index: 42
}
library_dependencies {
  library_index: 44
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 27
  library_dep_index: 30
}
library_dependencies {
  library_index: 45
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 36
}
library_dependencies {
  library_index: 47
  library_dep_index: 43
  library_dep_index: 48
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 40
  library_dep_index: 0
  library_dep_index: 41
}
library_dependencies {
  library_index: 48
  library_dep_index: 0
  library_dep_index: 36
}
library_dependencies {
  library_index: 49
  library_dep_index: 34
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 13
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 7
  library_dep_index: 36
  library_dep_index: 34
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 49
}
library_dependencies {
  library_index: 51
  library_dep_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 53
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 7
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 61
  library_dep_index: 0
  library_dep_index: 13
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 7
  library_dep_index: 57
  library_dep_index: 0
  library_dep_index: 13
  library_dep_index: 53
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 57
  library_dep_index: 0
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 0
}
library_dependencies {
  library_index: 59
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 55
  library_dep_index: 61
  library_dep_index: 0
  library_dep_index: 13
  library_dep_index: 53
  library_dep_index: 55
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 61
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 2
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 53
  library_dep_index: 65
  library_dep_index: 0
  library_dep_index: 13
  library_dep_index: 53
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 65
  library_dep_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 67
  library_dep_index: 61
  library_dep_index: 0
  library_dep_index: 53
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 67
  library_dep_index: 68
  library_dep_index: 53
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 68
}
library_dependencies {
  library_index: 68
  library_dep_index: 53
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 67
}
library_dependencies {
  library_index: 69
  library_dep_index: 7
  library_dep_index: 70
  library_dep_index: 34
  library_dep_index: 43
  library_dep_index: 47
  library_dep_index: 76
  library_dep_index: 83
  library_dep_index: 36
}
library_dependencies {
  library_index: 70
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 71
  library_dep_index: 41
  library_dep_index: 72
  library_dep_index: 75
  library_dep_index: 36
}
library_dependencies {
  library_index: 71
  library_dep_index: 7
}
library_dependencies {
  library_index: 72
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 36
}
library_dependencies {
  library_index: 73
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 36
}
library_dependencies {
  library_index: 74
  library_dep_index: 73
  library_dep_index: 37
  library_dep_index: 36
}
library_dependencies {
  library_index: 75
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 46
}
library_dependencies {
  library_index: 76
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 78
  library_dep_index: 46
  library_dep_index: 45
  library_dep_index: 82
  library_dep_index: 75
  library_dep_index: 83
  library_dep_index: 37
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 71
}
library_dependencies {
  library_index: 78
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 79
  library_dep_index: 44
  library_dep_index: 80
  library_dep_index: 81
}
library_dependencies {
  library_index: 79
  library_dep_index: 7
}
library_dependencies {
  library_index: 80
  library_dep_index: 7
}
library_dependencies {
  library_index: 81
  library_dep_index: 7
}
library_dependencies {
  library_index: 82
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 46
}
library_dependencies {
  library_index: 83
  library_dep_index: 7
  library_dep_index: 46
  library_dep_index: 34
  library_dep_index: 50
  library_dep_index: 84
}
library_dependencies {
  library_index: 84
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 36
}
library_dependencies {
  library_index: 85
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 37
}
library_dependencies {
  library_index: 86
  library_dep_index: 7
  library_dep_index: 34
}
library_dependencies {
  library_index: 87
  library_dep_index: 7
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 22
  library_dep_index: 34
  library_dep_index: 37
  library_dep_index: 23
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 4
  dependency_index: 5
  dependency_index: 6
  dependency_index: 7
  dependency_index: 9
  dependency_index: 10
  dependency_index: 53
  dependency_index: 63
  dependency_index: 69
  dependency_index: 34
  dependency_index: 87
  dependency_index: 88
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://storage.googleapis.com/download.flutter.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
