["D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/flutter_sound/assets/js/async_processor.js", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/flutter_sound/assets/js/tau_web.js", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/flutter_sound_web/howler/howler.js", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound.js", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound_player.js", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound_recorder.js", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound_stream_processor.js", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/record_web/assets/js/record.worklet.js", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages/record_web/assets/js/record.fixwebmduration.js", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.json", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.bin", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\FontManifest.json", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NOTICES.Z", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NativeAssetsManifest.json", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\armeabi-v7a\\app.so", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\x86_64\\app.so", "D:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\flutter\\release\\arm64-v8a\\app.so"]