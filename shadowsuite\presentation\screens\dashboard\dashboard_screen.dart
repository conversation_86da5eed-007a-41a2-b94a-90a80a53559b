import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/providers/auth_provider.dart';
import '../../../core/providers/localization_provider.dart';
import '../main/main_screen.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final translate = ref.read(translationProvider);
    final currentUser = ref.watch(currentUserProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(translate('dashboard')),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // TODO: Trigger sync
            },
          ),
        ],
      ),
      body: currentUser.when(
        data: (user) => SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Welcome back${user?.displayName != null ? ', ${user!.displayName}' : ''}!',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Here\'s what\'s happening with your productivity suite today.',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Quick Access Cards
              Text(
                'Quick Access',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: AppConstants.defaultMargin,
                mainAxisSpacing: AppConstants.defaultMargin,
                childAspectRatio: 1.2,
                children: [
                  _buildQuickAccessCard(
                    context: context,
                    ref: ref,
                    title: translate('memo_suite'),
                    subtitle: 'Record & transcribe',
                    icon: Icons.mic,
                    color: Colors.blue,
                    onTap: () => _navigateToScreen(ref, AppConstants.memoSuiteIndex),
                  ),
                  _buildQuickAccessCard(
                    context: context,
                    ref: ref,
                    title: translate('athkar_pro'),
                    subtitle: 'Prayer routines',
                    icon: Icons.auto_stories,
                    color: Colors.green,
                    onTap: () => _navigateToScreen(ref, AppConstants.athkarProIndex),
                  ),
                  _buildQuickAccessCard(
                    context: context,
                    ref: ref,
                    title: translate('money_flow'),
                    subtitle: 'Finance manager',
                    icon: Icons.account_balance_wallet,
                    color: Colors.orange,
                    onTap: () => _navigateToScreen(ref, AppConstants.moneyFlowIndex),
                  ),
                  _buildQuickAccessCard(
                    context: context,
                    ref: ref,
                    title: translate('settings'),
                    subtitle: 'App preferences',
                    icon: Icons.settings,
                    color: Colors.purple,
                    onTap: () => _navigateToScreen(ref, AppConstants.settingsIndex),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Recent Activity Section
              Text(
                'Recent Activity',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    children: [
                      _buildActivityItem(
                        context: context,
                        icon: Icons.mic,
                        title: 'New memo recorded',
                        subtitle: '2 minutes ago',
                        color: Colors.blue,
                      ),
                      const Divider(),
                      _buildActivityItem(
                        context: context,
                        icon: Icons.auto_stories,
                        title: 'Morning athkar completed',
                        subtitle: '1 hour ago',
                        color: Colors.green,
                      ),
                      const Divider(),
                      _buildActivityItem(
                        context: context,
                        icon: Icons.account_balance_wallet,
                        title: 'Expense added: \$25.00',
                        subtitle: '3 hours ago',
                        color: Colors.orange,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Statistics Section
              Text(
                'Today\'s Statistics',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      context: context,
                      title: 'Memos',
                      value: '3',
                      icon: Icons.mic,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultMargin),
                  Expanded(
                    child: _buildStatCard(
                      context: context,
                      title: 'Athkar',
                      value: '2/5',
                      icon: Icons.auto_stories,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultMargin),
                  Expanded(
                    child: _buildStatCard(
                      context: context,
                      title: 'Expenses',
                      value: '\$125',
                      icon: Icons.account_balance_wallet,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildQuickAccessCard({
    required BuildContext context,
    required WidgetRef ref,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withOpacity(0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildStatCard({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToScreen(WidgetRef ref, int index) {
    ref.read(navigationIndexProvider.notifier).state = index;
  }
}
