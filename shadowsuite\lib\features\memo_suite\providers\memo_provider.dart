import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/memo_model.dart';
import '../services/memo_service.dart';

// Memo service provider
final memoServiceProvider = Provider<MemoService>((ref) {
  return MemoService();
});

// All memos provider
final memosProvider = StateNotifierProvider<MemosNotifier, AsyncValue<List<MemoModel>>>((ref) {
  final memoService = ref.watch(memoServiceProvider);
  return MemosNotifier(memoService);
});

// Search query provider
final memoSearchQueryProvider = StateProvider<String>((ref) => '');

// Filtered memos provider
final filteredMemosProvider = Provider<AsyncValue<List<MemoModel>>>((ref) {
  final memos = ref.watch(memosProvider);
  final searchQuery = ref.watch(memoSearchQueryProvider);

  return memos.when(
    data: (memoList) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(memoList);
      }
      
      final filtered = memoList.where((memo) {
        return memo.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
               (memo.description?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
               (memo.transcription?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
               memo.tags.any((tag) => tag.toLowerCase().contains(searchQuery.toLowerCase()));
      }).toList();
      
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Recording state provider
final recordingStateProvider = StateNotifierProvider<RecordingStateNotifier, RecordingState>((ref) {
  return RecordingStateNotifier();
});

class MemosNotifier extends StateNotifier<AsyncValue<List<MemoModel>>> {
  final MemoService _memoService;

  MemosNotifier(this._memoService) : super(const AsyncValue.loading()) {
    loadMemos();
  }

  Future<void> loadMemos() async {
    try {
      state = const AsyncValue.loading();
      final memos = await _memoService.getAllMemos();
      state = AsyncValue.data(memos);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> addMemo(MemoModel memo) async {
    try {
      await _memoService.createMemo(memo);
      await loadMemos(); // Reload to get updated list
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateMemo(MemoModel memo) async {
    try {
      await _memoService.updateMemo(memo);
      await loadMemos(); // Reload to get updated list
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> deleteMemo(String memoId) async {
    try {
      await _memoService.deleteMemo(memoId);
      await loadMemos(); // Reload to get updated list
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> syncMemos() async {
    try {
      await _memoService.syncMemos();
      await loadMemos(); // Reload to get updated list
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

class RecordingStateNotifier extends StateNotifier<RecordingState> {
  RecordingStateNotifier() : super(RecordingState.idle);

  void setRecording() => state = RecordingState.recording;
  void setPaused() => state = RecordingState.paused;
  void setIdle() => state = RecordingState.idle;
  void setProcessing() => state = RecordingState.processing;
}

enum RecordingState {
  idle,
  recording,
  paused,
  processing,
}

// Audio playback provider
final audioPlaybackProvider = StateNotifierProvider<AudioPlaybackNotifier, AudioPlaybackState>((ref) {
  return AudioPlaybackNotifier();
});

class AudioPlaybackNotifier extends StateNotifier<AudioPlaybackState> {
  AudioPlaybackNotifier() : super(AudioPlaybackState.stopped);

  void setPlaying(String memoId) => state = AudioPlaybackState.playing(memoId);
  void setPaused(String memoId) => state = AudioPlaybackState.paused(memoId);
  void setStopped() => state = AudioPlaybackState.stopped;
  void setLoading(String memoId) => state = AudioPlaybackState.loading(memoId);
}

sealed class AudioPlaybackState {
  const AudioPlaybackState();
  
  static const stopped = AudioPlaybackStateStopped();
  static AudioPlaybackStatePlaying playing(String memoId) => AudioPlaybackStatePlaying(memoId);
  static AudioPlaybackStatePaused paused(String memoId) => AudioPlaybackStatePaused(memoId);
  static AudioPlaybackStateLoading loading(String memoId) => AudioPlaybackStateLoading(memoId);
}

class AudioPlaybackStateStopped extends AudioPlaybackState {
  const AudioPlaybackStateStopped();
}

class AudioPlaybackStatePlaying extends AudioPlaybackState {
  final String memoId;
  const AudioPlaybackStatePlaying(this.memoId);
}

class AudioPlaybackStatePaused extends AudioPlaybackState {
  final String memoId;
  const AudioPlaybackStatePaused(this.memoId);
}

class AudioPlaybackStateLoading extends AudioPlaybackState {
  final String memoId;
  const AudioPlaybackStateLoading(this.memoId);
}
