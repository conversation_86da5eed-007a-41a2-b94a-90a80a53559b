import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/memo_model.dart';
import '../services/memo_service.dart';
import '../services/memo_sync_service.dart';

// Memo service provider
final memoServiceProvider = Provider<MemoService>((ref) {
  return MemoService();
});

// All memos provider
final memosProvider =
    StateNotifierProvider<MemosNotifier, AsyncValue<List<MemoModel>>>((ref) {
      final memoService = ref.watch(memoServiceProvider);
      return MemosNotifier(memoService);
    });

// Search query provider
final memoSearchQueryProvider = StateProvider<String>((ref) => '');

// Sync status provider
final syncStatusProvider = StreamProvider<SyncStatus>((ref) {
  final memoService = ref.watch(memoServiceProvider);
  return memoService.syncStatusStream;
});

// Filtered memos provider
final filteredMemosProvider = Provider<AsyncValue<List<MemoModel>>>((ref) {
  final memos = ref.watch(memosProvider);
  final searchQuery = ref.watch(memoSearchQueryProvider);

  return memos.when(
    data: (memoList) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(memoList);
      }

      final filtered = memoList.where((memo) {
        return memo.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
            (memo.description?.toLowerCase().contains(
                  searchQuery.toLowerCase(),
                ) ??
                false) ||
            (memo.transcription?.toLowerCase().contains(
                  searchQuery.toLowerCase(),
                ) ??
                false) ||
            memo.tags.any(
              (tag) => tag.toLowerCase().contains(searchQuery.toLowerCase()),
            );
      }).toList();

      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Recording state provider
final recordingStateProvider =
    StateNotifierProvider<RecordingStateNotifier, RecordingState>((ref) {
      return RecordingStateNotifier();
    });

class MemosNotifier extends StateNotifier<AsyncValue<List<MemoModel>>> {
  final MemoService _memoService;
  final List<MemoModel> _localMemos = [];

  MemosNotifier(this._memoService) : super(const AsyncValue.loading()) {
    _initialize();
  }

  Future<void> _initialize() async {
    // Initialize sync service
    await _memoService.initializeSync();

    // Load memos
    await loadMemos();
  }

  Future<void> loadMemos() async {
    try {
      state = const AsyncValue.loading();

      final memos = await _memoService.getAllMemos();
      _localMemos.clear();
      _localMemos.addAll(memos);

      // If no memos exist, create sample data
      if (_localMemos.isEmpty) {
        await _createSampleData();
      }

      state = AsyncValue.data(List.from(_localMemos));
    } catch (e, stackTrace) {
      print('Error loading memos: $e');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> _createSampleData() async {
    final sampleMemos = [
      MemoModel(
        id: '1',
        userId: 'demo-user-id',
        title: 'Welcome to ShadowSuite',
        description:
            'This is your first memo! Explore the features and start organizing your thoughts.',
        transcription:
            'Welcome to ShadowSuite, your comprehensive productivity app.',
        audioFilePath: null,
        audioUrl: null,
        duration: 30,
        type: MemoType.text,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
        isSynced: false,
        tags: ['welcome', 'demo'],
        category: 'Getting Started',
        isPinned: true,
        isArchived: false,
        todoItems: [],
        attachments: [],
      ),
      MemoModel(
        id: '2',
        userId: 'demo-user-id',
        title: 'Voice Memo Example',
        description: 'Sample voice memo to demonstrate audio functionality',
        transcription:
            'This is an example of what a voice memo transcription would look like.',
        audioFilePath: null,
        audioUrl: null,
        duration: 45,
        type: MemoType.voice,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
        isSynced: false,
        tags: ['voice', 'example'],
        category: 'Examples',
        isPinned: false,
        isArchived: false,
        todoItems: [],
        attachments: [],
      ),
    ];

    for (final memo in sampleMemos) {
      try {
        await _memoService.createMemo(memo);
        _localMemos.add(memo);
      } catch (e) {
        print('Error creating sample memo: $e');
      }
    }
  }

  Future<void> addMemo(MemoModel memo) async {
    try {
      // Add to local list immediately
      _localMemos.insert(0, memo);
      state = AsyncValue.data(List.from(_localMemos));

      // Try to save to database
      try {
        await _memoService.createMemo(memo);
      } catch (e) {
        print('Failed to save to database: $e');
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateMemo(MemoModel memo) async {
    try {
      // Update local list
      final index = _localMemos.indexWhere((m) => m.id == memo.id);
      if (index != -1) {
        _localMemos[index] = memo;
        state = AsyncValue.data(List.from(_localMemos));
      }

      // Try to update in database
      try {
        await _memoService.updateMemo(memo);
      } catch (e) {
        print('Failed to update in database: $e');
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> deleteMemo(String memoId) async {
    try {
      // Remove from local list
      _localMemos.removeWhere((m) => m.id == memoId);
      state = AsyncValue.data(List.from(_localMemos));

      // Try to delete from database
      try {
        await _memoService.deleteMemo(memoId);
      } catch (e) {
        print('Failed to delete from database: $e');
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> syncMemos() async {
    try {
      await _memoService.syncMemos();
      await loadMemos(); // Reload to get updated list
    } catch (e, stackTrace) {
      print('Sync failed: $e');
      // Don't set error state for sync failures
    }
  }
}

class RecordingStateNotifier extends StateNotifier<RecordingState> {
  RecordingStateNotifier() : super(RecordingState.idle);

  void setRecording() => state = RecordingState.recording;
  void setPaused() => state = RecordingState.paused;
  void setIdle() => state = RecordingState.idle;
  void setProcessing() => state = RecordingState.processing;
}

enum RecordingState { idle, recording, paused, processing }

// Audio playback provider
final audioPlaybackProvider =
    StateNotifierProvider<AudioPlaybackNotifier, AudioPlaybackState>((ref) {
      return AudioPlaybackNotifier();
    });

class AudioPlaybackNotifier extends StateNotifier<AudioPlaybackState> {
  AudioPlaybackNotifier() : super(AudioPlaybackState.stopped);

  void setPlaying(String memoId) => state = AudioPlaybackState.playing(memoId);
  void setPaused(String memoId) => state = AudioPlaybackState.paused(memoId);
  void setStopped() => state = AudioPlaybackState.stopped;
  void setLoading(String memoId) => state = AudioPlaybackState.loading(memoId);
}

sealed class AudioPlaybackState {
  const AudioPlaybackState();

  static const stopped = AudioPlaybackStateStopped();
  static AudioPlaybackStatePlaying playing(String memoId) =>
      AudioPlaybackStatePlaying(memoId);
  static AudioPlaybackStatePaused paused(String memoId) =>
      AudioPlaybackStatePaused(memoId);
  static AudioPlaybackStateLoading loading(String memoId) =>
      AudioPlaybackStateLoading(memoId);
}

class AudioPlaybackStateStopped extends AudioPlaybackState {
  const AudioPlaybackStateStopped();
}

class AudioPlaybackStatePlaying extends AudioPlaybackState {
  final String memoId;
  const AudioPlaybackStatePlaying(this.memoId);
}

class AudioPlaybackStatePaused extends AudioPlaybackState {
  final String memoId;
  const AudioPlaybackStatePaused(this.memoId);
}

class AudioPlaybackStateLoading extends AudioPlaybackState {
  final String memoId;
  const AudioPlaybackStateLoading(this.memoId);
}
