import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/dhikr_model.dart';

// Dhikr list provider
final dhikrListProvider = StateNotifierProvider<DhikrListNotifier, AsyncValue<List<DhikrModel>>>((ref) {
  return DhikrListNotifier();
});

// Active dhikr session provider
final activeDhikrSessionProvider = StateNotifierProvider<ActiveDhikrSessionNotifier, DhikrSession?>((ref) {
  return ActiveDhikrSessionNotifier();
});

// Dhikr categories provider
final dhikrCategoriesProvider = Provider<List<DhikrCategory>>((ref) {
  return DhikrCategory.values;
});

// Filtered dhikr provider
final filteredDhikrProvider = Provider.family<List<DhikrModel>, DhikrCategory?>((ref, category) {
  final dhikrListAsync = ref.watch(dhikrListProvider);
  
  return dhikrListAsync.when(
    data: (dhikrList) {
      if (category == null) return dhikrList;
      return dhikrList.where((dhikr) => dhikr.category == category.name).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

class DhikrListNotifier extends StateNotifier<AsyncValue<List<DhikrModel>>> {
  DhikrListNotifier() : super(const AsyncValue.loading()) {
    loadDhikrList();
  }

  Future<void> loadDhikrList() async {
    try {
      state = const AsyncValue.loading();
      
      // Load default dhikr collection
      final dhikrList = _getDefaultDhikrCollection();
      
      state = AsyncValue.data(dhikrList);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void addDhikr(DhikrModel dhikr) {
    state.whenData((dhikrList) {
      state = AsyncValue.data([...dhikrList, dhikr]);
    });
  }

  void updateDhikr(DhikrModel updatedDhikr) {
    state.whenData((dhikrList) {
      final updatedList = dhikrList.map((dhikr) {
        return dhikr.id == updatedDhikr.id ? updatedDhikr : dhikr;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  void incrementDhikrCount(String dhikrId) {
    state.whenData((dhikrList) {
      final updatedList = dhikrList.map((dhikr) {
        if (dhikr.id == dhikrId) {
          final newCount = dhikr.currentCount + 1;
          final isCompleted = newCount >= dhikr.targetCount;
          
          return dhikr.copyWith(
            currentCount: newCount,
            isCompleted: isCompleted,
            updatedAt: DateTime.now(),
            lastCompletedDate: isCompleted ? DateTime.now() : dhikr.lastCompletedDate,
            streakCount: isCompleted ? dhikr.streakCount + 1 : dhikr.streakCount,
          );
        }
        return dhikr;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  void resetDhikrCount(String dhikrId) {
    state.whenData((dhikrList) {
      final updatedList = dhikrList.map((dhikr) {
        if (dhikr.id == dhikrId) {
          return dhikr.copyWith(
            currentCount: 0,
            isCompleted: false,
            updatedAt: DateTime.now(),
          );
        }
        return dhikr;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  void deleteDhikr(String dhikrId) {
    state.whenData((dhikrList) {
      final updatedList = dhikrList.where((dhikr) => dhikr.id != dhikrId).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  List<DhikrModel> _getDefaultDhikrCollection() {
    final now = DateTime.now();
    
    return [
      // Morning Adhkar
      DhikrModel(
        id: 'morning_1',
        arabicText: 'سُبْحَانَ اللَّهِ وَبِحَمْدِهِ',
        transliteration: 'Subhan Allah wa bihamdihi',
        translation: 'Glory is to Allah and praise is to Him',
        targetCount: 100,
        category: DhikrCategory.morning.name,
        benefits: ['Forgiveness of sins', 'Spiritual purification'],
        source: 'Sahih Muslim',
        createdAt: now,
        updatedAt: now,
      ),
      DhikrModel(
        id: 'morning_2',
        arabicText: 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ',
        transliteration: 'La ilaha illa Allah wahdahu la sharika lah',
        translation: 'There is no god but Allah alone, with no partner',
        targetCount: 10,
        category: DhikrCategory.morning.name,
        benefits: ['Great reward', 'Protection from evil'],
        source: 'Sahih Bukhari',
        createdAt: now,
        updatedAt: now,
      ),
      
      // Evening Adhkar
      DhikrModel(
        id: 'evening_1',
        arabicText: 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
        transliteration: 'A\'udhu billahi min ash-shaytani\'r-rajim',
        translation: 'I seek refuge in Allah from Satan the accursed',
        targetCount: 3,
        category: DhikrCategory.evening.name,
        benefits: ['Protection from Satan', 'Spiritual strength'],
        source: 'Quran',
        createdAt: now,
        updatedAt: now,
      ),
      
      // After Prayer
      DhikrModel(
        id: 'after_prayer_1',
        arabicText: 'سُبْحَانَ اللَّهِ',
        transliteration: 'Subhan Allah',
        translation: 'Glory is to Allah',
        targetCount: 33,
        category: DhikrCategory.afterPrayer.name,
        benefits: ['Spiritual elevation', 'Closeness to Allah'],
        source: 'Sahih Muslim',
        createdAt: now,
        updatedAt: now,
      ),
      DhikrModel(
        id: 'after_prayer_2',
        arabicText: 'الْحَمْدُ لِلَّهِ',
        transliteration: 'Alhamdulillah',
        translation: 'Praise is to Allah',
        targetCount: 33,
        category: DhikrCategory.afterPrayer.name,
        benefits: ['Gratitude', 'Spiritual satisfaction'],
        source: 'Sahih Muslim',
        createdAt: now,
        updatedAt: now,
      ),
      DhikrModel(
        id: 'after_prayer_3',
        arabicText: 'اللَّهُ أَكْبَرُ',
        transliteration: 'Allahu Akbar',
        translation: 'Allah is the Greatest',
        targetCount: 34,
        category: DhikrCategory.afterPrayer.name,
        benefits: ['Recognition of Allah\'s greatness', 'Humility'],
        source: 'Sahih Muslim',
        createdAt: now,
        updatedAt: now,
      ),
      
      // General Dhikr
      DhikrModel(
        id: 'general_1',
        arabicText: 'لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ',
        transliteration: 'La hawla wa la quwwata illa billah',
        translation: 'There is no power except with Allah',
        targetCount: 10,
        category: DhikrCategory.general.name,
        benefits: ['Strength in difficulties', 'Reliance on Allah'],
        source: 'Sahih Bukhari',
        createdAt: now,
        updatedAt: now,
      ),
      
      // Before Sleep
      DhikrModel(
        id: 'sleep_1',
        arabicText: 'بِاسْمِكَ اللَّهُمَّ أَمُوتُ وَأَحْيَا',
        transliteration: 'Bismika Allahumma amutu wa ahya',
        translation: 'In Your name, O Allah, I die and I live',
        targetCount: 1,
        category: DhikrCategory.sleeping.name,
        benefits: ['Peaceful sleep', 'Protection during sleep'],
        source: 'Sahih Bukhari',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}

class ActiveDhikrSessionNotifier extends StateNotifier<DhikrSession?> {
  ActiveDhikrSessionNotifier() : super(null);

  void startSession(String dhikrId) {
    state = DhikrSession(
      id: 'session_${DateTime.now().millisecondsSinceEpoch}',
      dhikrId: dhikrId,
      count: 0,
      startTime: DateTime.now(),
    );
  }

  void incrementCount() {
    if (state != null) {
      state = state!.copyWith(count: state!.count + 1);
    }
  }

  void endSession() {
    if (state != null) {
      state = state!.copyWith(
        endTime: DateTime.now(),
        isCompleted: true,
      );
      
      // Clear the session after a brief moment
      Future.delayed(const Duration(seconds: 1), () {
        state = null;
      });
    }
  }

  void cancelSession() {
    state = null;
  }
}

// Dhikr statistics provider
final dhikrStatsProvider = Provider<DhikrStatistics>((ref) {
  final dhikrListAsync = ref.watch(dhikrListProvider);
  
  return dhikrListAsync.when(
    data: (dhikrList) {
      final completedToday = dhikrList.where((dhikr) => dhikr.isCompleted).length;
      final totalDhikr = dhikrList.length;
      final totalCount = dhikrList.fold<int>(0, (sum, dhikr) => sum + dhikr.currentCount);
      final targetCount = dhikrList.fold<int>(0, (sum, dhikr) => sum + dhikr.targetCount);
      
      return DhikrStatistics(
        completedToday: completedToday,
        totalDhikr: totalDhikr,
        totalCount: totalCount,
        targetCount: targetCount,
        completionPercentage: totalDhikr > 0 ? completedToday / totalDhikr : 0.0,
      );
    },
    loading: () => const DhikrStatistics(
      completedToday: 0,
      totalDhikr: 0,
      totalCount: 0,
      targetCount: 0,
      completionPercentage: 0.0,
    ),
    error: (_, __) => const DhikrStatistics(
      completedToday: 0,
      totalDhikr: 0,
      totalCount: 0,
      targetCount: 0,
      completionPercentage: 0.0,
    ),
  );
});

class DhikrStatistics {
  final int completedToday;
  final int totalDhikr;
  final int totalCount;
  final int targetCount;
  final double completionPercentage;

  const DhikrStatistics({
    required this.completedToday,
    required this.totalDhikr,
    required this.totalCount,
    required this.targetCount,
    required this.completionPercentage,
  });

  int get remainingToday => totalDhikr - completedToday;
  bool get isAllCompleted => completedToday == totalDhikr;
  double get progressPercentage => targetCount > 0 ? totalCount / targetCount : 0.0;
}
