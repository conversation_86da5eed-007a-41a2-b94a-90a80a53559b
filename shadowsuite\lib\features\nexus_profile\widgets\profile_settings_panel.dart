import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_nexus_model.dart';
import '../providers/nexus_profile_provider.dart';
import '../../../core/theme/futuristic_theme.dart';

class ProfileSettingsPanel extends ConsumerWidget {
  final UserNexusModel profile;

  const ProfileSettingsPanel({
    super.key,
    required this.profile,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final syncLogs = ref.watch(syncLogsProvider);
    final isRealTimeConnected = ref.watch(realTimeConnectionProvider);

    return Column(
      children: [
        // Sync Status Card
        _buildSyncStatusCard(context, ref, isRealTimeConnected),
        
        const SizedBox(height: 16),
        
        // Quick Settings Card
        _buildQuickSettingsCard(context, ref),
        
        const SizedBox(height: 16),
        
        // Sync Logs Card
        _buildSyncLogsCard(context, ref, syncLogs),
      ],
    );
  }

  Widget _buildSyncStatusCard(BuildContext context, WidgetRef ref, bool isConnected) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            (isConnected ? FuturisticTheme.primaryGreen : Colors.orange).withOpacity(0.1),
            (isConnected ? FuturisticTheme.primaryGreen : Colors.orange).withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: (isConnected ? FuturisticTheme.primaryGreen : Colors.orange).withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isConnected ? Icons.cloud_done : Icons.cloud_off,
                color: isConnected ? FuturisticTheme.primaryGreen : Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Sync Status',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isConnected ? FuturisticTheme.primaryGreen : Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            isConnected ? 'Real-time sync active' : 'Sync disconnected',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isConnected 
                ? 'Your data is automatically synced across all devices'
                : 'Check your internet connection',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          if (profile.lastSyncAt != null) ...[
            const SizedBox(height: 12),
            Text(
              'Last sync: ${_formatSyncTime(profile.lastSyncAt!)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickSettingsCard(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: FuturisticTheme.primaryBlue.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings,
                color: FuturisticTheme.primaryBlue,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Quick Settings',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: FuturisticTheme.primaryBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Theme setting
          _buildSettingItem(
            context,
            'Theme',
            profile.themePreference.toUpperCase(),
            Icons.palette,
            () => _showThemeOptions(context, ref),
          ),
          
          const SizedBox(height: 12),
          
          // Language setting
          _buildSettingItem(
            context,
            'Language',
            profile.languagePreference.toUpperCase(),
            Icons.language,
            () => _showLanguageOptions(context, ref),
          ),
          
          const SizedBox(height: 12),
          
          // Timezone setting
          _buildSettingItem(
            context,
            'Timezone',
            profile.timezone,
            Icons.access_time,
            () => _showTimezoneOptions(context, ref),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncLogsCard(BuildContext context, WidgetRef ref, List<Map<String, dynamic>> syncLogs) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: FuturisticTheme.primaryPurple.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.sync_alt,
                color: FuturisticTheme.primaryPurple,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Sync Logs',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: FuturisticTheme.primaryPurple,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () {
                  ref.read(nexusProfileProvider.notifier).refreshSyncLogs();
                },
                icon: Icon(
                  Icons.refresh,
                  color: FuturisticTheme.primaryPurple,
                  size: 20,
                ),
                tooltip: 'Refresh',
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (syncLogs.isEmpty)
            Text(
              'No sync logs available',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            )
          else
            ...syncLogs.take(3).map((log) => _buildSyncLogItem(context, log)),
          
          if (syncLogs.length > 3) ...[
            const SizedBox(height: 12),
            TextButton(
              onPressed: () => _showAllSyncLogs(context, syncLogs),
              child: Text('View all ${syncLogs.length} logs'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: FuturisticTheme.primaryBlue.withOpacity(0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: FuturisticTheme.primaryBlue.withOpacity(0.1),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: FuturisticTheme.primaryBlue,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncLogItem(BuildContext context, Map<String, dynamic> log) {
    final operation = log['operation'] as String? ?? 'unknown';
    final tableName = log['table_name'] as String? ?? 'unknown';
    final status = log['sync_status'] as String? ?? 'unknown';
    final createdAt = DateTime.tryParse(log['created_at'] as String? ?? '');

    final statusColor = _getSyncStatusColor(status);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: statusColor.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getSyncOperationIcon(operation),
            color: statusColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$operation $tableName',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (createdAt != null)
                  Text(
                    _formatSyncTime(createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontSize: 10,
                    ),
                  ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              status.toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getSyncStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return FuturisticTheme.primaryGreen;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      case 'conflict':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getSyncOperationIcon(String operation) {
    switch (operation.toLowerCase()) {
      case 'insert':
        return Icons.add;
      case 'update':
        return Icons.edit;
      case 'delete':
        return Icons.delete;
      default:
        return Icons.sync;
    }
  }

  String _formatSyncTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${time.day}/${time.month}';
    }
  }

  void _showThemeOptions(BuildContext context, WidgetRef ref) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Theme options coming soon')),
    );
  }

  void _showLanguageOptions(BuildContext context, WidgetRef ref) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Language options coming soon')),
    );
  }

  void _showTimezoneOptions(BuildContext context, WidgetRef ref) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Timezone options coming soon')),
    );
  }

  void _showAllSyncLogs(BuildContext context, List<Map<String, dynamic>> logs) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Full sync logs view coming soon')),
    );
  }
}
