import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import '../constants/app_constants.dart';

class DatabaseService {
  static DatabaseService? _instance;
  static Database? _database;

  DatabaseService._internal();

  factory DatabaseService() {
    _instance ??= DatabaseService._internal();
    return _instance!;
  }

  Future<Database> get database async {
    if (_database == null) {
      _database = await _initDatabase();
      // Ensure database is properly initialized
      await _verifyDatabaseIntegrity();
    }
    return _database!;
  }

  // Verify database integrity and recreate if corrupted
  Future<void> _verifyDatabaseIntegrity() async {
    try {
      final db = _database!;
      // Test basic operations
      await db.rawQuery(
        'SELECT COUNT(*) FROM sqlite_master WHERE type="table"',
      );
      debugPrint('✅ Database integrity verified');
    } catch (e) {
      debugPrint('❌ Database corrupted, recreating: $e');
      await _recreateDatabase();
    }
  }

  Future<void> _recreateDatabase() async {
    try {
      await _database?.close();
      _database = null;
      await deleteDatabase();
      _database = await _initDatabase();
      debugPrint('✅ Database recreated successfully');
    } catch (e) {
      debugPrint('❌ Failed to recreate database: $e');
      rethrow;
    }
  }

  Future<Database> _initDatabase() async {
    if (kIsWeb) {
      // For web, create an in-memory database
      return await openDatabase(
        inMemoryDatabasePath,
        version: AppConstants.databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onConfigure: _onConfigure,
      );
    } else {
      // For mobile platforms
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, AppConstants.databaseName);

      return await openDatabase(
        path,
        version: AppConstants.databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onConfigure: _onConfigure,
      );
    }
  }

  Future<void> _onConfigure(Database db) async {
    await db.execute('PRAGMA foreign_keys = ON');
  }

  Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
    await _createIndexes(db);
    await _insertDefaultData(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here
    if (oldVersion < newVersion) {
      // Add migration logic for future versions
    }
  }

  Future<void> _createTables(Database db) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        display_name TEXT,
        avatar_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        preferences TEXT
      )
    ''');

    // Memos table
    await db.execute('''
      CREATE TABLE memos (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        rich_content TEXT,
        transcription TEXT,
        audio_file_path TEXT,
        audio_url TEXT,
        duration INTEGER DEFAULT 0,
        type TEXT DEFAULT 'text',
        category TEXT,
        is_pinned INTEGER DEFAULT 0,
        is_archived INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        tags TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Todo items table
    await db.execute('''
      CREATE TABLE todo_items (
        id TEXT PRIMARY KEY,
        memo_id TEXT NOT NULL,
        text TEXT NOT NULL,
        is_completed INTEGER DEFAULT 0,
        due_date TEXT,
        priority TEXT DEFAULT 'medium',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (memo_id) REFERENCES memos (id) ON DELETE CASCADE
      )
    ''');

    // File attachments table
    await db.execute('''
      CREATE TABLE file_attachments (
        id TEXT PRIMARY KEY,
        memo_id TEXT NOT NULL,
        name TEXT NOT NULL,
        path TEXT NOT NULL,
        url TEXT,
        mime_type TEXT NOT NULL,
        size INTEGER NOT NULL,
        uploaded_at TEXT NOT NULL,
        FOREIGN KEY (memo_id) REFERENCES memos (id) ON DELETE CASCADE
      )
    ''');

    // Athkar routines table
    await db.execute('''
      CREATE TABLE athkar_routines (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        target_count INTEGER DEFAULT 1,
        current_count INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        is_synced INTEGER DEFAULT 0,
        reminder_time TEXT,
        reminder_days TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Athkar steps table
    await db.execute('''
      CREATE TABLE athkar_steps (
        id TEXT PRIMARY KEY,
        routine_id TEXT NOT NULL,
        arabic_text TEXT NOT NULL,
        transliteration TEXT,
        translation TEXT,
        repetitions INTEGER DEFAULT 1,
        current_repetitions INTEGER DEFAULT 0,
        step_order INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (routine_id) REFERENCES athkar_routines (id) ON DELETE CASCADE
      )
    ''');

    // Accounts table
    await db.execute('''
      CREATE TABLE accounts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL,
        balance REAL DEFAULT 0.0,
        currency TEXT DEFAULT 'USD',
        color TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');

    // Categories table
    await db.execute('''
      CREATE TABLE categories (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL,
        parent_id TEXT,
        color TEXT,
        icon TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (parent_id) REFERENCES categories (id) ON DELETE SET NULL
      )
    ''');

    // Transactions table
    await db.execute('''
      CREATE TABLE transactions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        account_id TEXT NOT NULL,
        category_id TEXT,
        to_account_id TEXT,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        currency TEXT DEFAULT 'USD',
        description TEXT,
        notes TEXT,
        date TEXT NOT NULL,
        receipt_path TEXT,
        receipt_url TEXT,
        tags TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        recurring_id TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL,
        FOREIGN KEY (to_account_id) REFERENCES accounts (id) ON DELETE SET NULL
      )
    ''');

    // Budgets table
    await db.execute('''
      CREATE TABLE budgets (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        category_id TEXT NOT NULL,
        name TEXT NOT NULL,
        amount REAL NOT NULL,
        spent REAL DEFAULT 0.0,
        period TEXT DEFAULT 'monthly',
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        is_synced INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
      )
    ''');
  }

  Future<void> _createIndexes(Database db) async {
    // Create indexes for better query performance
    await db.execute('CREATE INDEX idx_memos_user_id ON memos (user_id)');
    await db.execute('CREATE INDEX idx_memos_created_at ON memos (created_at)');
    await db.execute('CREATE INDEX idx_memos_type ON memos (type)');
    await db.execute('CREATE INDEX idx_memos_category ON memos (category)');
    await db.execute('CREATE INDEX idx_memos_is_pinned ON memos (is_pinned)');
    await db.execute(
      'CREATE INDEX idx_memos_is_archived ON memos (is_archived)',
    );
    await db.execute(
      'CREATE INDEX idx_todo_items_memo_id ON todo_items (memo_id)',
    );
    await db.execute(
      'CREATE INDEX idx_todo_items_is_completed ON todo_items (is_completed)',
    );
    await db.execute(
      'CREATE INDEX idx_todo_items_due_date ON todo_items (due_date)',
    );
    await db.execute(
      'CREATE INDEX idx_file_attachments_memo_id ON file_attachments (memo_id)',
    );
    await db.execute(
      'CREATE INDEX idx_athkar_routines_user_id ON athkar_routines (user_id)',
    );
    await db.execute(
      'CREATE INDEX idx_athkar_steps_routine_id ON athkar_steps (routine_id)',
    );
    await db.execute('CREATE INDEX idx_accounts_user_id ON accounts (user_id)');
    await db.execute(
      'CREATE INDEX idx_categories_user_id ON categories (user_id)',
    );
    await db.execute(
      'CREATE INDEX idx_transactions_user_id ON transactions (user_id)',
    );
    await db.execute(
      'CREATE INDEX idx_transactions_account_id ON transactions (account_id)',
    );
    await db.execute(
      'CREATE INDEX idx_transactions_date ON transactions (date)',
    );
    await db.execute('CREATE INDEX idx_budgets_user_id ON budgets (user_id)');
  }

  Future<void> _insertDefaultData(Database db) async {
    // Insert default categories
    final defaultCategories = [
      "('cat_food', 'default_user', 'Food & Dining', 'Restaurants, groceries, etc.', 'expense', NULL, '#FF5722', 'restaurant', 1, '${DateTime.now().toIso8601String()}', '${DateTime.now().toIso8601String()}', 0)",
      "('cat_transport', 'default_user', 'Transportation', 'Gas, public transport, etc.', 'expense', NULL, '#2196F3', 'directions_car', 1, '${DateTime.now().toIso8601String()}', '${DateTime.now().toIso8601String()}', 0)",
      "('cat_entertainment', 'default_user', 'Entertainment', 'Movies, games, etc.', 'expense', NULL, '#9C27B0', 'movie', 1, '${DateTime.now().toIso8601String()}', '${DateTime.now().toIso8601String()}', 0)",
      "('cat_salary', 'default_user', 'Salary', 'Monthly salary', 'income', NULL, '#4CAF50', 'work', 1, '${DateTime.now().toIso8601String()}', '${DateTime.now().toIso8601String()}', 0)",
    ];

    for (final category in defaultCategories) {
      await db.execute('INSERT INTO categories VALUES $category');
    }
  }

  Future<void> closeDatabase() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  Future<void> deleteDatabase() async {
    if (kIsWeb) {
      // For web, just close and recreate in-memory database
      await _database?.close();
      _database = null;
    } else {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, AppConstants.databaseName);
      await databaseFactory.deleteDatabase(path);
      _database = null;
    }
  }
}
