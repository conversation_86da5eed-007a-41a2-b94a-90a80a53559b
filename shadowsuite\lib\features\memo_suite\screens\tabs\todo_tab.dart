import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/memo_model.dart';
import '../../../../core/models/todo_item_model.dart';
import '../../providers/memo_provider.dart';
import '../../providers/search_provider.dart';
import '../../services/audio_service.dart';
import '../create_memo_screen.dart';

class TodoTab extends ConsumerStatefulWidget {
  final AudioService audioService;

  const TodoTab({super.key, required this.audioService});

  @override
  ConsumerState<TodoTab> createState() => _TodoTabState();
}

class _TodoTabState extends ConsumerState<TodoTab> {
  final TextEditingController _searchController = TextEditingController();
  String _filterBy = 'all';
  String _sortBy = 'date';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final memosAsync = ref.watch(memosProvider);
    final searchQuery = ref.watch(memoSearchQueryProvider);

    return memosAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(memosProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
      data: (allMemos) {
        // Filter for todo memos and memos with todo items
        var memos = allMemos
            .where(
              (memo) => memo.type == MemoType.todo || memo.todoItems.isNotEmpty,
            )
            .toList();

        // Apply search filter
        if (searchQuery.isNotEmpty) {
          memos = memos
              .where(
                (memo) =>
                    memo.title.toLowerCase().contains(
                      searchQuery.toLowerCase(),
                    ) ||
                    (memo.description?.toLowerCase().contains(
                          searchQuery.toLowerCase(),
                        ) ??
                        false) ||
                    memo.todoItems.any(
                      (todo) => todo.text.toLowerCase().contains(
                        searchQuery.toLowerCase(),
                      ),
                    ),
              )
              .toList();
        }

        // Apply completion filter
        if (_filterBy != 'all') {
          memos = memos.where((memo) {
            final totalTodos = memo.todoItems.length;
            final completedTodos = memo.todoItems
                .where((t) => t.isCompleted)
                .length;

            switch (_filterBy) {
              case 'completed':
                return totalTodos > 0 && completedTodos == totalTodos;
              case 'incomplete':
                return totalTodos > 0 && completedTodos < totalTodos;
              case 'empty':
                return totalTodos == 0;
              default:
                return true;
            }
          }).toList();
        }

        // Apply sorting
        _sortMemos(memos);

        return Scaffold(
          body: Column(
            children: [
              // Search and filter section
              _buildSearchAndFilters(),

              // Statistics bar
              _buildStatsBar(memos),

              // Content
              Expanded(
                child: memos.isEmpty
                    ? _buildEmptyState()
                    : _buildTodosList(memos),
              ),
            ],
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () => _createTodoMemo(),
            child: const Icon(Icons.add_task),
          ),
        );
      },
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search todos...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        ref.read(memoSearchQueryProvider.notifier).state = '';
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.background,
            ),
            onChanged: (value) {
              ref.read(memoSearchQueryProvider.notifier).state = value;
            },
          ),
          const SizedBox(height: 12),

          // Filter row
          Row(
            children: [
              // Filter dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _filterBy,
                  decoration: const InputDecoration(
                    labelText: 'Filter',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All Lists')),
                    DropdownMenuItem(
                      value: 'incomplete',
                      child: Text('In Progress'),
                    ),
                    DropdownMenuItem(
                      value: 'completed',
                      child: Text('Completed'),
                    ),
                    DropdownMenuItem(
                      value: 'empty',
                      child: Text('Empty Lists'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _filterBy = value!;
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),

              // Sort dropdown
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  decoration: const InputDecoration(
                    labelText: 'Sort by',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'date', child: Text('Date')),
                    DropdownMenuItem(value: 'title', child: Text('Title')),
                    DropdownMenuItem(
                      value: 'progress',
                      child: Text('Progress'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsBar(List<MemoModel> memos) {
    final totalLists = memos.length;
    final totalTodos = memos.expand((m) => m.todoItems).length;
    final completedTodos = memos
        .expand((m) => m.todoItems)
        .where((t) => t.isCompleted)
        .length;
    final completedLists = memos
        .where(
          (m) =>
              m.todoItems.isNotEmpty && m.todoItems.every((t) => t.isCompleted),
        )
        .length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.primaryContainer.withValues(alpha: 0.3),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Lists', totalLists.toString(), Icons.list),
          _buildStatItem('Tasks', totalTodos.toString(), Icons.task),
          _buildStatItem(
            'Done',
            '$completedTodos/$totalTodos',
            Icons.check_circle,
          ),
          _buildStatItem('Complete', completedLists.toString(), Icons.done_all),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Theme.of(context).colorScheme.primary),
        const SizedBox(height: 2),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        Text(label, style: Theme.of(context).textTheme.labelSmall),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.checklist, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No todo lists yet',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to create your first todo list',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildTodosList(List<MemoModel> memos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: memos.length,
      itemBuilder: (context, index) {
        final memo = memos[index];
        final totalTodos = memo.todoItems.length;
        final completedTodos = memo.todoItems
            .where((t) => t.isCompleted)
            .length;
        final progress = totalTodos > 0 ? completedTodos / totalTodos : 0.0;

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ExpansionTile(
            leading: CircleAvatar(
              backgroundColor: Colors.purple.withValues(alpha: 0.1),
              child: Icon(
                totalTodos == 0
                    ? Icons.list
                    : progress == 1.0
                    ? Icons.check_circle
                    : Icons.checklist,
                color: totalTodos == 0
                    ? Colors.grey
                    : progress == 1.0
                    ? Colors.green
                    : Colors.purple,
              ),
            ),
            title: Text(memo.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (memo.description != null)
                  Text(
                    memo.description!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '$completedTodos/$totalTodos tasks',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(width: 8),
                    if (totalTodos > 0) ...[
                      Expanded(
                        child: LinearProgressIndicator(
                          value: progress,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            progress == 1.0 ? Colors.green : Colors.purple,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${(progress * 100).round()}%',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('Edit'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              onSelected: (value) => _handleMenuAction(value, memo),
            ),
            children: [
              if (memo.todoItems.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    'No tasks in this list',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              else
                ...memo.todoItems.map((todo) => _buildTodoItem(memo, todo)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTodoItem(MemoModel memo, TodoItemModel todo) {
    return ListTile(
      leading: Checkbox(
        value: todo.isCompleted,
        onChanged: (value) => _toggleTodoCompletion(memo, todo),
      ),
      title: Text(
        todo.text,
        style: TextStyle(
          decoration: todo.isCompleted ? TextDecoration.lineThrough : null,
          color: todo.isCompleted ? Colors.grey : null,
        ),
      ),
      subtitle: Row(
        children: [
          Icon(
            _getPriorityIcon(todo.priority),
            size: 14,
            color: _getPriorityColor(todo.priority),
          ),
          const SizedBox(width: 4),
          Text(
            todo.priority.name.toUpperCase(),
            style: TextStyle(
              color: _getPriorityColor(todo.priority),
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (todo.dueDate != null) ...[
            const SizedBox(width: 12),
            Icon(Icons.schedule, size: 14, color: Colors.grey[600]),
            const SizedBox(width: 4),
            Text(
              _formatDate(todo.dueDate!),
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }

  void _sortMemos(List<MemoModel> memos) {
    switch (_sortBy) {
      case 'date':
        memos.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'title':
        memos.sort((a, b) => a.title.compareTo(b.title));
        break;
      case 'progress':
        memos.sort((a, b) {
          final aProgress = a.todoItems.isEmpty
              ? 0.0
              : a.todoItems.where((t) => t.isCompleted).length /
                    a.todoItems.length;
          final bProgress = b.todoItems.isEmpty
              ? 0.0
              : b.todoItems.where((t) => t.isCompleted).length /
                    b.todoItems.length;
          return bProgress.compareTo(aProgress);
        });
        break;
    }
  }

  void _createTodoMemo() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            const CreateMemoScreen(initialType: MemoType.todo),
      ),
    );
  }

  void _handleMenuAction(String action, MemoModel memo) {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CreateMemoScreen(editingMemo: memo),
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(memo);
        break;
    }
  }

  void _toggleTodoCompletion(MemoModel memo, TodoItemModel todo) {
    final updatedTodos = memo.todoItems
        .map(
          (t) => t.id == todo.id ? t.copyWith(isCompleted: !t.isCompleted) : t,
        )
        .toList();

    final updatedMemo = memo.copyWith(todoItems: updatedTodos);
    ref.read(memosProvider.notifier).updateMemo(updatedMemo);
  }

  void _showDeleteConfirmation(MemoModel memo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Todo List'),
        content: Text('Are you sure you want to delete "${memo.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(memosProvider.notifier).deleteMemo(memo.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Todo list deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  IconData _getPriorityIcon(Priority priority) {
    switch (priority) {
      case Priority.low:
        return Icons.keyboard_arrow_down;
      case Priority.medium:
        return Icons.remove;
      case Priority.high:
        return Icons.keyboard_arrow_up;
      case Priority.urgent:
        return Icons.priority_high;
    }
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.low:
        return Colors.green;
      case Priority.medium:
        return Colors.orange;
      case Priority.high:
        return Colors.red;
      case Priority.urgent:
        return Colors.purple;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
