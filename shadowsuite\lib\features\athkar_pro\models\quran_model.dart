class SurahModel {
  final int number;
  final String nameArabic;
  final String nameEnglish;
  final String nameTransliteration;
  final String meaning;
  final int versesCount;
  final String revelationType; // 'Meccan' or 'Medinan'
  final int revelationOrder;
  final List<VerseModel> verses;
  final bool isFavorite;
  final DateTime? lastReadAt;
  final int? lastReadVerse;

  const SurahModel({
    required this.number,
    required this.nameArabic,
    required this.nameEnglish,
    required this.nameTransliteration,
    required this.meaning,
    required this.versesCount,
    required this.revelationType,
    required this.revelationOrder,
    this.verses = const [],
    this.isFavorite = false,
    this.lastReadAt,
    this.lastReadVerse,
  });

  SurahModel copyWith({
    int? number,
    String? nameArabic,
    String? nameEnglish,
    String? nameTransliteration,
    String? meaning,
    int? versesCount,
    String? revelationType,
    int? revelationOrder,
    List<VerseModel>? verses,
    bool? isFavorite,
    DateTime? lastReadAt,
    int? lastReadVerse,
  }) {
    return SurahModel(
      number: number ?? this.number,
      nameArabic: nameArabic ?? this.nameArabic,
      nameEnglish: nameEnglish ?? this.nameEnglish,
      nameTransliteration: nameTransliteration ?? this.nameTransliteration,
      meaning: meaning ?? this.meaning,
      versesCount: versesCount ?? this.versesCount,
      revelationType: revelationType ?? this.revelationType,
      revelationOrder: revelationOrder ?? this.revelationOrder,
      verses: verses ?? this.verses,
      isFavorite: isFavorite ?? this.isFavorite,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      lastReadVerse: lastReadVerse ?? this.lastReadVerse,
    );
  }

  double get readingProgress {
    if (lastReadVerse == null || versesCount == 0) return 0.0;
    return (lastReadVerse! / versesCount).clamp(0.0, 1.0);
  }

  bool get isCompleted => lastReadVerse != null && lastReadVerse! >= versesCount;

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name_arabic': nameArabic,
      'name_english': nameEnglish,
      'name_transliteration': nameTransliteration,
      'meaning': meaning,
      'verses_count': versesCount,
      'revelation_type': revelationType,
      'revelation_order': revelationOrder,
      'verses': verses.map((v) => v.toJson()).toList(),
      'is_favorite': isFavorite,
      'last_read_at': lastReadAt?.toIso8601String(),
      'last_read_verse': lastReadVerse,
    };
  }

  factory SurahModel.fromJson(Map<String, dynamic> json) {
    return SurahModel(
      number: json['number'],
      nameArabic: json['name_arabic'],
      nameEnglish: json['name_english'],
      nameTransliteration: json['name_transliteration'],
      meaning: json['meaning'],
      versesCount: json['verses_count'],
      revelationType: json['revelation_type'],
      revelationOrder: json['revelation_order'],
      verses: (json['verses'] as List<dynamic>?)
          ?.map((v) => VerseModel.fromJson(v))
          .toList() ?? [],
      isFavorite: json['is_favorite'] ?? false,
      lastReadAt: json['last_read_at'] != null 
          ? DateTime.parse(json['last_read_at']) 
          : null,
      lastReadVerse: json['last_read_verse'],
    );
  }

  @override
  String toString() {
    return 'SurahModel(number: $number, nameEnglish: $nameEnglish)';
  }
}

class VerseModel {
  final int number;
  final String textArabic;
  final String textUthmani; // Uthmani script
  final List<TranslationModel> translations;
  final String? transliteration;
  final List<String> keywords;
  final bool isBookmarked;
  final List<String> notes;
  final String? audioUrl;
  final int? juzNumber;
  final int? hizbNumber;
  final int? rukuNumber;
  final int? manzilNumber;
  final int? pageNumber;

  const VerseModel({
    required this.number,
    required this.textArabic,
    required this.textUthmani,
    this.translations = const [],
    this.transliteration,
    this.keywords = const [],
    this.isBookmarked = false,
    this.notes = const [],
    this.audioUrl,
    this.juzNumber,
    this.hizbNumber,
    this.rukuNumber,
    this.manzilNumber,
    this.pageNumber,
  });

  VerseModel copyWith({
    int? number,
    String? textArabic,
    String? textUthmani,
    List<TranslationModel>? translations,
    String? transliteration,
    List<String>? keywords,
    bool? isBookmarked,
    List<String>? notes,
    String? audioUrl,
    int? juzNumber,
    int? hizbNumber,
    int? rukuNumber,
    int? manzilNumber,
    int? pageNumber,
  }) {
    return VerseModel(
      number: number ?? this.number,
      textArabic: textArabic ?? this.textArabic,
      textUthmani: textUthmani ?? this.textUthmani,
      translations: translations ?? this.translations,
      transliteration: transliteration ?? this.transliteration,
      keywords: keywords ?? this.keywords,
      isBookmarked: isBookmarked ?? this.isBookmarked,
      notes: notes ?? this.notes,
      audioUrl: audioUrl ?? this.audioUrl,
      juzNumber: juzNumber ?? this.juzNumber,
      hizbNumber: hizbNumber ?? this.hizbNumber,
      rukuNumber: rukuNumber ?? this.rukuNumber,
      manzilNumber: manzilNumber ?? this.manzilNumber,
      pageNumber: pageNumber ?? this.pageNumber,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text_arabic': textArabic,
      'text_uthmani': textUthmani,
      'translations': translations.map((t) => t.toJson()).toList(),
      'transliteration': transliteration,
      'keywords': keywords,
      'is_bookmarked': isBookmarked,
      'notes': notes,
      'audio_url': audioUrl,
      'juz_number': juzNumber,
      'hizb_number': hizbNumber,
      'ruku_number': rukuNumber,
      'manzil_number': manzilNumber,
      'page_number': pageNumber,
    };
  }

  factory VerseModel.fromJson(Map<String, dynamic> json) {
    return VerseModel(
      number: json['number'],
      textArabic: json['text_arabic'],
      textUthmani: json['text_uthmani'] ?? json['text_arabic'],
      translations: (json['translations'] as List<dynamic>?)
          ?.map((t) => TranslationModel.fromJson(t))
          .toList() ?? [],
      transliteration: json['transliteration'],
      keywords: List<String>.from(json['keywords'] ?? []),
      isBookmarked: json['is_bookmarked'] ?? false,
      notes: List<String>.from(json['notes'] ?? []),
      audioUrl: json['audio_url'],
      juzNumber: json['juz_number'],
      hizbNumber: json['hizb_number'],
      rukuNumber: json['ruku_number'],
      manzilNumber: json['manzil_number'],
      pageNumber: json['page_number'],
    );
  }

  @override
  String toString() {
    return 'VerseModel(number: $number)';
  }
}

class TranslationModel {
  final String id;
  final String language;
  final String translatorName;
  final String text;
  final String? footnote;

  const TranslationModel({
    required this.id,
    required this.language,
    required this.translatorName,
    required this.text,
    this.footnote,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'language': language,
      'translator_name': translatorName,
      'text': text,
      'footnote': footnote,
    };
  }

  factory TranslationModel.fromJson(Map<String, dynamic> json) {
    return TranslationModel(
      id: json['id'],
      language: json['language'],
      translatorName: json['translator_name'],
      text: json['text'],
      footnote: json['footnote'],
    );
  }
}

class BookmarkModel {
  final String id;
  final String userId;
  final int surahNumber;
  final int verseNumber;
  final String? note;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BookmarkModel({
    required this.id,
    required this.userId,
    required this.surahNumber,
    required this.verseNumber,
    this.note,
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  BookmarkModel copyWith({
    String? id,
    String? userId,
    int? surahNumber,
    int? verseNumber,
    String? note,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BookmarkModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      surahNumber: surahNumber ?? this.surahNumber,
      verseNumber: verseNumber ?? this.verseNumber,
      note: note ?? this.note,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'surah_number': surahNumber,
      'verse_number': verseNumber,
      'note': note,
      'tags': tags,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory BookmarkModel.fromJson(Map<String, dynamic> json) {
    return BookmarkModel(
      id: json['id'],
      userId: json['user_id'],
      surahNumber: json['surah_number'],
      verseNumber: json['verse_number'],
      note: json['note'],
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  @override
  String toString() {
    return 'BookmarkModel(surah: $surahNumber, verse: $verseNumber)';
  }
}

class ReadingSessionModel {
  final String id;
  final String userId;
  final int surahNumber;
  final int startVerse;
  final int? endVerse;
  final DateTime startTime;
  final DateTime? endTime;
  final int duration; // in seconds
  final bool isCompleted;

  const ReadingSessionModel({
    required this.id,
    required this.userId,
    required this.surahNumber,
    required this.startVerse,
    this.endVerse,
    required this.startTime,
    this.endTime,
    this.duration = 0,
    this.isCompleted = false,
  });

  ReadingSessionModel copyWith({
    String? id,
    String? userId,
    int? surahNumber,
    int? startVerse,
    int? endVerse,
    DateTime? startTime,
    DateTime? endTime,
    int? duration,
    bool? isCompleted,
  }) {
    return ReadingSessionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      surahNumber: surahNumber ?? this.surahNumber,
      startVerse: startVerse ?? this.startVerse,
      endVerse: endVerse ?? this.endVerse,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'surah_number': surahNumber,
      'start_verse': startVerse,
      'end_verse': endVerse,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'duration': duration,
      'is_completed': isCompleted,
    };
  }

  factory ReadingSessionModel.fromJson(Map<String, dynamic> json) {
    return ReadingSessionModel(
      id: json['id'],
      userId: json['user_id'],
      surahNumber: json['surah_number'],
      startVerse: json['start_verse'],
      endVerse: json['end_verse'],
      startTime: DateTime.parse(json['start_time']),
      endTime: json['end_time'] != null 
          ? DateTime.parse(json['end_time']) 
          : null,
      duration: json['duration'] ?? 0,
      isCompleted: json['is_completed'] ?? false,
    );
  }
}
