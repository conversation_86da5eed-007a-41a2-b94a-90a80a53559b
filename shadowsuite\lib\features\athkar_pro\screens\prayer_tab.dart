import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/prayer_time_model.dart';
import '../providers/prayer_provider.dart';
import 'prayer_time_settings_screen.dart';

class PrayerTab extends ConsumerStatefulWidget {
  const PrayerTab({super.key});

  @override
  ConsumerState<PrayerTab> createState() => _PrayerTabState();
}

class _PrayerTabState extends ConsumerState<PrayerTab> {
  @override
  Widget build(BuildContext context) {
    final prayerTimesAsync = ref.watch(prayerTimesProvider);
    final currentPrayer = ref.watch(currentPrayerProvider);
    final nextPrayer = ref.watch(nextPrayerProvider);

    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current prayer status
            _buildCurrentPrayerCard(currentPrayer, nextPrayer),
            const SizedBox(height: 20),

            // Prayer times list
            _buildPrayerTimesList(prayerTimesAsync),
            const SizedBox(height: 20),

            // Quick actions
            _buildQuickActions(),
            const SizedBox(height: 20),

            // Prayer statistics
            _buildPrayerStats(),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const PrayerTimeSettingsScreen(),
            ),
          );
        },
        child: const Icon(Icons.settings),
      ),
    );
  }

  Widget _buildCurrentPrayerCard(
    PrayerTimeModel? current,
    PrayerTimeModel? next,
  ) {
    return Card(
      color: Colors.blue.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.access_time, size: 32, color: Colors.blue),
                const SizedBox(width: 12),
                Text(
                  'Prayer Times',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (current != null) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: current.isCompleted
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      current.isCompleted ? Icons.check_circle : Icons.schedule,
                      color: current.isCompleted ? Colors.green : Colors.orange,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            current.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${current.time.hour.toString().padLeft(2, '0')}:${current.time.minute.toString().padLeft(2, '0')}',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (!current.isCompleted)
                      ElevatedButton(
                        onPressed: () => _markPrayerCompleted(current),
                        child: const Text('Mark Done'),
                      ),
                  ],
                ),
              ),

              if (next != null) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(Icons.arrow_forward, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Text(
                      'Next: ${next.name} at ${next.time.hour.toString().padLeft(2, '0')}:${next.time.minute.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ] else ...[
              const Text(
                'No prayer times available. Please configure your location.',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerTimesList(
    AsyncValue<List<PrayerTimeModel>> prayerTimesAsync,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Prayer Times',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        prayerTimesAsync.when(
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Icon(Icons.error, size: 48, color: Colors.red),
                  const SizedBox(height: 8),
                  Text('Error loading prayer times: $error'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => ref.invalidate(prayerTimesProvider),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),
          data: (prayerTimes) {
            if (prayerTimes.isEmpty) {
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.location_off,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'No prayer times available',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Please configure your location in settings',
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) =>
                                  const PrayerTimeSettingsScreen(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.settings),
                        label: const Text('Configure'),
                      ),
                    ],
                  ),
                ),
              );
            }

            return Column(
              children: prayerTimes
                  .map((prayer) => _buildPrayerTimeCard(prayer))
                  .toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPrayerTimeCard(PrayerTimeModel prayer) {
    final now = DateTime.now();
    final isPast = prayer.time.isBefore(now);
    final isCurrent = !isPast && prayer.time.difference(now).inHours < 1;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      color: prayer.isCompleted
          ? Colors.green.withValues(alpha: 0.1)
          : isCurrent
          ? Colors.orange.withValues(alpha: 0.1)
          : null,
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: prayer.isCompleted
                ? Colors.green.withValues(alpha: 0.2)
                : isCurrent
                ? Colors.orange.withValues(alpha: 0.2)
                : Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                prayer.isCompleted
                    ? Icons.check_circle
                    : isCurrent
                    ? Icons.schedule
                    : Icons.access_time,
                color: prayer.isCompleted
                    ? Colors.green
                    : isCurrent
                    ? Colors.orange
                    : Colors.blue,
                size: 20,
              ),
              Text(
                prayer.name,
                style: TextStyle(
                  fontSize: 8,
                  color: prayer.isCompleted
                      ? Colors.green
                      : isCurrent
                      ? Colors.orange
                      : Colors.blue,
                ),
                textDirection: TextDirection.rtl,
              ),
            ],
          ),
        ),
        title: Row(
          children: [
            Text(
              prayer.name,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isPast && !prayer.isCompleted ? Colors.red : null,
              ),
            ),
          ],
        ),
        subtitle: Text(
          '${prayer.time.hour.toString().padLeft(2, '0')}:${prayer.time.minute.toString().padLeft(2, '0')}',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: isPast && !prayer.isCompleted
                ? Colors.red
                : Colors.grey[600],
          ),
        ),
        trailing: prayer.isCompleted
            ? const Icon(Icons.check_circle, color: Colors.green)
            : isPast
            ? const Icon(Icons.warning, color: Colors.red)
            : isCurrent
            ? const Icon(Icons.notifications_active, color: Colors.orange)
            : null,
        onTap: () => _showPrayerDetails(prayer),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Card(
                child: InkWell(
                  onTap: _showQiblaDirection,
                  borderRadius: BorderRadius.circular(12),
                  child: const Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(Icons.explore, size: 32, color: Colors.teal),
                        SizedBox(height: 8),
                        Text(
                          'Qibla Direction',
                          style: TextStyle(fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Card(
                child: InkWell(
                  onTap: _showPrayerReminders,
                  borderRadius: BorderRadius.circular(12),
                  child: const Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          Icons.notifications,
                          size: 32,
                          color: Colors.purple,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Reminders',
                          style: TextStyle(fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPrayerStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Prayer Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(child: _buildStatItem('Today', '4/5', Colors.blue)),
                Expanded(
                  child: _buildStatItem('This Week', '28/35', Colors.green),
                ),
                Expanded(
                  child: _buildStatItem('This Month', '120/150', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
      ],
    );
  }

  void _markPrayerCompleted(PrayerTimeModel prayer) {
    ref.read(prayerCompletionProvider.notifier).markPrayerCompleted(prayer.id);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${prayer.name} marked as completed')),
    );
  }

  void _showPrayerDetails(PrayerTimeModel prayer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(prayer.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Time: ${prayer.time.hour.toString().padLeft(2, '0')}:${prayer.time.minute.toString().padLeft(2, '0')}',
            ),
            Text('Status: ${prayer.isCompleted ? 'Completed' : 'Pending'}'),
            Text('Location: ${prayer.location ?? 'Not set'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (!prayer.isCompleted)
            ElevatedButton(
              onPressed: () {
                _markPrayerCompleted(prayer);
                Navigator.of(context).pop();
              },
              child: const Text('Mark Completed'),
            ),
        ],
      ),
    );
  }

  void _showQiblaDirection() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Qibla direction feature coming soon!')),
    );
  }

  void _showPrayerReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Prayer reminders feature coming soon!')),
    );
  }
}
