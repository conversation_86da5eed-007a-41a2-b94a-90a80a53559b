import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import 'storage_service.dart';
import 'supabase_service.dart';
import 'sync_service.dart';

class AuthService {
  static AuthService? _instance;
  final SupabaseService _supabaseService = SupabaseService();
  final StorageService _storageService = StorageService();
  final SyncService _syncService = SyncService();

  final StreamController<AuthState> _authStateController =
      StreamController<AuthState>.broadcast();
  UserModel? _currentUser;

  AuthService._internal();

  factory AuthService() {
    _instance ??= AuthService._internal();
    return _instance!;
  }

  Future<void> initialize() async {
    try {
      // Check for existing session first
      await _restoreExistingSession();

      // Listen to Supabase auth state changes if available
      if (_supabaseService.isInitialized) {
        _supabaseService.authStateChanges?.listen((AuthState state) {
          _authStateController.add(state);
          _handleAuthStateChange(state);
        });

        // Check if user is already logged in
        final user = _supabaseService.currentUser;
        if (user != null) {
          await _loadUserProfile(user.id);
        }
      }
    } catch (e) {
      print('Auth initialization error: $e');
      // Continue without auth if Supabase is unavailable
    }
  }

  Future<void> _restoreExistingSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedUserId = prefs.getString(AppConstants.keyUserId);

      if (savedUserId != null) {
        print('🔄 Attempting to restore session for: $savedUserId');

        // Ensure storage is initialized
        if (!_storageService.isInitialized) {
          await _storageService.initialize();
        }

        // Try to load user from local storage
        await _loadUserProfile(savedUserId);

        if (_currentUser != null) {
          print('✅ Successfully restored user session: ${_currentUser!.email}');

          // Verify data persistence
          await _verifyUserDataPersistence();
        } else {
          print('⚠️ User data not found, clearing invalid session');
          await _clearUserSession();
        }
      } else {
        print('ℹ️ No saved user session found');
      }
    } catch (e) {
      print('❌ Failed to restore session: $e');
      // Clear potentially corrupted session
      await _clearUserSession();
    }
  }

  Future<void> _verifyUserDataPersistence() async {
    try {
      if (_currentUser == null) return;

      // Test that we can read the user data
      final users = await _storageService.query(
        'users',
        where: 'id = ?',
        whereArgs: [_currentUser!.id],
      );

      if (users.isEmpty) {
        print('⚠️ User data missing, recreating...');
        await _saveUserToDatabase(_currentUser!);
      }

      print('✅ User data persistence verified');
    } catch (e) {
      print('❌ Data persistence verification failed: $e');
    }
  }

  Stream<AuthState> get authStateChanges => _authStateController.stream;

  UserModel? get currentUser => _currentUser;

  bool get isAuthenticated => _currentUser != null;

  Future<AuthResult> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      debugPrint('🔐 Starting cloud sign up process for: $email');

      // Ensure Supabase is initialized
      if (!_supabaseService.isInitialized) {
        return AuthResult.failure(
          message:
              'Authentication service not available. Please check your internet connection.',
        );
      }

      debugPrint('☁️ Attempting Supabase sign up...');
      final response = await _supabaseService.signUp(
        email: email,
        password: password,
        displayName: displayName,
      );

      if (response.user != null) {
        debugPrint('✅ Supabase account created successfully');

        // Create user profile in Supabase (will be created when user signs in)
        // For now, just return success message
        return AuthResult.success(
          message:
              'Account created successfully! Please check your email for verification, then sign in.',
        );
      } else {
        return AuthResult.failure(
          message: 'Failed to create account. Please try again.',
        );
      }
    } on AuthException catch (e) {
      debugPrint('❌ Supabase sign up error: ${e.message}');
      return AuthResult.failure(message: e.message);
    } catch (e) {
      debugPrint('❌ Sign up error: $e');
      if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        return AuthResult.failure(
          message:
              'Network error. Please check your internet connection and try again.',
        );
      }
      return AuthResult.failure(message: 'Sign up failed. Please try again.');
    }
  }

  Future<AuthResult> signIn({
    required String email,
    required String password,
  }) async {
    try {
      print('🔐 Starting cloud sign in process for: $email');

      // Ensure Supabase is initialized
      if (!_supabaseService.isInitialized) {
        return AuthResult.failure(
          message:
              'Authentication service not available. Please check your internet connection.',
        );
      }

      print('☁️ Attempting Supabase sign in...');
      final response = await _supabaseService.signIn(
        email: email,
        password: password,
      );

      if (response.user != null) {
        print('✅ Supabase authentication successful');

        // Load/create user profile in Supabase
        await _loadOrCreateUserProfile(response.user!);
        await _saveUserSession(response.user!.id);

        // Start sync after successful login
        _syncService.syncAll().catchError((e) {
          print('Sync error after login: $e');
        });

        print('✅ User profile loaded and session saved');
        return AuthResult.success(message: 'Signed in successfully');
      } else {
        return AuthResult.failure(message: 'Invalid email or password');
      }
    } on AuthException catch (e) {
      print('❌ Supabase auth error: ${e.message}');
      return AuthResult.failure(message: e.message);
    } catch (e) {
      print('❌ Sign in error: $e');
      if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        return AuthResult.failure(
          message:
              'Network error. Please check your internet connection and try again.',
        );
      }
      return AuthResult.failure(message: 'Sign in failed. Please try again.');
    }
  }

  Future<void> _loadOrCreateUserProfile(User supabaseUser) async {
    try {
      debugPrint(
        '🔄 Loading user profile from Supabase for: ${supabaseUser.email}',
      );

      // Try to get existing user profile from Supabase
      final userModel = await _supabaseService.getUserProfile(supabaseUser.id);

      if (userModel != null) {
        debugPrint('✅ Found existing user profile in Supabase');
        _currentUser = userModel;
        // Also save to local storage for offline access
        await _saveUserToDatabase(userModel);
      } else {
        debugPrint('👤 Creating new user profile in Supabase');
        // Create new user profile in Supabase
        final newUser = UserModel(
          id: supabaseUser.id,
          email: supabaseUser.email ?? '',
          displayName:
              supabaseUser.userMetadata?['display_name'] ??
              supabaseUser.email?.split('@').first ??
              'User',
          avatarUrl: supabaseUser.userMetadata?['avatar_url'],
          bio: null,
          phone: supabaseUser.phone,
          location: null,
          website: null,
          createdAt: DateTime.parse(supabaseUser.createdAt),
          updatedAt: DateTime.now(),
          lastSeenAt: DateTime.now(),
          isActive: true,
          preferences: {},
        );

        // Save to Supabase first
        await _supabaseService.createUserProfile(newUser);
        _currentUser = newUser;

        // Also save to local storage
        await _saveUserToDatabase(newUser);
        debugPrint('✅ New user profile created and saved to Supabase');
      }
    } catch (e) {
      debugPrint('❌ Error loading/creating user profile: $e');
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      await _supabaseService.signOut();
      await _clearUserSession();
      _currentUser = null;
    } catch (e) {
      print('Error signing out: $e');
    }
  }

  Future<AuthResult> resetPassword(String email) async {
    try {
      await _supabaseService.resetPassword(email);
      return AuthResult.success(
        message: 'Password reset email sent. Please check your inbox.',
      );
    } on AuthException catch (e) {
      return AuthResult.failure(message: e.message);
    } catch (e) {
      return AuthResult.failure(message: AppConstants.errorGeneric);
    }
  }

  Future<void> updateProfile({
    String? displayName,
    String? avatarUrl,
    Map<String, dynamic>? preferences,
  }) async {
    if (_currentUser == null) return;

    try {
      final updatedUser = _currentUser!.copyWith(
        displayName: displayName ?? _currentUser!.displayName,
        avatarUrl: avatarUrl ?? _currentUser!.avatarUrl,
        preferences: preferences ?? _currentUser!.preferences,
        updatedAt: DateTime.now(),
      );

      // Update in local database
      await _updateUserInDatabase(updatedUser);

      // Update in Supabase
      await _supabaseService.updateUserProfile(updatedUser);

      _currentUser = updatedUser;
    } catch (e) {
      print('Error updating profile: $e');
      rethrow;
    }
  }

  Future<void> _handleAuthStateChange(AuthState state) async {
    if (state.event == AuthChangeEvent.signedIn &&
        state.session?.user != null) {
      await _loadUserProfile(state.session!.user.id);
      await _saveUserSession(state.session!.user.id);
    } else if (state.event == AuthChangeEvent.signedOut) {
      await _clearUserSession();
      _currentUser = null;
    }
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      // Try to load from local storage first
      final userRecords = await _storageService.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (userRecords.isNotEmpty) {
        _currentUser = _userFromDatabase(userRecords.first);
      } else {
        // Load from Supabase if not found locally
        final userModel = await _supabaseService.getUserProfile(userId);
        if (userModel != null) {
          await _saveUserToDatabase(userModel);
          _currentUser = userModel;
        }
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  Future<void> _saveUserToDatabase(UserModel user) async {
    await _storageService.insert('users', _userToDatabase(user));
  }

  Future<void> _updateUserInDatabase(UserModel user) async {
    await _storageService.update(
      'users',
      _userToDatabase(user),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Map<String, dynamic> _userToDatabase(UserModel user) {
    return {
      'id': user.id,
      'email': user.email,
      'display_name': user.displayName,
      'avatar_url': user.avatarUrl,
      'created_at': user.createdAt.toIso8601String(),
      'updated_at': user.updatedAt.toIso8601String(),
      'is_active': user.isActive ? 1 : 0,
      'preferences': user.preferences != null
          ? user.preferences.toString()
          : null,
    };
  }

  UserModel _userFromDatabase(Map<String, dynamic> data) {
    return UserModel(
      id: data['id'],
      email: data['email'],
      displayName: data['display_name'],
      avatarUrl: data['avatar_url'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      isActive: data['is_active'] == 1,
      preferences: data['preferences'] != null
          ? Map<String, dynamic>.from(data['preferences'])
          : null,
    );
  }

  Future<void> _saveUserSession(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.keyUserId, userId);
  }

  Future<void> _clearUserSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.keyUserId);
  }

  void dispose() {
    _authStateController.close();
  }
}

class AuthResult {
  final bool isSuccess;
  final String message;

  AuthResult.success({required this.message}) : isSuccess = true;
  AuthResult.failure({required this.message}) : isSuccess = false;
}
