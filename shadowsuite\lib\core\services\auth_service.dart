import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import 'storage_service.dart';
import 'supabase_service.dart';
import 'sync_service.dart';

class AuthService {
  static AuthService? _instance;
  final SupabaseService _supabaseService = SupabaseService();
  final StorageService _storageService = StorageService();
  final SyncService _syncService = SyncService();

  final StreamController<AuthState> _authStateController =
      StreamController<AuthState>.broadcast();
  UserModel? _currentUser;

  AuthService._internal();

  factory AuthService() {
    _instance ??= AuthService._internal();
    return _instance!;
  }

  Future<void> initialize() async {
    try {
      // Check for existing session first
      await _restoreExistingSession();

      // Listen to Supabase auth state changes if available
      if (_supabaseService.isInitialized) {
        _supabaseService.authStateChanges?.listen((AuthState state) {
          _authStateController.add(state);
          _handleAuthStateChange(state);
        });

        // Check if user is already logged in
        final user = _supabaseService.currentUser;
        if (user != null) {
          await _loadUserProfile(user.id);
        }
      }
    } catch (e) {
      print('Auth initialization error: $e');
      // Continue without auth if Supabase is unavailable
    }
  }

  Future<void> _restoreExistingSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedUserId = prefs.getString(AppConstants.keyUserId);

      if (savedUserId != null) {
        print('🔄 Attempting to restore session for: $savedUserId');

        // Ensure storage is initialized
        if (!_storageService.isInitialized) {
          await _storageService.initialize();
        }

        // Try to load user from local storage
        await _loadUserProfile(savedUserId);

        if (_currentUser != null) {
          print('✅ Successfully restored user session: ${_currentUser!.email}');

          // Verify data persistence
          await _verifyUserDataPersistence();
        } else {
          print('⚠️ User data not found, clearing invalid session');
          await _clearUserSession();
        }
      } else {
        print('ℹ️ No saved user session found');
      }
    } catch (e) {
      print('❌ Failed to restore session: $e');
      // Clear potentially corrupted session
      await _clearUserSession();
    }
  }

  Future<void> _verifyUserDataPersistence() async {
    try {
      if (_currentUser == null) return;

      // Test that we can read the user data
      final users = await _storageService.query(
        'users',
        where: 'id = ?',
        whereArgs: [_currentUser!.id],
      );

      if (users.isEmpty) {
        print('⚠️ User data missing, recreating...');
        await _saveUserToDatabase(_currentUser!);
      }

      print('✅ User data persistence verified');
    } catch (e) {
      print('❌ Data persistence verification failed: $e');
    }
  }

  Stream<AuthState> get authStateChanges => _authStateController.stream;

  UserModel? get currentUser => _currentUser;

  bool get isAuthenticated => _currentUser != null;

  Future<AuthResult> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      final response = await _supabaseService.signUp(
        email: email,
        password: password,
        displayName: displayName,
      );

      if (response.user != null) {
        // Create user profile in local database
        await _createUserProfile(response.user!, displayName);

        return AuthResult.success(
          message:
              'Account created successfully. Please check your email for verification.',
        );
      } else {
        return AuthResult.failure(
          message: 'Failed to create account. Please try again.',
        );
      }
    } on AuthException catch (e) {
      return AuthResult.failure(message: e.message);
    } catch (e) {
      return AuthResult.failure(message: AppConstants.errorGeneric);
    }
  }

  Future<AuthResult> signIn({
    required String email,
    required String password,
  }) async {
    try {
      print('🔐 Starting sign in process for: $email');

      // First try offline authentication
      final offlineResult = await _tryOfflineSignIn(email, password);
      if (offlineResult.isSuccess) {
        print('✅ Offline sign in successful');
        return offlineResult;
      }

      // If offline fails, try Supabase (only if available)
      if (_supabaseService.isInitialized) {
        try {
          print('☁️ Attempting Supabase sign in...');
          final response = await _supabaseService.signIn(
            email: email,
            password: password,
          );

          if (response.user != null) {
            await _loadUserProfile(response.user!.id);
            await _saveUserSession(response.user!.id);

            // Start sync after successful login (non-blocking)
            _syncService.syncAll().catchError((e) {
              print('Sync error after login: $e');
            });

            print('✅ Supabase sign in successful');
            return AuthResult.success(message: 'Signed in successfully');
          } else {
            return AuthResult.failure(message: 'Invalid credentials');
          }
        } catch (e) {
          print('❌ Supabase sign in failed: $e');
          // Fall back to creating a local account
          return await _createLocalAccount(email, password);
        }
      } else {
        print('⚠️ Supabase not available, creating local account');
        // Supabase not available, create local account
        return await _createLocalAccount(email, password);
      }
    } catch (e) {
      print('❌ Sign in error: $e');
      return AuthResult.failure(message: 'Sign in failed. Please try again.');
    }
  }

  Future<AuthResult> _tryOfflineSignIn(String email, String password) async {
    try {
      print('📱 Trying offline sign in...');
      // Check if user exists locally
      final users = await _storageService.query(
        'users',
        where: 'email = ?',
        whereArgs: [email],
      );

      if (users.isNotEmpty) {
        final user = _userFromDatabase(users.first);
        // For demo purposes, accept any password for existing local users
        // In production, you'd hash and verify passwords
        _currentUser = user;
        await _saveUserSession(user.id);
        print('✅ Found existing local user');
        return AuthResult.success(message: 'Welcome back! (Offline mode)');
      }

      print('ℹ️ No local user found');
      return AuthResult.failure(message: 'User not found locally');
    } catch (e) {
      print('❌ Offline sign in error: $e');
      return AuthResult.failure(message: 'Offline sign in failed');
    }
  }

  Future<AuthResult> _createLocalAccount(String email, String password) async {
    try {
      print('👤 Creating local account...');
      // Create a local user account
      final userId = 'local_${DateTime.now().millisecondsSinceEpoch}';
      final user = UserModel(
        id: userId,
        email: email,
        displayName: email.split('@').first,
        avatarUrl: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
        preferences: {},
      );

      await _saveUserToDatabase(user);
      await _saveUserSession(userId);
      _currentUser = user;

      print('✅ Local account created successfully');
      return AuthResult.success(
        message: 'Account created successfully! (Offline mode)',
      );
    } catch (e) {
      print('❌ Local account creation error: $e');
      return AuthResult.failure(
        message: 'Failed to create account. Please try again.',
      );
    }
  }

  Future<void> signOut() async {
    try {
      await _supabaseService.signOut();
      await _clearUserSession();
      _currentUser = null;
    } catch (e) {
      print('Error signing out: $e');
    }
  }

  Future<AuthResult> resetPassword(String email) async {
    try {
      await _supabaseService.resetPassword(email);
      return AuthResult.success(
        message: 'Password reset email sent. Please check your inbox.',
      );
    } on AuthException catch (e) {
      return AuthResult.failure(message: e.message);
    } catch (e) {
      return AuthResult.failure(message: AppConstants.errorGeneric);
    }
  }

  Future<void> updateProfile({
    String? displayName,
    String? avatarUrl,
    Map<String, dynamic>? preferences,
  }) async {
    if (_currentUser == null) return;

    try {
      final updatedUser = _currentUser!.copyWith(
        displayName: displayName ?? _currentUser!.displayName,
        avatarUrl: avatarUrl ?? _currentUser!.avatarUrl,
        preferences: preferences ?? _currentUser!.preferences,
        updatedAt: DateTime.now(),
      );

      // Update in local database
      await _updateUserInDatabase(updatedUser);

      // Update in Supabase
      await _supabaseService.updateUserProfile(updatedUser);

      _currentUser = updatedUser;
    } catch (e) {
      print('Error updating profile: $e');
      rethrow;
    }
  }

  Future<void> _handleAuthStateChange(AuthState state) async {
    if (state.event == AuthChangeEvent.signedIn &&
        state.session?.user != null) {
      await _loadUserProfile(state.session!.user.id);
      await _saveUserSession(state.session!.user.id);
    } else if (state.event == AuthChangeEvent.signedOut) {
      await _clearUserSession();
      _currentUser = null;
    }
  }

  Future<void> _createUserProfile(User user, String? displayName) async {
    final userModel = UserModel(
      id: user.id,
      email: user.email!,
      displayName: displayName ?? user.userMetadata?['display_name'],
      avatarUrl: user.userMetadata?['avatar_url'],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isActive: true,
      preferences: {},
    );

    await _saveUserToDatabase(userModel);
    _currentUser = userModel;
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      // Try to load from local storage first
      final userRecords = await _storageService.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (userRecords.isNotEmpty) {
        _currentUser = _userFromDatabase(userRecords.first);
      } else {
        // Load from Supabase if not found locally
        final userModel = await _supabaseService.getUserProfile(userId);
        if (userModel != null) {
          await _saveUserToDatabase(userModel);
          _currentUser = userModel;
        }
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  Future<void> _saveUserToDatabase(UserModel user) async {
    await _storageService.insert('users', _userToDatabase(user));
  }

  Future<void> _updateUserInDatabase(UserModel user) async {
    await _storageService.update(
      'users',
      _userToDatabase(user),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Map<String, dynamic> _userToDatabase(UserModel user) {
    return {
      'id': user.id,
      'email': user.email,
      'display_name': user.displayName,
      'avatar_url': user.avatarUrl,
      'created_at': user.createdAt.toIso8601String(),
      'updated_at': user.updatedAt.toIso8601String(),
      'is_active': user.isActive ? 1 : 0,
      'preferences': user.preferences != null
          ? user.preferences.toString()
          : null,
    };
  }

  UserModel _userFromDatabase(Map<String, dynamic> data) {
    return UserModel(
      id: data['id'],
      email: data['email'],
      displayName: data['display_name'],
      avatarUrl: data['avatar_url'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      isActive: data['is_active'] == 1,
      preferences: data['preferences'] != null
          ? Map<String, dynamic>.from(data['preferences'])
          : null,
    );
  }

  Future<void> _saveUserSession(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.keyUserId, userId);
  }

  Future<void> _clearUserSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.keyUserId);
  }

  void dispose() {
    _authStateController.close();
  }
}

class AuthResult {
  final bool isSuccess;
  final String message;

  AuthResult.success({required this.message}) : isSuccess = true;
  AuthResult.failure({required this.message}) : isSuccess = false;
}
