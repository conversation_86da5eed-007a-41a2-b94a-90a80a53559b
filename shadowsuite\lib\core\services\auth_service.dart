import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import 'database_service.dart';
import 'supabase_service.dart';
import 'sync_service.dart';

class AuthService {
  static AuthService? _instance;
  final SupabaseService _supabaseService = SupabaseService();
  final DatabaseService _databaseService = DatabaseService();
  final SyncService _syncService = SyncService();

  final StreamController<AuthState> _authStateController =
      StreamController<AuthState>.broadcast();
  UserModel? _currentUser;

  AuthService._internal();

  factory AuthService() {
    _instance ??= AuthService._internal();
    return _instance!;
  }

  Future<void> initialize() async {
    // Listen to Supabase auth state changes if available
    _supabaseService.authStateChanges?.listen((AuthState state) {
      _authStateController.add(state);
      _handleAuthStateChange(state);
    });

    // Check if user is already logged in
    final user = _supabaseService.currentUser;
    if (user != null) {
      await _loadUserProfile(user.id);
    }
  }

  Stream<AuthState> get authStateChanges => _authStateController.stream;

  UserModel? get currentUser => _currentUser;

  bool get isAuthenticated => _currentUser != null;

  Future<AuthResult> signUp({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      final response = await _supabaseService.signUp(
        email: email,
        password: password,
        displayName: displayName,
      );

      if (response.user != null) {
        // Create user profile in local database
        await _createUserProfile(response.user!, displayName);

        return AuthResult.success(
          message:
              'Account created successfully. Please check your email for verification.',
        );
      } else {
        return AuthResult.failure(
          message: 'Failed to create account. Please try again.',
        );
      }
    } on AuthException catch (e) {
      return AuthResult.failure(message: e.message);
    } catch (e) {
      return AuthResult.failure(message: AppConstants.errorGeneric);
    }
  }

  Future<AuthResult> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabaseService.signIn(
        email: email,
        password: password,
      );

      if (response.user != null) {
        await _loadUserProfile(response.user!.id);
        await _saveUserSession(response.user!.id);

        // Start sync after successful login
        await _syncService.syncAll();

        return AuthResult.success(message: 'Signed in successfully');
      } else {
        return AuthResult.failure(message: 'Invalid credentials');
      }
    } on AuthException catch (e) {
      return AuthResult.failure(message: e.message);
    } catch (e) {
      return AuthResult.failure(message: AppConstants.errorAuth);
    }
  }

  Future<void> signOut() async {
    try {
      await _supabaseService.signOut();
      await _clearUserSession();
      _currentUser = null;
    } catch (e) {
      print('Error signing out: $e');
    }
  }

  Future<AuthResult> resetPassword(String email) async {
    try {
      await _supabaseService.resetPassword(email);
      return AuthResult.success(
        message: 'Password reset email sent. Please check your inbox.',
      );
    } on AuthException catch (e) {
      return AuthResult.failure(message: e.message);
    } catch (e) {
      return AuthResult.failure(message: AppConstants.errorGeneric);
    }
  }

  Future<void> updateProfile({
    String? displayName,
    String? avatarUrl,
    Map<String, dynamic>? preferences,
  }) async {
    if (_currentUser == null) return;

    try {
      final updatedUser = _currentUser!.copyWith(
        displayName: displayName ?? _currentUser!.displayName,
        avatarUrl: avatarUrl ?? _currentUser!.avatarUrl,
        preferences: preferences ?? _currentUser!.preferences,
        updatedAt: DateTime.now(),
      );

      // Update in local database
      await _updateUserInDatabase(updatedUser);

      // Update in Supabase
      await _supabaseService.updateUserProfile(updatedUser);

      _currentUser = updatedUser;
    } catch (e) {
      print('Error updating profile: $e');
      rethrow;
    }
  }

  Future<void> _handleAuthStateChange(AuthState state) async {
    if (state.event == AuthChangeEvent.signedIn &&
        state.session?.user != null) {
      await _loadUserProfile(state.session!.user.id);
      await _saveUserSession(state.session!.user.id);
    } else if (state.event == AuthChangeEvent.signedOut) {
      await _clearUserSession();
      _currentUser = null;
    }
  }

  Future<void> _createUserProfile(User user, String? displayName) async {
    final userModel = UserModel(
      id: user.id,
      email: user.email!,
      displayName: displayName ?? user.userMetadata?['display_name'],
      avatarUrl: user.userMetadata?['avatar_url'],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isActive: true,
      preferences: {},
    );

    await _saveUserToDatabase(userModel);
    _currentUser = userModel;
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      // Try to load from local database first
      final db = await _databaseService.database;
      final userRecords = await db.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (userRecords.isNotEmpty) {
        _currentUser = _userFromDatabase(userRecords.first);
      } else {
        // Load from Supabase if not found locally
        final userModel = await _supabaseService.getUserProfile(userId);
        if (userModel != null) {
          await _saveUserToDatabase(userModel);
          _currentUser = userModel;
        }
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  Future<void> _saveUserToDatabase(UserModel user) async {
    final db = await _databaseService.database;
    await db.insert('users', _userToDatabase(user));
  }

  Future<void> _updateUserInDatabase(UserModel user) async {
    final db = await _databaseService.database;
    await db.update(
      'users',
      _userToDatabase(user),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Map<String, dynamic> _userToDatabase(UserModel user) {
    return {
      'id': user.id,
      'email': user.email,
      'display_name': user.displayName,
      'avatar_url': user.avatarUrl,
      'created_at': user.createdAt.toIso8601String(),
      'updated_at': user.updatedAt.toIso8601String(),
      'is_active': user.isActive ? 1 : 0,
      'preferences': user.preferences != null
          ? user.preferences.toString()
          : null,
    };
  }

  UserModel _userFromDatabase(Map<String, dynamic> data) {
    return UserModel(
      id: data['id'],
      email: data['email'],
      displayName: data['display_name'],
      avatarUrl: data['avatar_url'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      isActive: data['is_active'] == 1,
      preferences: data['preferences'] != null
          ? Map<String, dynamic>.from(data['preferences'])
          : null,
    );
  }

  Future<void> _saveUserSession(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.keyUserId, userId);
  }

  Future<void> _clearUserSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.keyUserId);
  }

  void dispose() {
    _authStateController.close();
  }
}

class AuthResult {
  final bool isSuccess;
  final String message;

  AuthResult.success({required this.message}) : isSuccess = true;
  AuthResult.failure({required this.message}) : isSuccess = false;
}
