import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/memo_model.dart';
import 'memo_provider.dart';

// Search query provider
final searchQueryProvider = StateProvider<String>((ref) => '');

// Search filter type provider
enum SearchFilterType { all, text, voice, todo, mixed }

final searchFilterProvider = StateProvider<SearchFilterType>(
  (ref) => SearchFilterType.all,
);

// Sort options provider
enum SortOption { dateNewest, dateOldest, titleAZ, titleZA, priority }

final sortOptionProvider = StateProvider<SortOption>(
  (ref) => SortOption.dateNewest,
);

// Category filter provider
final categoryFilterProvider = StateProvider<String?>((ref) => null);

// Tag filter provider
final tagFilterProvider = StateProvider<String?>((ref) => null);

// Show archived memos provider
final showArchivedProvider = StateProvider<bool>((ref) => false);

// Filtered and sorted memos provider
final searchFilteredMemosProvider = Provider<AsyncValue<List<MemoModel>>>((
  ref,
) {
  final memosAsync = ref.watch(memosProvider);
  final searchQuery = ref.watch(searchQueryProvider);
  final filterType = ref.watch(searchFilterProvider);
  final sortOption = ref.watch(sortOptionProvider);
  final categoryFilter = ref.watch(categoryFilterProvider);
  final tagFilter = ref.watch(tagFilterProvider);
  final showArchived = ref.watch(showArchivedProvider);

  return memosAsync.when(
    data: (memos) {
      var filteredMemos = memos.where((memo) {
        // Archive filter
        if (!showArchived && memo.isArchived) return false;
        if (showArchived && !memo.isArchived) return false;

        // Type filter
        if (filterType != SearchFilterType.all) {
          switch (filterType) {
            case SearchFilterType.text:
              if (memo.type != MemoType.text) return false;
              break;
            case SearchFilterType.voice:
              if (memo.type != MemoType.voice) return false;
              break;
            case SearchFilterType.todo:
              if (memo.type != MemoType.todo) return false;
              break;
            case SearchFilterType.mixed:
              if (memo.type != MemoType.mixed) return false;
              break;
            case SearchFilterType.all:
              break;
          }
        }

        // Category filter
        if (categoryFilter != null && categoryFilter.isNotEmpty) {
          if (memo.category != categoryFilter) return false;
        }

        // Tag filter
        if (tagFilter != null && tagFilter.isNotEmpty) {
          if (!memo.tags.contains(tagFilter)) return false;
        }

        // Search query filter
        if (searchQuery.isNotEmpty) {
          final query = searchQuery.toLowerCase();
          final titleMatch = memo.title.toLowerCase().contains(query);
          final descriptionMatch =
              memo.description?.toLowerCase().contains(query) ?? false;
          final transcriptionMatch =
              memo.transcription?.toLowerCase().contains(query) ?? false;
          final richContentMatch =
              memo.richContent?.toLowerCase().contains(query) ?? false;
          final tagsMatch = memo.tags.any(
            (tag) => tag.toLowerCase().contains(query),
          );
          final todoMatch = memo.todoItems.any(
            (todo) => todo.text.toLowerCase().contains(query),
          );

          if (!titleMatch &&
              !descriptionMatch &&
              !transcriptionMatch &&
              !richContentMatch &&
              !tagsMatch &&
              !todoMatch) {
            return false;
          }
        }

        return true;
      }).toList();

      // Sort memos
      switch (sortOption) {
        case SortOption.dateNewest:
          filteredMemos.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case SortOption.dateOldest:
          filteredMemos.sort((a, b) => a.createdAt.compareTo(b.createdAt));
          break;
        case SortOption.titleAZ:
          filteredMemos.sort((a, b) => a.title.compareTo(b.title));
          break;
        case SortOption.titleZA:
          filteredMemos.sort((a, b) => b.title.compareTo(a.title));
          break;
        case SortOption.priority:
          // Sort by pinned first, then by creation date
          filteredMemos.sort((a, b) {
            if (a.isPinned && !b.isPinned) return -1;
            if (!a.isPinned && b.isPinned) return 1;
            return b.createdAt.compareTo(a.createdAt);
          });
          break;
      }

      return AsyncValue.data(filteredMemos);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Available categories provider
final availableCategoriesProvider = Provider<List<String>>((ref) {
  final memosAsync = ref.watch(memosProvider);
  return memosAsync.when(
    data: (memos) {
      final categories = memos
          .where((memo) => memo.category != null && memo.category!.isNotEmpty)
          .map((memo) => memo.category!)
          .toSet()
          .toList();
      categories.sort();
      return categories;
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Available tags provider
final availableTagsProvider = Provider<List<String>>((ref) {
  final memosAsync = ref.watch(memosProvider);
  return memosAsync.when(
    data: (memos) {
      final tags = <String>{};
      for (final memo in memos) {
        tags.addAll(memo.tags);
      }
      final tagList = tags.toList();
      tagList.sort();
      return tagList;
    },
    loading: () => [],
    error: (_, __) => [],
  );
});
