import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/nexus_profile_provider.dart';
import '../../../core/theme/futuristic_theme.dart';

class ProfileActivityFeed extends ConsumerWidget {
  const ProfileActivityFeed({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activityLogs = ref.watch(activityLogsProvider);
    final isLoading = ref.watch(profileLoadingProvider);

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: FuturisticTheme.primaryBlue.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: FuturisticTheme.primaryBlue.withOpacity(0.1),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: <PERSON>umn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  Icons.timeline,
                  color: FuturisticTheme.primaryBlue,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Recent Activity',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: FuturisticTheme.primaryBlue,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    ref
                        .read(nexusProfileProvider.notifier)
                        .refreshActivityLogs();
                  },
                  icon: Icon(Icons.refresh, color: FuturisticTheme.primaryBlue),
                  tooltip: 'Refresh',
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Activity list
          if (isLoading)
            const Padding(
              padding: EdgeInsets.all(40),
              child: Center(child: CircularProgressIndicator()),
            )
          else if (activityLogs.isEmpty)
            Padding(
              padding: const EdgeInsets.all(40),
              child: Center(
                child: Column(
                  children: [
                    Icon(Icons.timeline, size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'No activity yet',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Your recent activities will appear here',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            Container(
              constraints: const BoxConstraints(maxHeight: 400),
              child: ListView.separated(
                shrinkWrap: true,
                padding: const EdgeInsets.all(16),
                itemCount: activityLogs.length,
                separatorBuilder: (context, index) =>
                    const SizedBox(height: 12),
                itemBuilder: (context, index) {
                  final activity = activityLogs[index];
                  return _buildActivityItem(context, activity);
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    Map<String, dynamic> activity,
  ) {
    final activityType = activity['activity_type'] as String? ?? 'unknown';
    final description =
        activity['description'] as String? ?? 'Unknown activity';
    final createdAt = DateTime.tryParse(
      activity['created_at'] as String? ?? '',
    );
    final entityType = activity['entity_type'] as String?;

    final icon = _getActivityIcon(activityType, entityType);
    final color = _getActivityColor(activityType);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  description,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
                if (createdAt != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    _formatActivityTime(createdAt),
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getActivityIcon(String activityType, String? entityType) {
    switch (activityType.toLowerCase()) {
      case 'insert':
        switch (entityType?.toLowerCase()) {
          case 'memos':
            return Icons.note_add;
          case 'transactions':
            return Icons.payment;
          case 'athkar_sessions':
            return Icons.auto_stories;
          default:
            return Icons.add_circle;
        }
      case 'update':
        return Icons.edit;
      case 'delete':
        return Icons.delete;
      default:
        return Icons.timeline;
    }
  }

  Color _getActivityColor(String activityType) {
    switch (activityType.toLowerCase()) {
      case 'insert':
        return FuturisticTheme.primaryGreen;
      case 'update':
        return FuturisticTheme.primaryBlue;
      case 'delete':
        return Colors.red;
      default:
        return FuturisticTheme.primaryPurple;
    }
  }

  String _formatActivityTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${time.day}/${time.month}/${time.year}';
    }
  }
}
