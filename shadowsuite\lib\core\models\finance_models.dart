import 'package:json_annotation/json_annotation.dart';

part 'finance_models.g.dart';

enum AccountType {
  checking,
  savings,
  credit,
  investment,
  cash,
}

enum TransactionType {
  income,
  expense,
  transfer,
}

enum CategoryType {
  income,
  expense,
}

@JsonSerializable()
class AccountModel {
  final String id;
  final String userId;
  final String name;
  final String? description;
  final AccountType type;
  final double balance;
  final String currency;
  final String? color;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const AccountModel({
    required this.id,
    required this.userId,
    required this.name,
    this.description,
    required this.type,
    this.balance = 0.0,
    this.currency = 'USD',
    this.color,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  factory AccountModel.fromJson(Map<String, dynamic> json) =>
      _$AccountModelFromJson(json);

  Map<String, dynamic> toJson() => _$AccountModelToJson(this);

  AccountModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? description,
    AccountType? type,
    double? balance,
    String? currency,
    String? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return AccountModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }
}

@JsonSerializable()
class CategoryModel {
  final String id;
  final String userId;
  final String name;
  final String? description;
  final CategoryType type;
  final String? parentId;
  final String? color;
  final String? icon;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const CategoryModel({
    required this.id,
    required this.userId,
    required this.name,
    this.description,
    required this.type,
    this.parentId,
    this.color,
    this.icon,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryModelToJson(this);

  CategoryModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? description,
    CategoryType? type,
    String? parentId,
    String? color,
    String? icon,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      parentId: parentId ?? this.parentId,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }
}
