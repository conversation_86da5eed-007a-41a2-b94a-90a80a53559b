import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/transaction_model.dart';
import '../models/account_model.dart';
import '../models/category_model.dart';
import '../providers/money_flow_provider.dart';

class CreateTransactionScreen extends ConsumerStatefulWidget {
  final TransactionModel? editingTransaction;
  final TransactionType? initialType;

  const CreateTransactionScreen({
    super.key,
    this.editingTransaction,
    this.initialType,
  });

  @override
  ConsumerState<CreateTransactionScreen> createState() =>
      _CreateTransactionScreenState();
}

class _CreateTransactionScreenState
    extends ConsumerState<CreateTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _tagsController = TextEditingController();

  TransactionType _selectedType = TransactionType.expense;
  String? _selectedAccountId;
  String? _selectedToAccountId; // For transfers
  String? _selectedCategoryId;
  DateTime _selectedDate = DateTime.now();
  List<String> _tags = [];
  bool _isRecurring = false;
  String? _recurringPattern;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.editingTransaction != null) {
      final transaction = widget.editingTransaction!;
      _titleController.text = transaction.title;
      _amountController.text = transaction.amount.toString();
      _descriptionController.text = transaction.description ?? '';
      _locationController.text = transaction.location ?? '';
      _tagsController.text = transaction.tags.join(', ');
      _selectedType = transaction.type;
      _selectedAccountId = transaction.accountId;
      _selectedToAccountId = transaction.toAccountId;
      _selectedCategoryId = transaction.categoryId;
      _selectedDate = transaction.date;
      _tags = List.from(transaction.tags);
      _isRecurring = transaction.isRecurring;
      _recurringPattern = transaction.recurringPattern;
    } else if (widget.initialType != null) {
      _selectedType = widget.initialType!;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  void _updateTags() {
    final tagsText = _tagsController.text;
    _tags = tagsText
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
  }

  void _saveTransaction() {
    if (_formKey.currentState!.validate()) {
      if (_selectedAccountId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select an account')),
        );
        return;
      }

      if (_selectedCategoryId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select a category')),
        );
        return;
      }

      if (_selectedType == TransactionType.transfer &&
          _selectedToAccountId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select destination account for transfer'),
          ),
        );
        return;
      }

      _updateTags();

      final transaction = TransactionModel(
        id:
            widget.editingTransaction?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        userId: 'demo-user-id',
        accountId: _selectedAccountId!,
        toAccountId: _selectedToAccountId,
        type: _selectedType,
        amount: double.parse(_amountController.text),
        categoryId: _selectedCategoryId!,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        date: _selectedDate,
        tags: _tags,
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        isRecurring: _isRecurring,
        recurringPattern: _recurringPattern,
        createdAt: widget.editingTransaction?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.editingTransaction != null) {
        ref.read(transactionsProvider.notifier).updateTransaction(transaction);
      } else {
        ref.read(transactionsProvider.notifier).addTransaction(transaction);
      }

      Navigator.of(context).pop();
    }
  }

  Widget _buildTypeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Transaction Type',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: TransactionType.values.map((type) {
                final isSelected = _selectedType == type;
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: ChoiceChip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(type.icon),
                          const SizedBox(width: 4),
                          Text(type.displayName),
                        ],
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _selectedType = type;
                            _selectedCategoryId =
                                null; // Reset category when type changes
                            if (type != TransactionType.transfer) {
                              _selectedToAccountId = null;
                            }
                          });
                        }
                      },
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSelector(List<AccountModel> accounts) {
    return Column(
      children: [
        DropdownButtonFormField<String>(
          value: _selectedAccountId,
          decoration: const InputDecoration(
            labelText: 'From Account *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.account_balance_wallet),
          ),
          items: accounts.map((account) {
            return DropdownMenuItem(
              value: account.id,
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Color(account.colorValue),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(child: Text(account.name)),
                  Text(
                    '\$${account.balance.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: account.balance >= 0 ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedAccountId = value;
            });
          },
        ),

        if (_selectedType == TransactionType.transfer) ...[
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedToAccountId,
            decoration: const InputDecoration(
              labelText: 'To Account *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.account_balance),
            ),
            items: accounts
                .where((account) => account.id != _selectedAccountId)
                .map((account) {
                  return DropdownMenuItem(
                    value: account.id,
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: Color(account.colorValue),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(account.name)),
                        Text(
                          '\$${account.balance.toStringAsFixed(2)}',
                          style: TextStyle(
                            color: account.balance >= 0
                                ? Colors.green
                                : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                })
                .toList(),
            onChanged: (value) {
              setState(() {
                _selectedToAccountId = value;
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildCategorySelector(List<CategoryModel> categories) {
    final filteredCategories = categories.where((category) {
      if (_selectedType == TransactionType.transfer) return true;
      return (_selectedType == TransactionType.income &&
              category.type == CategoryType.income) ||
          (_selectedType == TransactionType.expense &&
              category.type == CategoryType.expense);
    }).toList();

    return DropdownButtonFormField<String>(
      value: _selectedCategoryId,
      decoration: const InputDecoration(
        labelText: 'Category *',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.category),
      ),
      items: filteredCategories.map((category) {
        return DropdownMenuItem(
          value: category.id,
          child: Row(
            children: [
              Text(category.icon),
              const SizedBox(width: 8),
              Expanded(child: Text(category.name)),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCategoryId = value;
        });
      },
    );
  }

  Widget _buildDateSelector() {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: _selectedDate,
          firstDate: DateTime(2020),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (date != null) {
          setState(() {
            _selectedDate = date;
          });
        }
      },
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'Date',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(
          '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final accountsAsync = ref.watch(accountsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.editingTransaction != null
              ? 'Edit Transaction'
              : 'Add Transaction',
        ),
        actions: [
          IconButton(icon: const Icon(Icons.save), onPressed: _saveTransaction),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Transaction type selector
              _buildTypeSelector(),
              const SizedBox(height: 24),

              // Amount and title
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      controller: _amountController,
                      decoration: const InputDecoration(
                        labelText: 'Amount *',
                        border: OutlineInputBorder(),
                        prefixText: '\$ ',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}'),
                        ),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter amount';
                        }
                        final amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return 'Please enter valid amount';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 3,
                    child: TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title *',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter title';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Account selection
              accountsAsync.when(
                data: (accounts) => _buildAccountSelector(accounts),
                loading: () => const CircularProgressIndicator(),
                error: (_, __) => const Text('Error loading accounts'),
              ),
              const SizedBox(height: 16),

              // Category selection
              categoriesAsync.when(
                data: (categories) => _buildCategorySelector(categories),
                loading: () => const CircularProgressIndicator(),
                error: (_, __) => const Text('Error loading categories'),
              ),
              const SizedBox(height: 16),

              // Date selection
              _buildDateSelector(),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // Location
              TextFormField(
                controller: _locationController,
                decoration: const InputDecoration(
                  labelText: 'Location (Optional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_on),
                ),
              ),
              const SizedBox(height: 16),

              // Tags
              TextFormField(
                controller: _tagsController,
                decoration: const InputDecoration(
                  labelText: 'Tags (Optional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.tag),
                  hintText: 'Separate tags with commas',
                ),
                onChanged: (value) => _updateTags(),
              ),
              const SizedBox(height: 16),

              // Tags preview
              if (_tags.isNotEmpty) ...[
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _tags
                      .map(
                        (tag) => Chip(
                          label: Text(tag),
                          backgroundColor: Colors.blue.withValues(alpha: 0.1),
                          labelStyle: TextStyle(color: Colors.blue[700]),
                          deleteIcon: const Icon(Icons.close, size: 16),
                          onDeleted: () {
                            setState(() {
                              _tags.remove(tag);
                              _tagsController.text = _tags.join(', ');
                            });
                          },
                        ),
                      )
                      .toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Recurring transaction
              Card(
                child: SwitchListTile(
                  title: const Text('Recurring Transaction'),
                  subtitle: const Text(
                    'Set up automatic recurring transactions',
                  ),
                  value: _isRecurring,
                  onChanged: (value) {
                    setState(() {
                      _isRecurring = value;
                      if (!value) {
                        _recurringPattern = null;
                      }
                    });
                  },
                ),
              ),

              if (_isRecurring) ...[
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _recurringPattern,
                  decoration: const InputDecoration(
                    labelText: 'Recurring Pattern',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'daily', child: Text('Daily')),
                    DropdownMenuItem(value: 'weekly', child: Text('Weekly')),
                    DropdownMenuItem(value: 'monthly', child: Text('Monthly')),
                    DropdownMenuItem(value: 'yearly', child: Text('Yearly')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _recurringPattern = value;
                    });
                  },
                ),
              ],

              const SizedBox(height: 32),

              // Save button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _saveTransaction,
                  icon: const Icon(Icons.save),
                  label: Text(
                    widget.editingTransaction != null
                        ? 'Update Transaction'
                        : 'Save Transaction',
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
