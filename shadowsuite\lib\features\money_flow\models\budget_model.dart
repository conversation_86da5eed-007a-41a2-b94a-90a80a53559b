enum BudgetPeriod {
  weekly,
  monthly,
  quarterly,
  yearly,
}

extension BudgetPeriodExtension on BudgetPeriod {
  String get displayName {
    switch (this) {
      case BudgetPeriod.weekly:
        return 'Weekly';
      case BudgetPeriod.monthly:
        return 'Monthly';
      case BudgetPeriod.quarterly:
        return 'Quarterly';
      case BudgetPeriod.yearly:
        return 'Yearly';
    }
  }

  int get daysCount {
    switch (this) {
      case BudgetPeriod.weekly:
        return 7;
      case BudgetPeriod.monthly:
        return 30;
      case BudgetPeriod.quarterly:
        return 90;
      case BudgetPeriod.yearly:
        return 365;
    }
  }
}

class BudgetModel {
  final String id;
  final String userId;
  final String name;
  final String? categoryId;
  final double amount;
  final double spent;
  final String currency;
  final BudgetPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final bool alertEnabled;
  final double alertThreshold; // Percentage (0.0 to 1.0)
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSynced;

  const BudgetModel({
    required this.id,
    required this.userId,
    required this.name,
    this.categoryId,
    required this.amount,
    this.spent = 0.0,
    this.currency = 'USD',
    required this.period,
    required this.startDate,
    required this.endDate,
    this.isActive = true,
    this.alertEnabled = true,
    this.alertThreshold = 0.8,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.isSynced = false,
  });

  BudgetModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? categoryId,
    double? amount,
    double? spent,
    String? currency,
    BudgetPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    bool? alertEnabled,
    double? alertThreshold,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
  }) {
    return BudgetModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
      amount: amount ?? this.amount,
      spent: spent ?? this.spent,
      currency: currency ?? this.currency,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      alertEnabled: alertEnabled ?? this.alertEnabled,
      alertThreshold: alertThreshold ?? this.alertThreshold,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  double get remaining => amount - spent;
  double get progress => amount > 0 ? spent / amount : 0.0;
  bool get isOverBudget => spent > amount;
  bool get isNearLimit => progress >= alertThreshold;
  
  int get daysRemaining {
    final now = DateTime.now();
    if (now.isAfter(endDate)) return 0;
    return endDate.difference(now).inDays;
  }

  double get dailyBudget {
    final totalDays = endDate.difference(startDate).inDays;
    return totalDays > 0 ? amount / totalDays : 0.0;
  }

  double get dailySpent {
    final daysPassed = DateTime.now().difference(startDate).inDays + 1;
    return daysPassed > 0 ? spent / daysPassed : 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'category_id': categoryId,
      'amount': amount,
      'spent': spent,
      'currency': currency,
      'period': period.name,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'is_active': isActive,
      'alert_enabled': alertEnabled,
      'alert_threshold': alertThreshold,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_synced': isSynced,
    };
  }

  factory BudgetModel.fromJson(Map<String, dynamic> json) {
    return BudgetModel(
      id: json['id'],
      userId: json['user_id'],
      name: json['name'],
      categoryId: json['category_id'],
      amount: (json['amount'] as num).toDouble(),
      spent: (json['spent'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'USD',
      period: BudgetPeriod.values.firstWhere(
        (e) => e.name == json['period'],
        orElse: () => BudgetPeriod.monthly,
      ),
      startDate: DateTime.parse(json['start_date']),
      endDate: DateTime.parse(json['end_date']),
      isActive: json['is_active'] ?? true,
      alertEnabled: json['alert_enabled'] ?? true,
      alertThreshold: (json['alert_threshold'] as num?)?.toDouble() ?? 0.8,
      description: json['description'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      isSynced: json['is_synced'] ?? false,
    );
  }

  @override
  String toString() {
    return 'BudgetModel(id: $id, name: $name, amount: $amount, spent: $spent)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BudgetModel &&
        other.id == id &&
        other.name == name &&
        other.amount == amount;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ amount.hashCode;
  }
}
