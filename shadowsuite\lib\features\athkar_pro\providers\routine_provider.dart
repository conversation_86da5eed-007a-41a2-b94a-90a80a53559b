import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/routine_model.dart';
import '../models/dhikr_model.dart';

// Routines provider
final routinesProvider = StateNotifierProvider<RoutinesNotifier, AsyncValue<List<RoutineModel>>>((ref) {
  return RoutinesNotifier();
});

// Active routine session provider
final activeRoutineSessionProvider = StateNotifierProvider<ActiveRoutineSessionNotifier, RoutineSession?>((ref) {
  return ActiveRoutineSessionNotifier();
});

// Routine templates provider
final routineTemplatesProvider = StateNotifierProvider<RoutineTemplatesNotifier, AsyncValue<List<RoutineModel>>>((ref) {
  return RoutineTemplatesNotifier();
});

// Filtered routines provider
final filteredRoutinesProvider = Provider.family<List<RoutineModel>, RoutineFilter>((ref, filter) {
  final routinesAsync = ref.watch(routinesProvider);
  
  return routinesAsync.when(
    data: (routines) {
      var filtered = routines.where((routine) {
        // Type filter
        if (filter.type != null && routine.type != filter.type) {
          return false;
        }
        
        // Category filter
        if (filter.category != null && routine.category != filter.category) {
          return false;
        }
        
        // Active filter
        if (filter.activeOnly && !routine.isActive) {
          return false;
        }
        
        // Favorite filter
        if (filter.favoritesOnly && !routine.isFavorite) {
          return false;
        }
        
        // Search filter
        if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
          final query = filter.searchQuery!.toLowerCase();
          if (!routine.name.toLowerCase().contains(query) &&
              !(routine.description?.toLowerCase().contains(query) ?? false) &&
              !routine.tags.any((tag) => tag.toLowerCase().contains(query))) {
            return false;
          }
        }
        
        return true;
      }).toList();
      
      // Sort by last updated (newest first)
      filtered.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      
      return filtered;
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Routine statistics provider
final routineStatsProvider = Provider<RoutineStatistics>((ref) {
  final routinesAsync = ref.watch(routinesProvider);
  
  return routinesAsync.when(
    data: (routines) {
      final activeRoutines = routines.where((r) => r.isActive).length;
      final completedToday = routines.where((r) => 
        r.lastCompletedAt != null && 
        _isSameDay(r.lastCompletedAt!, DateTime.now())
      ).length;
      final totalRoutines = routines.length;
      final favoriteRoutines = routines.where((r) => r.isFavorite).length;
      
      return RoutineStatistics(
        totalRoutines: totalRoutines,
        activeRoutines: activeRoutines,
        completedToday: completedToday,
        favoriteRoutines: favoriteRoutines,
      );
    },
    loading: () => const RoutineStatistics(
      totalRoutines: 0,
      activeRoutines: 0,
      completedToday: 0,
      favoriteRoutines: 0,
    ),
    error: (_, __) => const RoutineStatistics(
      totalRoutines: 0,
      activeRoutines: 0,
      completedToday: 0,
      favoriteRoutines: 0,
    ),
  );
});

class RoutinesNotifier extends StateNotifier<AsyncValue<List<RoutineModel>>> {
  RoutinesNotifier() : super(const AsyncValue.loading()) {
    loadRoutines();
  }

  Future<void> loadRoutines() async {
    try {
      state = const AsyncValue.loading();
      
      // Load sample routines for demo
      final routines = _getSampleRoutines();
      
      state = AsyncValue.data(routines);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addRoutine(RoutineModel routine) async {
    state.whenData((routines) {
      state = AsyncValue.data([routine, ...routines]);
    });
  }

  Future<void> updateRoutine(RoutineModel updatedRoutine) async {
    state.whenData((routines) {
      final updatedList = routines.map((routine) {
        return routine.id == updatedRoutine.id ? updatedRoutine : routine;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> deleteRoutine(String routineId) async {
    state.whenData((routines) {
      final updatedList = routines.where((routine) => routine.id != routineId).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> toggleFavorite(String routineId) async {
    state.whenData((routines) {
      final updatedList = routines.map((routine) {
        if (routine.id == routineId) {
          return routine.copyWith(
            isFavorite: !routine.isFavorite,
            updatedAt: DateTime.now(),
          );
        }
        return routine;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> incrementStepCount(String routineId, String stepId) async {
    state.whenData((routines) {
      final updatedList = routines.map((routine) {
        if (routine.id == routineId) {
          final updatedSteps = routine.steps.map((step) {
            if (step.id == stepId && step.currentRepetitions < step.targetRepetitions) {
              return step.copyWith(currentRepetitions: step.currentRepetitions + 1);
            }
            return step;
          }).toList();
          
          final totalCurrent = updatedSteps.fold<int>(0, (sum, step) => sum + step.currentRepetitions);
          
          return routine.copyWith(
            steps: updatedSteps,
            currentRepetitions: totalCurrent,
            updatedAt: DateTime.now(),
            lastCompletedAt: totalCurrent >= routine.totalRepetitions ? DateTime.now() : routine.lastCompletedAt,
          );
        }
        return routine;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> resetRoutine(String routineId) async {
    state.whenData((routines) {
      final updatedList = routines.map((routine) {
        if (routine.id == routineId) {
          final resetSteps = routine.steps.map((step) => step.copyWith(currentRepetitions: 0)).toList();
          return routine.copyWith(
            steps: resetSteps,
            currentRepetitions: 0,
            updatedAt: DateTime.now(),
          );
        }
        return routine;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  List<RoutineModel> _getSampleRoutines() {
    final now = DateTime.now();
    const userId = 'demo-user-id';
    
    return [
      RoutineModel(
        id: 'routine_morning',
        userId: userId,
        name: 'Morning Athkar',
        description: 'Essential morning remembrance routine',
        type: RoutineType.preset,
        category: DhikrCategory.morning,
        steps: [
          RoutineStep(
            id: 'step_1',
            arabicText: 'سُبْحَانَ اللَّهِ',
            transliteration: 'Subhan Allah',
            translation: 'Glory be to Allah',
            targetRepetitions: 33,
            currentRepetitions: 15,
            order: 1,
            tags: ['tasbih', 'morning'],
          ),
          RoutineStep(
            id: 'step_2',
            arabicText: 'الْحَمْدُ لِلَّهِ',
            transliteration: 'Alhamdulillah',
            translation: 'Praise be to Allah',
            targetRepetitions: 33,
            currentRepetitions: 0,
            order: 2,
            tags: ['hamd', 'morning'],
          ),
          RoutineStep(
            id: 'step_3',
            arabicText: 'اللَّهُ أَكْبَرُ',
            transliteration: 'Allahu Akbar',
            translation: 'Allah is the Greatest',
            targetRepetitions: 34,
            currentRepetitions: 0,
            order: 3,
            tags: ['takbir', 'morning'],
          ),
        ],
        schedule: RoutineSchedule(
          type: RoutineScheduleType.daily,
          reminderTimes: ['06:00'],
        ),
        totalRepetitions: 100,
        currentRepetitions: 15,
        isActive: true,
        isFavorite: true,
        tags: ['morning', 'daily', 'essential'],
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now,
      ),
      RoutineModel(
        id: 'routine_evening',
        userId: userId,
        name: 'Evening Protection',
        description: 'Evening athkar for protection and gratitude',
        type: RoutineType.preset,
        category: DhikrCategory.evening,
        steps: [
          RoutineStep(
            id: 'step_4',
            arabicText: 'أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ',
            transliteration: 'A\'udhu billahi min ash-shaytani\'r-rajim',
            translation: 'I seek refuge in Allah from Satan, the accursed',
            targetRepetitions: 3,
            currentRepetitions: 0,
            order: 1,
            tags: ['protection', 'evening'],
          ),
          RoutineStep(
            id: 'step_5',
            arabicText: 'أَسْتَغْفِرُ اللَّهَ',
            transliteration: 'Astaghfirullah',
            translation: 'I seek forgiveness from Allah',
            targetRepetitions: 100,
            currentRepetitions: 0,
            order: 2,
            tags: ['istighfar', 'evening'],
          ),
        ],
        schedule: RoutineSchedule(
          type: RoutineScheduleType.daily,
          reminderTimes: ['18:00'],
        ),
        totalRepetitions: 103,
        currentRepetitions: 0,
        isActive: true,
        isFavorite: false,
        tags: ['evening', 'protection', 'daily'],
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
    ];
  }
}

class ActiveRoutineSessionNotifier extends StateNotifier<RoutineSession?> {
  ActiveRoutineSessionNotifier() : super(null);

  void startSession(RoutineModel routine) {
    state = RoutineSession(
      routineId: routine.id,
      routineName: routine.name,
      startTime: DateTime.now(),
      currentStepIndex: 0,
      stepProgress: {},
    );
  }

  void updateStepProgress(String stepId, int count) {
    if (state != null) {
      final updatedProgress = Map<String, int>.from(state!.stepProgress);
      updatedProgress[stepId] = count;
      
      state = state!.copyWith(stepProgress: updatedProgress);
    }
  }

  void nextStep() {
    if (state != null) {
      state = state!.copyWith(currentStepIndex: state!.currentStepIndex + 1);
    }
  }

  void endSession() {
    state = null;
  }
}

class RoutineTemplatesNotifier extends StateNotifier<AsyncValue<List<RoutineModel>>> {
  RoutineTemplatesNotifier() : super(const AsyncValue.loading()) {
    loadTemplates();
  }

  Future<void> loadTemplates() async {
    try {
      state = const AsyncValue.loading();
      
      // Load routine templates
      final templates = _getRoutineTemplates();
      
      state = AsyncValue.data(templates);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  List<RoutineModel> _getRoutineTemplates() {
    final now = DateTime.now();
    const userId = 'template';
    
    return [
      RoutineModel(
        id: 'template_basic_tasbih',
        userId: userId,
        name: 'Basic Tasbih',
        description: 'Simple tasbih routine for beginners',
        type: RoutineType.template,
        category: DhikrCategory.general,
        steps: [
          RoutineStep(
            id: 'template_step_1',
            arabicText: 'سُبْحَانَ اللَّهِ',
            transliteration: 'Subhan Allah',
            translation: 'Glory be to Allah',
            targetRepetitions: 33,
            order: 1,
          ),
          RoutineStep(
            id: 'template_step_2',
            arabicText: 'الْحَمْدُ لِلَّهِ',
            transliteration: 'Alhamdulillah',
            translation: 'Praise be to Allah',
            targetRepetitions: 33,
            order: 2,
          ),
          RoutineStep(
            id: 'template_step_3',
            arabicText: 'اللَّهُ أَكْبَرُ',
            transliteration: 'Allahu Akbar',
            translation: 'Allah is the Greatest',
            targetRepetitions: 34,
            order: 3,
          ),
        ],
        totalRepetitions: 100,
        tags: ['template', 'basic', 'tasbih'],
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}

// Helper classes
class RoutineFilter {
  final RoutineType? type;
  final DhikrCategory? category;
  final bool activeOnly;
  final bool favoritesOnly;
  final String? searchQuery;

  const RoutineFilter({
    this.type,
    this.category,
    this.activeOnly = false,
    this.favoritesOnly = false,
    this.searchQuery,
  });
}

class RoutineStatistics {
  final int totalRoutines;
  final int activeRoutines;
  final int completedToday;
  final int favoriteRoutines;

  const RoutineStatistics({
    required this.totalRoutines,
    required this.activeRoutines,
    required this.completedToday,
    required this.favoriteRoutines,
  });
}

class RoutineSession {
  final String routineId;
  final String routineName;
  final DateTime startTime;
  final int currentStepIndex;
  final Map<String, int> stepProgress;

  const RoutineSession({
    required this.routineId,
    required this.routineName,
    required this.startTime,
    required this.currentStepIndex,
    required this.stepProgress,
  });

  RoutineSession copyWith({
    String? routineId,
    String? routineName,
    DateTime? startTime,
    int? currentStepIndex,
    Map<String, int>? stepProgress,
  }) {
    return RoutineSession(
      routineId: routineId ?? this.routineId,
      routineName: routineName ?? this.routineName,
      startTime: startTime ?? this.startTime,
      currentStepIndex: currentStepIndex ?? this.currentStepIndex,
      stepProgress: stepProgress ?? this.stepProgress,
    );
  }
}

bool _isSameDay(DateTime date1, DateTime date2) {
  return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
}
