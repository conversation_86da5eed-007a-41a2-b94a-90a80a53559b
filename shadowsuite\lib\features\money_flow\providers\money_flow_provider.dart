import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/account_model.dart';
import '../models/transaction_model.dart';
import '../models/category_model.dart';
import '../models/budget_model.dart';
import '../models/recurring_transaction_model.dart';

// Accounts provider
final accountsProvider =
    StateNotifierProvider<AccountsNotifier, AsyncValue<List<AccountModel>>>((
      ref,
    ) {
      return AccountsNotifier();
    });

// Transactions provider
final transactionsProvider =
    StateNotifierProvider<
      TransactionsNotifier,
      AsyncValue<List<TransactionModel>>
    >((ref) {
      return TransactionsNotifier();
    });

// Categories provider
final categoriesProvider =
    StateNotifierProvider<CategoriesNotifier, AsyncValue<List<CategoryModel>>>((
      ref,
    ) {
      return CategoriesNotifier();
    });

// Budgets provider
final budgetsProvider =
    StateNotifierProvider<BudgetsNotifier, AsyncValue<List<BudgetModel>>>((
      ref,
    ) {
      return BudgetsNotifier();
    });

// Recurring transactions provider
final recurringTransactionsProvider =
    StateNotifierProvider<
      RecurringTransactionsNotifier,
      AsyncValue<List<RecurringTransactionModel>>
    >((ref) {
      return RecurringTransactionsNotifier();
    });

// Filtered transactions provider
final filteredTransactionsProvider =
    Provider.family<List<TransactionModel>, TransactionFilter>((ref, filter) {
      final transactionsAsync = ref.watch(transactionsProvider);

      return transactionsAsync.when(
        data: (transactions) {
          var filtered = transactions.where((transaction) {
            // Date filter
            if (filter.startDate != null &&
                transaction.date.isBefore(filter.startDate!)) {
              return false;
            }
            if (filter.endDate != null &&
                transaction.date.isAfter(filter.endDate!)) {
              return false;
            }

            // Type filter
            if (filter.type != null && transaction.type != filter.type) {
              return false;
            }

            // Category filter
            if (filter.categoryId != null &&
                transaction.categoryId != filter.categoryId) {
              return false;
            }

            // Account filter
            if (filter.accountId != null &&
                transaction.accountId != filter.accountId) {
              return false;
            }

            // Search filter
            if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
              final query = filter.searchQuery!.toLowerCase();
              if (!transaction.title.toLowerCase().contains(query) &&
                  !(transaction.description?.toLowerCase().contains(query) ??
                      false)) {
                return false;
              }
            }

            return true;
          }).toList();

          // Sort by date (newest first)
          filtered.sort((a, b) => b.date.compareTo(a.date));

          return filtered;
        },
        loading: () => [],
        error: (_, __) => [],
      );
    });

// Financial summary provider
final financialSummaryProvider = Provider<FinancialSummary>((ref) {
  final accountsAsync = ref.watch(accountsProvider);
  final transactionsAsync = ref.watch(transactionsProvider);

  return accountsAsync.when(
    data: (accounts) => transactionsAsync.when(
      data: (transactions) {
        final totalBalance = accounts.fold<double>(
          0,
          (sum, account) => sum + account.balance,
        );

        final now = DateTime.now();
        final startOfMonth = DateTime(now.year, now.month, 1);
        final endOfMonth = DateTime(now.year, now.month + 1, 0);

        final monthlyTransactions = transactions
            .where(
              (t) =>
                  t.date.isAfter(startOfMonth) &&
                  t.date.isBefore(endOfMonth.add(const Duration(days: 1))),
            )
            .toList();

        final monthlyIncome = monthlyTransactions
            .where((t) => t.type == TransactionType.income)
            .fold<double>(0, (sum, t) => sum + t.amount);

        final monthlyExpenses = monthlyTransactions
            .where((t) => t.type == TransactionType.expense)
            .fold<double>(0, (sum, t) => sum + t.amount);

        return FinancialSummary(
          totalBalance: totalBalance,
          monthlyIncome: monthlyIncome,
          monthlyExpenses: monthlyExpenses,
          accountsCount: accounts.length,
          transactionsCount: transactions.length,
        );
      },
      loading: () => const FinancialSummary(
        totalBalance: 0,
        monthlyIncome: 0,
        monthlyExpenses: 0,
        accountsCount: 0,
        transactionsCount: 0,
      ),
      error: (_, __) => const FinancialSummary(
        totalBalance: 0,
        monthlyIncome: 0,
        monthlyExpenses: 0,
        accountsCount: 0,
        transactionsCount: 0,
      ),
    ),
    loading: () => const FinancialSummary(
      totalBalance: 0,
      monthlyIncome: 0,
      monthlyExpenses: 0,
      accountsCount: 0,
      transactionsCount: 0,
    ),
    error: (_, __) => const FinancialSummary(
      totalBalance: 0,
      monthlyIncome: 0,
      monthlyExpenses: 0,
      accountsCount: 0,
      transactionsCount: 0,
    ),
  );
});

class AccountsNotifier extends StateNotifier<AsyncValue<List<AccountModel>>> {
  AccountsNotifier() : super(const AsyncValue.loading()) {
    loadAccounts();
  }

  Future<void> loadAccounts() async {
    try {
      state = const AsyncValue.loading();

      // Load default accounts for demo
      final accounts = _getDefaultAccounts();

      state = AsyncValue.data(accounts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addAccount(AccountModel account) async {
    state.whenData((accounts) {
      state = AsyncValue.data([...accounts, account]);
    });
  }

  Future<void> updateAccount(AccountModel updatedAccount) async {
    state.whenData((accounts) {
      final updatedList = accounts.map((account) {
        return account.id == updatedAccount.id ? updatedAccount : account;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> deleteAccount(String accountId) async {
    state.whenData((accounts) {
      final updatedList = accounts
          .where((account) => account.id != accountId)
          .toList();
      state = AsyncValue.data(updatedList);
    });
  }

  List<AccountModel> _getDefaultAccounts() {
    final now = DateTime.now();
    const userId = 'demo-user-id';

    return [
      AccountModel(
        id: 'acc_checking',
        userId: userId,
        name: 'Main Checking',
        type: AccountType.checking,
        balance: 2500.00,
        colorValue: 0xFF2196F3,
        description: 'Primary checking account',
        createdAt: now,
        updatedAt: now,
      ),
      AccountModel(
        id: 'acc_savings',
        userId: userId,
        name: 'Emergency Fund',
        type: AccountType.savings,
        balance: 10000.00,
        colorValue: 0xFF4CAF50,
        description: 'Emergency savings account',
        createdAt: now,
        updatedAt: now,
      ),
      AccountModel(
        id: 'acc_credit',
        userId: userId,
        name: 'Credit Card',
        type: AccountType.credit,
        balance: -850.00,
        colorValue: 0xFFFF5722,
        description: 'Main credit card',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}

class TransactionsNotifier
    extends StateNotifier<AsyncValue<List<TransactionModel>>> {
  TransactionsNotifier() : super(const AsyncValue.loading()) {
    loadTransactions();
  }

  Future<void> loadTransactions() async {
    try {
      state = const AsyncValue.loading();

      // Load sample transactions for demo
      final transactions = _getSampleTransactions();

      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addTransaction(TransactionModel transaction) async {
    state.whenData((transactions) {
      state = AsyncValue.data([transaction, ...transactions]);
    });
  }

  Future<void> updateTransaction(TransactionModel updatedTransaction) async {
    state.whenData((transactions) {
      final updatedList = transactions.map((transaction) {
        return transaction.id == updatedTransaction.id
            ? updatedTransaction
            : transaction;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> deleteTransaction(String transactionId) async {
    state.whenData((transactions) {
      final updatedList = transactions
          .where((transaction) => transaction.id != transactionId)
          .toList();
      state = AsyncValue.data(updatedList);
    });
  }

  List<TransactionModel> _getSampleTransactions() {
    final now = DateTime.now();
    const userId = 'demo-user-id';

    return [
      TransactionModel(
        id: 'txn_1',
        userId: userId,
        accountId: 'acc_checking',
        type: TransactionType.income,
        amount: 3500.00,
        categoryId: 'inc_salary',
        title: 'Monthly Salary',
        description: 'Salary payment for ${now.month}/${now.year}',
        date: DateTime(now.year, now.month, 1),
        createdAt: now,
        updatedAt: now,
      ),
      TransactionModel(
        id: 'txn_2',
        userId: userId,
        accountId: 'acc_checking',
        type: TransactionType.expense,
        amount: 45.50,
        categoryId: 'exp_food',
        title: 'Grocery Shopping',
        description: 'Weekly groceries',
        date: now.subtract(const Duration(days: 2)),
        tags: ['groceries', 'food'],
        createdAt: now,
        updatedAt: now,
      ),
      TransactionModel(
        id: 'txn_3',
        userId: userId,
        accountId: 'acc_credit',
        type: TransactionType.expense,
        amount: 25.00,
        categoryId: 'exp_transport',
        title: 'Gas Station',
        description: 'Fuel for car',
        date: now.subtract(const Duration(days: 3)),
        tags: ['gas', 'transport'],
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}

class CategoriesNotifier
    extends StateNotifier<AsyncValue<List<CategoryModel>>> {
  CategoriesNotifier() : super(const AsyncValue.loading()) {
    loadCategories();
  }

  Future<void> loadCategories() async {
    try {
      state = const AsyncValue.loading();

      const userId = 'demo-user-id';
      final categories = [
        ...DefaultCategories.getDefaultExpenseCategories(userId),
        ...DefaultCategories.getDefaultIncomeCategories(userId),
      ];

      state = AsyncValue.data(categories);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addCategory(CategoryModel category) async {
    state.whenData((categories) {
      state = AsyncValue.data([...categories, category]);
    });
  }

  Future<void> updateCategory(CategoryModel updatedCategory) async {
    state.whenData((categories) {
      final updatedList = categories.map((category) {
        return category.id == updatedCategory.id ? updatedCategory : category;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> deleteCategory(String categoryId) async {
    state.whenData((categories) {
      final updatedList = categories
          .where((category) => category.id != categoryId)
          .toList();
      state = AsyncValue.data(updatedList);
    });
  }
}

class BudgetsNotifier extends StateNotifier<AsyncValue<List<BudgetModel>>> {
  BudgetsNotifier() : super(const AsyncValue.loading()) {
    loadBudgets();
  }

  Future<void> loadBudgets() async {
    try {
      state = const AsyncValue.loading();

      // Load sample budgets for demo
      final budgets = _getSampleBudgets();

      state = AsyncValue.data(budgets);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addBudget(BudgetModel budget) async {
    state.whenData((budgets) {
      state = AsyncValue.data([...budgets, budget]);
    });
  }

  Future<void> updateBudget(BudgetModel updatedBudget) async {
    state.whenData((budgets) {
      final updatedList = budgets.map((budget) {
        return budget.id == updatedBudget.id ? updatedBudget : budget;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> deleteBudget(String budgetId) async {
    state.whenData((budgets) {
      final updatedList = budgets
          .where((budget) => budget.id != budgetId)
          .toList();
      state = AsyncValue.data(updatedList);
    });
  }

  List<BudgetModel> _getSampleBudgets() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    const userId = 'demo-user-id';

    return [
      BudgetModel(
        id: 'budget_food',
        userId: userId,
        name: 'Food & Dining',
        categoryId: 'exp_food',
        amount: 500.00,
        spent: 245.50,
        period: BudgetPeriod.monthly,
        startDate: startOfMonth,
        endDate: endOfMonth,
        createdAt: now,
        updatedAt: now,
      ),
      BudgetModel(
        id: 'budget_transport',
        userId: userId,
        name: 'Transportation',
        categoryId: 'exp_transport',
        amount: 200.00,
        spent: 125.00,
        period: BudgetPeriod.monthly,
        startDate: startOfMonth,
        endDate: endOfMonth,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}

class RecurringTransactionsNotifier
    extends StateNotifier<AsyncValue<List<RecurringTransactionModel>>> {
  RecurringTransactionsNotifier() : super(const AsyncValue.loading()) {
    loadRecurringTransactions();
  }

  Future<void> loadRecurringTransactions() async {
    try {
      state = const AsyncValue.loading();

      // Load sample recurring transactions for demo
      final recurringTransactions = _getSampleRecurringTransactions();

      state = AsyncValue.data(recurringTransactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addRecurringTransaction(
    RecurringTransactionModel recurringTransaction,
  ) async {
    state.whenData((recurringTransactions) {
      state = AsyncValue.data([...recurringTransactions, recurringTransaction]);
    });
  }

  Future<void> updateRecurringTransaction(
    RecurringTransactionModel updatedRecurringTransaction,
  ) async {
    state.whenData((recurringTransactions) {
      final updatedList = recurringTransactions.map((recurringTransaction) {
        return recurringTransaction.id == updatedRecurringTransaction.id
            ? updatedRecurringTransaction
            : recurringTransaction;
      }).toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> deleteRecurringTransaction(String recurringTransactionId) async {
    state.whenData((recurringTransactions) {
      final updatedList = recurringTransactions
          .where(
            (recurringTransaction) =>
                recurringTransaction.id != recurringTransactionId,
          )
          .toList();
      state = AsyncValue.data(updatedList);
    });
  }

  Future<void> executeRecurringTransaction(
    String recurringTransactionId,
  ) async {
    state.whenData((recurringTransactions) {
      final recurringTransaction = recurringTransactions.firstWhere(
        (rt) => rt.id == recurringTransactionId,
      );

      final now = DateTime.now();
      final updatedRecurringTransaction = recurringTransaction.copyWith(
        lastExecuted: now,
        nextDue: recurringTransaction.calculateNextDue(),
        executionCount: recurringTransaction.executionCount + 1,
        updatedAt: now,
      );

      final updatedList = recurringTransactions.map((rt) {
        return rt.id == recurringTransactionId
            ? updatedRecurringTransaction
            : rt;
      }).toList();

      state = AsyncValue.data(updatedList);
    });
  }

  List<RecurringTransactionModel> _getSampleRecurringTransactions() {
    final now = DateTime.now();
    const userId = 'demo-user-id';

    return [
      RecurringTransactionModel(
        id: 'rec_salary',
        userId: userId,
        accountId: 'acc_checking',
        type: TransactionType.income,
        amount: 3500.00,
        categoryId: 'inc_salary',
        title: 'Monthly Salary',
        description: 'Regular monthly salary payment',
        frequency: RecurrenceFrequency.monthly,
        startDate: DateTime(now.year, now.month, 1),
        nextDue: DateTime(now.year, now.month + 1, 1),
        autoExecute: true,
        createdAt: now,
        updatedAt: now,
      ),
      RecurringTransactionModel(
        id: 'rec_rent',
        userId: userId,
        accountId: 'acc_checking',
        type: TransactionType.expense,
        amount: 1200.00,
        categoryId: 'exp_bills',
        title: 'Monthly Rent',
        description: 'Apartment rent payment',
        frequency: RecurrenceFrequency.monthly,
        startDate: DateTime(now.year, now.month, 1),
        nextDue: DateTime(now.year, now.month + 1, 1),
        autoExecute: false,
        createdAt: now,
        updatedAt: now,
      ),
      RecurringTransactionModel(
        id: 'rec_coffee',
        userId: userId,
        accountId: 'acc_checking',
        type: TransactionType.expense,
        amount: 5.50,
        categoryId: 'exp_food',
        title: 'Daily Coffee',
        description: 'Morning coffee purchase',
        frequency: RecurrenceFrequency.daily,
        startDate: now,
        nextDue: now.add(const Duration(days: 1)),
        autoExecute: false,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}

// Helper classes
class TransactionFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final TransactionType? type;
  final String? categoryId;
  final String? accountId;
  final String? searchQuery;

  const TransactionFilter({
    this.startDate,
    this.endDate,
    this.type,
    this.categoryId,
    this.accountId,
    this.searchQuery,
  });
}

class FinancialSummary {
  final double totalBalance;
  final double monthlyIncome;
  final double monthlyExpenses;
  final int accountsCount;
  final int transactionsCount;

  const FinancialSummary({
    required this.totalBalance,
    required this.monthlyIncome,
    required this.monthlyExpenses,
    required this.accountsCount,
    required this.transactionsCount,
  });

  double get monthlyNet => monthlyIncome - monthlyExpenses;
  double get savingsRate =>
      monthlyIncome > 0 ? (monthlyNet / monthlyIncome) : 0.0;
}
