// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AthkarRoutineModel _$AthkarRoutineModelFromJson(Map<String, dynamic> json) =>
    AthkarRoutineModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      steps:
          (json['steps'] as List<dynamic>?)
              ?.map((e) => AthkarStepModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      targetCount: (json['targetCount'] as num?)?.toInt() ?? 1,
      currentCount: (json['currentCount'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isActive: json['isActive'] as bool? ?? true,
      isSynced: json['isSynced'] as bool? ?? false,
      reminderTime: json['reminderTime'] as String?,
      reminderDays:
          (json['reminderDays'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$AthkarRoutineModelToJson(AthkarRoutineModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'name': instance.name,
      'description': instance.description,
      'steps': instance.steps,
      'targetCount': instance.targetCount,
      'currentCount': instance.currentCount,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isActive': instance.isActive,
      'isSynced': instance.isSynced,
      'reminderTime': instance.reminderTime,
      'reminderDays': instance.reminderDays,
    };

AthkarStepModel _$AthkarStepModelFromJson(Map<String, dynamic> json) =>
    AthkarStepModel(
      id: json['id'] as String,
      routineId: json['routineId'] as String,
      arabicText: json['arabicText'] as String,
      transliteration: json['transliteration'] as String?,
      translation: json['translation'] as String?,
      repetitions: (json['repetitions'] as num?)?.toInt() ?? 1,
      currentRepetitions: (json['currentRepetitions'] as num?)?.toInt() ?? 0,
      order: (json['order'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$AthkarStepModelToJson(AthkarStepModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'routineId': instance.routineId,
      'arabicText': instance.arabicText,
      'transliteration': instance.transliteration,
      'translation': instance.translation,
      'repetitions': instance.repetitions,
      'currentRepetitions': instance.currentRepetitions,
      'order': instance.order,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
