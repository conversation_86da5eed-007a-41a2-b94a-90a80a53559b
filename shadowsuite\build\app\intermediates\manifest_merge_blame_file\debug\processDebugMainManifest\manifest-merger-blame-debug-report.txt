1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.shadowsuite"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\apps\android\shadowsuite\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\apps\android\shadowsuite\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a42f03c2eee02ce9eb5a727d5ee33665\transformed\media-1.4.1\AndroidManifest.xml:25:9-27:18
30            <action android:name="android.media.browse.MediaBrowserService" />
30-->[androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a42f03c2eee02ce9eb5a727d5ee33665\transformed\media-1.4.1\AndroidManifest.xml:26:13-79
30-->[androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a42f03c2eee02ce9eb5a727d5ee33665\transformed\media-1.4.1\AndroidManifest.xml:26:21-76
31        </intent>
32    </queries>
33
34    <uses-permission android:name="android.permission.RECORD_AUDIO" />
34-->[:record_android] D:\apps\android\shadowsuite\build\record_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-71
34-->[:record_android] D:\apps\android\shadowsuite\build\record_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-68
35    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
35-->[:connectivity_plus] D:\apps\android\shadowsuite\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
35-->[:connectivity_plus] D:\apps\android\shadowsuite\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
36    <uses-permission android:name="android.permission.BLUETOOTH" />
36-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:5-68
36-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:22-65
37    <uses-permission android:name="android.permission.WAKE_LOCK" />
37-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:5-68
37-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:22-65
38    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
38-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:5-80
38-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:22-77
39    <uses-permission android:name="Manifest.permission.CAPTURE_AUDIO_OUTPUT" />
39-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:5-80
39-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:22-77
40    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
40-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
40-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
41    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
41-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
41-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.example.shadowsuite.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.example.shadowsuite.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
52        android:debuggable="true"
53        android:extractNativeLibs="false"
54        android:icon="@mipmap/ic_launcher"
55        android:label="shadowsuite" >
56        <activity
57            android:name="com.example.shadowsuite.MainActivity"
58            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
59            android:exported="true"
60            android:hardwareAccelerated="true"
61            android:launchMode="singleTop"
62            android:taskAffinity=""
63            android:theme="@style/LaunchTheme"
64            android:windowSoftInputMode="adjustResize" >
65
66            <!--
67                 Specifies an Android theme to apply to this Activity as soon as
68                 the Android process has started. This theme is visible to the user
69                 while the Flutter UI initializes. After that, this theme continues
70                 to determine the Window background behind the Flutter UI.
71            -->
72            <meta-data
73                android:name="io.flutter.embedding.android.NormalTheme"
74                android:resource="@style/NormalTheme" />
75
76            <intent-filter>
77                <action android:name="android.intent.action.MAIN" />
78
79                <category android:name="android.intent.category.LAUNCHER" />
80            </intent-filter>
81        </activity>
82        <!--
83             Don't delete the meta-data below.
84             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
85        -->
86        <meta-data
87            android:name="flutterEmbedding"
88            android:value="2" />
89        <!--
90           Declares a provider which allows us to store files to share in
91           '.../caches/share_plus' and grant the receiving action access
92        -->
93        <provider
93-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
94            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
94-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
95            android:authorities="com.example.shadowsuite.flutter.share_provider"
95-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
96            android:exported="false"
96-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
97            android:grantUriPermissions="true" >
97-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
98            <meta-data
98-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
99                android:name="android.support.FILE_PROVIDER_PATHS"
99-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
100                android:resource="@xml/flutter_share_file_paths" />
100-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
101        </provider>
102        <!--
103           This manifest declared broadcast receiver allows us to use an explicit
104           Intent when creating a PendingItent to be informed of the user's choice
105        -->
106        <receiver
106-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
107            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
107-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
108            android:exported="false" >
108-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
109            <intent-filter>
109-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
110                <action android:name="EXTRA_CHOSEN_COMPONENT" />
110-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
110-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
111            </intent-filter>
112        </receiver>
113
114        <provider
114-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
115            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
115-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
116            android:authorities="com.example.shadowsuite.flutter.image_provider"
116-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
117            android:exported="false"
117-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
118            android:grantUriPermissions="true" >
118-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
119            <meta-data
119-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
120                android:name="android.support.FILE_PROVIDER_PATHS"
120-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
121                android:resource="@xml/flutter_image_picker_file_paths" />
121-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
122        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
123        <service
123-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
124            android:name="com.google.android.gms.metadata.ModuleDependencies"
124-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
125            android:enabled="false"
125-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
126            android:exported="false" >
126-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
127            <intent-filter>
127-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
128                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
128-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
128-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
129            </intent-filter>
130
131            <meta-data
131-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
132                android:name="photopicker_activity:0:required"
132-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
133                android:value="" />
133-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
134        </service>
135
136        <activity
136-->[:url_launcher_android] D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
137            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
137-->[:url_launcher_android] D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
138            android:exported="false"
138-->[:url_launcher_android] D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
139            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
139-->[:url_launcher_android] D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
140
141        <provider
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
142            android:name="androidx.startup.InitializationProvider"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
143            android:authorities="com.example.shadowsuite.androidx-startup"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
144            android:exported="false" >
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
145            <meta-data
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
146                android:name="androidx.work.WorkManagerInitializer"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
147                android:value="androidx.startup" />
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
148            <meta-data
148-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
149                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
149-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
150                android:value="androidx.startup" />
150-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
151            <meta-data
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
152                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
153                android:value="androidx.startup" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
154        </provider>
155
156        <service
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
157            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
158            android:directBootAware="false"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
159            android:enabled="@bool/enable_system_alarm_service_default"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
160            android:exported="false" />
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
161        <service
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
162            android:name="androidx.work.impl.background.systemjob.SystemJobService"
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
163            android:directBootAware="false"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
164            android:enabled="@bool/enable_system_job_service_default"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
165            android:exported="true"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
166            android:permission="android.permission.BIND_JOB_SERVICE" />
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
167        <service
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
168            android:name="androidx.work.impl.foreground.SystemForegroundService"
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
169            android:directBootAware="false"
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
170            android:enabled="@bool/enable_system_foreground_service_default"
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
171            android:exported="false" />
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
172
173        <receiver
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
174            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
175            android:directBootAware="false"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
176            android:enabled="true"
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
177            android:exported="false" />
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
178        <receiver
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
179            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
180            android:directBootAware="false"
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
181            android:enabled="false"
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
182            android:exported="false" >
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
183            <intent-filter>
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
184                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
185                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
186            </intent-filter>
187        </receiver>
188        <receiver
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
189            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
190            android:directBootAware="false"
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
191            android:enabled="false"
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
192            android:exported="false" >
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
193            <intent-filter>
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
194                <action android:name="android.intent.action.BATTERY_OKAY" />
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
195                <action android:name="android.intent.action.BATTERY_LOW" />
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
196            </intent-filter>
197        </receiver>
198        <receiver
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
199            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
201            android:enabled="false"
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
202            android:exported="false" >
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
203            <intent-filter>
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
204                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
205                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
206            </intent-filter>
207        </receiver>
208        <receiver
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
209            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
211            android:enabled="false"
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
212            android:exported="false" >
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
213            <intent-filter>
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
214                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
215            </intent-filter>
216        </receiver>
217        <receiver
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
218            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
219            android:directBootAware="false"
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
220            android:enabled="false"
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
221            android:exported="false" >
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
222            <intent-filter>
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
223                <action android:name="android.intent.action.BOOT_COMPLETED" />
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
224                <action android:name="android.intent.action.TIME_SET" />
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
225                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
226            </intent-filter>
227        </receiver>
228        <receiver
228-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
229            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
229-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
231            android:enabled="@bool/enable_system_alarm_service_default"
231-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
232            android:exported="false" >
232-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
233            <intent-filter>
233-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
234                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
234-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
234-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
235            </intent-filter>
236        </receiver>
237        <receiver
237-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
238            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
238-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
239            android:directBootAware="false"
239-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
240            android:enabled="true"
240-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
241            android:exported="true"
241-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
242            android:permission="android.permission.DUMP" >
242-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
243            <intent-filter>
243-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
244                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
244-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
244-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
245            </intent-filter>
246        </receiver>
247
248        <uses-library
248-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
249            android:name="androidx.window.extensions"
249-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
250            android:required="false" />
250-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
251        <uses-library
251-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
252            android:name="androidx.window.sidecar"
252-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
253            android:required="false" />
253-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
254
255        <receiver
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
256            android:name="androidx.profileinstaller.ProfileInstallReceiver"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
257            android:directBootAware="false"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
258            android:enabled="true"
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
259            android:exported="true"
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
260            android:permission="android.permission.DUMP" >
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
261            <intent-filter>
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
262                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
263            </intent-filter>
264            <intent-filter>
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
265                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
266            </intent-filter>
267            <intent-filter>
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
268                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
269            </intent-filter>
270            <intent-filter>
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
271                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
272            </intent-filter>
273        </receiver>
274
275        <service
275-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
276            android:name="androidx.room.MultiInstanceInvalidationService"
276-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
277            android:directBootAware="true"
277-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
278            android:exported="false" />
278-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
279    </application>
280
281</manifest>
