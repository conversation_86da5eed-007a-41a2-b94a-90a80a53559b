import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_nexus_model.dart';
import '../providers/nexus_profile_provider.dart';
import '../../../core/theme/futuristic_theme.dart';

class ProfileQuickActions extends ConsumerWidget {
  final UserNexusModel profile;

  const ProfileQuickActions({
    super.key,
    required this.profile,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: FuturisticTheme.primaryBlue,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'Edit Profile',
                'Update your information',
                Icons.edit,
                FuturisticTheme.primaryBlue,
                () => _editProfile(context, ref),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'Privacy Settings',
                'Manage your privacy',
                Icons.privacy_tip,
                FuturisticTheme.primaryPurple,
                () => _openPrivacySettings(context, ref),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'Backup Data',
                'Export your data',
                Icons.backup,
                FuturisticTheme.primaryGreen,
                () => _backupData(context, ref),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'Sync Now',
                'Force synchronization',
                Icons.sync,
                FuturisticTheme.primaryOrange,
                () => _syncNow(context, ref),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                'Security',
                'Manage security settings',
                Icons.security,
                Colors.red,
                () => _openSecurity(context, ref),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'Notifications',
                'Configure notifications',
                Icons.notifications,
                FuturisticTheme.primaryBlue,
                () => _openNotifications(context, ref),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'Storage',
                'Manage storage usage',
                Icons.storage,
                profile.isStorageNearLimit ? Colors.orange : FuturisticTheme.primaryPurple,
                () => _openStorage(context, ref),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                'Support',
                'Get help and support',
                Icons.help,
                FuturisticTheme.primaryGreen,
                () => _openSupport(context, ref),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withOpacity(0.1),
              color.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.3),
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 6,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _editProfile(BuildContext context, WidgetRef ref) {
    // TODO: Navigate to profile edit screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Profile editing coming soon')),
    );
  }

  void _openPrivacySettings(BuildContext context, WidgetRef ref) {
    // TODO: Navigate to privacy settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Privacy settings coming soon')),
    );
  }

  void _backupData(BuildContext context, WidgetRef ref) {
    // TODO: Implement data backup
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup Data'),
        content: const Text('This will export all your data to a secure backup file.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Backup feature coming soon')),
              );
            },
            child: const Text('Start Backup'),
          ),
        ],
      ),
    );
  }

  void _syncNow(BuildContext context, WidgetRef ref) {
    // TODO: Implement force sync
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            const Text('Syncing data...'),
          ],
        ),
        duration: const Duration(seconds: 2),
      ),
    );

    // Simulate sync completion
    Future.delayed(const Duration(seconds: 2), () {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ Sync completed successfully'),
          backgroundColor: Colors.green,
        ),
      );
    });
  }

  void _openSecurity(BuildContext context, WidgetRef ref) {
    // TODO: Navigate to security settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Security settings coming soon')),
    );
  }

  void _openNotifications(BuildContext context, WidgetRef ref) {
    // TODO: Navigate to notification settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notification settings coming soon')),
    );
  }

  void _openStorage(BuildContext context, WidgetRef ref) {
    // TODO: Navigate to storage management
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Management'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Current Usage: ${profile.formattedStorageUsed}'),
            Text('Storage Limit: ${profile.formattedStorageLimit}'),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: profile.storageUsagePercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                profile.isStorageNearLimit ? Colors.orange : FuturisticTheme.primaryBlue,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${profile.storageUsagePercentage.toStringAsFixed(1)}% used',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (profile.isStorageNearLimit)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Storage cleanup coming soon')),
                );
              },
              child: const Text('Clean Up'),
            ),
        ],
      ),
    );
  }

  void _openSupport(BuildContext context, WidgetRef ref) {
    // TODO: Navigate to support
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Support center coming soon')),
    );
  }
}
