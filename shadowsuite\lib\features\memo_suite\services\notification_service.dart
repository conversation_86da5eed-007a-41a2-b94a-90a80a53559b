import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../../core/models/memo_model.dart';
import '../../../core/models/todo_item_model.dart';

enum NotificationType { reminder, dueDate, overdue, syncStatus, general }

class NotificationData {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime scheduledTime;
  final Map<String, dynamic>? data;
  final bool isRead;
  final DateTime createdAt;

  const NotificationData({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.scheduledTime,
    this.data,
    this.isRead = false,
    required this.createdAt,
  });

  NotificationData copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? scheduledTime,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? createdAt,
  }) {
    return NotificationData(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final List<NotificationData> _notifications = [];
  final StreamController<List<NotificationData>> _notificationsController =
      StreamController<List<NotificationData>>.broadcast();

  Timer? _reminderTimer;
  bool _isInitialized = false;

  // Notification settings
  bool _remindersEnabled = true;
  bool _dueDateNotificationsEnabled = true;
  bool _overdueNotificationsEnabled = true;
  bool _syncNotificationsEnabled = true;
  int _reminderMinutesBefore = 15;

  Stream<List<NotificationData>> get notificationsStream =>
      _notificationsController.stream;

  List<NotificationData> get notifications => List.unmodifiable(_notifications);

  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  // Getters for settings
  bool get remindersEnabled => _remindersEnabled;
  bool get dueDateNotificationsEnabled => _dueDateNotificationsEnabled;
  bool get overdueNotificationsEnabled => _overdueNotificationsEnabled;
  bool get syncNotificationsEnabled => _syncNotificationsEnabled;
  int get reminderMinutesBefore => _reminderMinutesBefore;

  Future<void> initialize() async {
    if (_isInitialized) return;

    // Load saved notifications and settings
    await _loadSettings();
    await _loadNotifications();

    // Start periodic check for due dates and reminders
    _startReminderTimer();

    _isInitialized = true;
  }

  Future<void> _loadSettings() async {
    // In a real app, load from SharedPreferences or database
    // For now, using default values
  }

  Future<void> _loadNotifications() async {
    // In a real app, load from local storage
    // For now, starting with empty list
    _notifyListeners();
  }

  void _startReminderTimer() {
    _reminderTimer?.cancel();
    _reminderTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkForDueReminders();
    });
  }

  void _checkForDueReminders() {
    final now = DateTime.now();

    // Check for notifications that should be triggered
    for (final notification in _notifications) {
      if (!notification.isRead &&
          notification.scheduledTime.isBefore(now) &&
          notification.scheduledTime.isAfter(
            now.subtract(const Duration(minutes: 1)),
          )) {
        _triggerNotification(notification);
      }
    }
  }

  void _triggerNotification(NotificationData notification) {
    if (kDebugMode) {
      print('Triggering notification: ${notification.title}');
    }

    // In a real app, this would show a system notification
    // For web/demo, we'll just mark it as triggered
  }

  Future<void> scheduleReminderForMemo(MemoModel memo) async {
    if (!_remindersEnabled) return;

    // Schedule reminders for todo items with due dates
    for (final todo in memo.todoItems) {
      if (todo.dueDate != null && !todo.isCompleted) {
        await scheduleReminderForTodo(memo, todo);
      }
    }

    // For general memo reminders (could be based on creation date + some interval)
    // This is optional - you can implement custom reminder logic here
    await _addNotification(
      NotificationData(
        id: 'memo_created_${memo.id}',
        title: 'New Memo Created',
        message: 'Memo "${memo.title}" has been created',
        type: NotificationType.general,
        scheduledTime: DateTime.now(),
        data: {'memoId': memo.id, 'memoType': memo.type.name},
        createdAt: DateTime.now(),
      ),
    );
  }

  Future<void> scheduleReminderForTodo(
    MemoModel memo,
    TodoItemModel todo,
  ) async {
    if (!_dueDateNotificationsEnabled) return;

    if (todo.dueDate != null && !todo.isCompleted) {
      final reminderTime = todo.dueDate!.subtract(
        Duration(minutes: _reminderMinutesBefore),
      );

      if (reminderTime.isAfter(DateTime.now())) {
        await _addNotification(
          NotificationData(
            id: 'todo_reminder_${todo.id}',
            title: 'Task Reminder',
            message: 'Task due soon: ${todo.text}',
            type: NotificationType.dueDate,
            scheduledTime: reminderTime,
            data: {
              'memoId': memo.id,
              'todoId': todo.id,
              'priority': todo.priority.name,
            },
            createdAt: DateTime.now(),
          ),
        );
      }

      // Schedule overdue notification
      if (_overdueNotificationsEnabled) {
        await _addNotification(
          NotificationData(
            id: 'todo_overdue_${todo.id}',
            title: 'Overdue Task',
            message: 'Task is overdue: ${todo.text}',
            type: NotificationType.overdue,
            scheduledTime: todo.dueDate!.add(const Duration(hours: 1)),
            data: {
              'memoId': memo.id,
              'todoId': todo.id,
              'priority': todo.priority.name,
            },
            createdAt: DateTime.now(),
          ),
        );
      }
    }
  }

  Future<void> notifySyncStatus(String message, {bool isError = false}) async {
    if (!_syncNotificationsEnabled) return;

    await _addNotification(
      NotificationData(
        id: 'sync_${DateTime.now().millisecondsSinceEpoch}',
        title: isError ? 'Sync Error' : 'Sync Status',
        message: message,
        type: NotificationType.syncStatus,
        scheduledTime: DateTime.now(),
        data: {'isError': isError},
        createdAt: DateTime.now(),
      ),
    );
  }

  Future<void> addGeneralNotification(String title, String message) async {
    await _addNotification(
      NotificationData(
        id: 'general_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        message: message,
        type: NotificationType.general,
        scheduledTime: DateTime.now(),
        createdAt: DateTime.now(),
      ),
    );
  }

  Future<void> _addNotification(NotificationData notification) async {
    _notifications.add(notification);
    _notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    // Keep only last 100 notifications
    if (_notifications.length > 100) {
      _notifications.removeRange(100, _notifications.length);
    }

    _notifyListeners();
  }

  Future<void> markAsRead(String notificationId) async {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _notifyListeners();
    }
  }

  Future<void> markAllAsRead() async {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
    _notifyListeners();
  }

  Future<void> deleteNotification(String notificationId) async {
    _notifications.removeWhere((n) => n.id == notificationId);
    _notifyListeners();
  }

  Future<void> clearAllNotifications() async {
    _notifications.clear();
    _notifyListeners();
  }

  Future<void> cancelReminderForMemo(String memoId) async {
    _notifications.removeWhere(
      (n) => n.data?['memoId'] == memoId && n.type == NotificationType.reminder,
    );
    _notifyListeners();
  }

  Future<void> cancelReminderForTodo(String todoId) async {
    _notifications.removeWhere(
      (n) =>
          n.data?['todoId'] == todoId &&
          (n.type == NotificationType.dueDate ||
              n.type == NotificationType.overdue),
    );
    _notifyListeners();
  }

  // Settings methods
  Future<void> updateSettings({
    bool? remindersEnabled,
    bool? dueDateNotificationsEnabled,
    bool? overdueNotificationsEnabled,
    bool? syncNotificationsEnabled,
    int? reminderMinutesBefore,
  }) async {
    _remindersEnabled = remindersEnabled ?? _remindersEnabled;
    _dueDateNotificationsEnabled =
        dueDateNotificationsEnabled ?? _dueDateNotificationsEnabled;
    _overdueNotificationsEnabled =
        overdueNotificationsEnabled ?? _overdueNotificationsEnabled;
    _syncNotificationsEnabled =
        syncNotificationsEnabled ?? _syncNotificationsEnabled;
    _reminderMinutesBefore = reminderMinutesBefore ?? _reminderMinutesBefore;

    // In a real app, save to SharedPreferences
    await _saveSettings();
  }

  Future<void> _saveSettings() async {
    // In a real app, save to SharedPreferences or database
  }

  void _notifyListeners() {
    _notificationsController.add(List.unmodifiable(_notifications));
  }

  void dispose() {
    _reminderTimer?.cancel();
    _notificationsController.close();
  }
}
