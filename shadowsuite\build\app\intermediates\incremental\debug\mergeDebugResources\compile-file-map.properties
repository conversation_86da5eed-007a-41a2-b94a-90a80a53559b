#Fri Jun 06 05:46:15 EET 2025
com.example.shadowsuite.app-main-44\:/drawable-v21/launch_background.xml=D\:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable-v21_launch_background.xml.flat
com.example.shadowsuite.app-main-44\:/mipmap-hdpi/ic_launcher.png=D\:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.png.flat
com.example.shadowsuite.app-main-44\:/mipmap-mdpi/ic_launcher.png=D\:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.png.flat
com.example.shadowsuite.app-main-44\:/mipmap-xhdpi/ic_launcher.png=D\:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.png.flat
com.example.shadowsuite.app-main-44\:/mipmap-xxhdpi/ic_launcher.png=D\:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.png.flat
com.example.shadowsuite.app-main-44\:/mipmap-xxxhdpi/ic_launcher.png=D\:\\apps\\android\\shadowsuite\\build\\app\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.png.flat
