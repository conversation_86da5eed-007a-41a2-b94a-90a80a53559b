name: shadowsuite
description: "A production-ready cross-platform Flutter application with offline-first SQLite database and optional Supabase real-time synchronization."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3

  # Database & Storage
  sqflite: ^2.3.0
  path: ^1.8.3
  path_provider: ^2.1.1

  # Supabase Integration
  supabase_flutter: ^2.0.0

  # UI Components
  google_nav_bar: ^5.0.6
  fl_chart: ^0.65.0
  table_calendar: ^3.0.9

  # Audio & Recording
  permission_handler: ^11.1.0
  record: ^5.0.4
  flutter_sound: ^9.2.13
  audioplayers: ^5.2.1
  speech_to_text: ^6.6.0

  # Notifications & Background (temporarily disabled for APK build)
  # flutter_local_notifications: ^16.3.0
  workmanager: ^0.5.2

  # Utilities
  intl: ^0.20.2
  connectivity_plus: ^5.0.2
  share_plus: ^7.2.1
  image_picker: ^1.0.4
  # file_picker: ^6.1.1  # Temporarily disabled for APK build
  url_launcher: ^6.2.1
  package_info_plus: ^4.2.0
  shared_preferences: ^2.2.2
  uuid: ^4.2.1

  # Icons & Fonts
  cupertino_icons: ^1.0.8

  # JSON Serialization
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  riverpod_generator: ^2.3.9
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
