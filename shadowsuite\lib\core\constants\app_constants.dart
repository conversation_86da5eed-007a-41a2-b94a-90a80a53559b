/// Application-wide constants
class AppConstants {
  // App Information
  static const String appName = 'ShadowSuite';
  static const String appVersion = '1.0.0';

  // Supabase Configuration
  static const String supabaseUrl = 'https://zrnpgshoakinpuhldctp.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpybnBnc2hvYWtpbnB1aGxkY3RwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNDUyMzQsImV4cCI6MjA2NDcyMTIzNH0.Ca24-SpPP7rHrVJQiICnwLIkjEiDQhSfIsVNrjnEGcw';

  // Database Configuration
  static const String databaseName = 'shadowsuite.db';
  static const int databaseVersion = 1;

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double defaultMargin = 12.0;
  static const double borderRadius = 8.0;

  // Navigation
  static const int dashboardIndex = 0;
  static const int memoSuiteIndex = 1;
  static const int athkarProIndex = 2;
  static const int moneyFlowIndex = 3;
  static const int profileIndex = 4;
  static const int settingsIndex = 5;

  // Audio Recording
  static const int maxRecordingDuration = 3600; // 1 hour in seconds
  static const String audioFileExtension = '.m4a';

  // Sync Configuration
  static const Duration syncInterval = Duration(minutes: 15);
  static const Duration offlineTimeout = Duration(seconds: 30);

  // Notification Channels
  static const String reminderChannelId = 'reminder_channel';
  static const String syncChannelId = 'sync_channel';

  // Shared Preferences Keys
  static const String keyLanguage = 'language';
  static const String keyThemeMode = 'theme_mode';
  static const String keyUserId = 'user_id';
  static const String keyLastSync = 'last_sync';
  static const String keyOfflineMode = 'offline_mode';
  static const String keySyncMode = 'sync_mode';
  static const String keyAutoSync = 'auto_sync';
  static const String keySyncWifiOnly = 'sync_wifi_only';
  static const String keyNotificationEnabled = 'notification_enabled';

  // Error Messages
  static const String errorGeneric = 'An unexpected error occurred';
  static const String errorNetwork = 'Network connection error';
  static const String errorAuth = 'Authentication failed';
  static const String errorPermission = 'Permission denied';
  static const String errorStorage = 'Storage access error';
}
