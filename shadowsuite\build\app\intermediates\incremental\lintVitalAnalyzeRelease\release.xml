<variant
    name="release"
    package="com.example.shadowsuite"
    minSdkVersion="24"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="D:\apps\android\shadowsuite\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="D:\apps\android\shadowsuite\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.3;C:\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="D:\apps\android\shadowsuite\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\6690d805285c209660c0deea2b7ec586\transformed\desugar_jdk_libs_configuration-2.0.4-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.12\transforms\dc4b100e803265f7c968374f34a8c270\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="D:\apps\android\shadowsuite\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\apps\android\shadowsuite\build\app\tmp\kotlin-classes\release;D:\apps\android\shadowsuite\build\app\kotlinToolingMetadata;D:\apps\android\shadowsuite\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.shadowsuite"
      generatedSourceFolders="D:\apps\android\shadowsuite\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\apps\android\shadowsuite\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\6690d805285c209660c0deea2b7ec586\transformed\desugar_jdk_libs_configuration-2.0.4-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.12\transforms\dc4b100e803265f7c968374f34a8c270\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
