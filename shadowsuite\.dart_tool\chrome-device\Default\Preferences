{"accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_tracker_service_last_update": "***********746264", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 133}, "autofill": {"last_version_deduped": 133}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 496, "left": -8, "maximized": true, "right": 1448, "top": 0, "work_area_bottom": 912, "work_area_left": 0, "work_area_right": 1440, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "***********688475", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "6ec5e44e-f21f-4051-ba29-f95f2bec20ca", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "133.0.6943.142"}, "gaia_cookie": {"changed_time": **********.360658, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "07a3eaf0-3bf7-4c0a-aa3f-039f4d3a38c8"}}, "https_upgrade_navigations": {"2025-06-05": 10}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "***********332640", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "***********332682", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "***********332728", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "***********330452", "recent_session_start_times": ["***********330452"], "session_last_active_time": "*****************", "session_start_time": "***********330452"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"device_id_salt": "803E6DBE1DE32C22833A3AFD2B4DD50D", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "QYqT33UGP2H6RF8EjNHvjIAdfe9z8UgyDE+jPS2QbZVFBLN26CKZGMZv/t2RB69KREXA5M8D5fZZMuTdOUqKZg=="}, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13393631216003926", "last_fetch_success": "13393631216458051"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "FORMS_ANNOTATIONS": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "***********328761", "prompt_shown_time_sync": "***********328736"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "media_engagement": {"http://localhost:49225,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:50337,*": {"expiration": "13401401183103652", "last_modified": "13393625183103667", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53627,*": {"expiration": "13401401017748242", "last_modified": "13393625017748255", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:54086,*": {"expiration": "13401402450553741", "last_modified": "13393626450553754", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:54514,*": {"expiration": "13401403728385299", "last_modified": "13393627728385309", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:54696,*": {"expiration": "13401406880866220", "last_modified": "13393630880866234", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:57426,*": {"expiration": "13401407359211242", "last_modified": "13393631359211259", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:58767,*": {"expiration": "13401402817344095", "last_modified": "13393626817344105", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:60227,*": {"expiration": "13401404278740199", "last_modified": "13393628278740217", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62105,*": {"expiration": "13401401910507765", "last_modified": "13393625910507778", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63424,*": {"expiration": "13401405366389464", "last_modified": "13393629366389471", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:63516,*": {"expiration": "13401404457415768", "last_modified": "13393628457415780", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:65018,*": {"expiration": "13401403592907957", "last_modified": "13393627592907971", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:65093,*": {"expiration": "13401399476754194", "last_modified": "13393623476754207", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {"http://localhost:54514,*": {"last_modified": "13393627722812102", "last_used": "13393627723102070", "last_visit": "13393296000000000", "setting": 1}, "http://localhost:63424,*": {"last_modified": "13393629326894423", "last_used": "13393629327149840", "last_visit": "13393296000000000", "setting": 1}}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:49225,*": {"last_modified": "13393626068839935", "setting": {"lastEngagementTime": 1.3393626068839912e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:50337,*": {"last_modified": "13393625179397155", "setting": {"lastEngagementTime": 1.3393625179397124e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:53627,*": {"last_modified": "13393625009347625", "setting": {"lastEngagementTime": 1.3393625009347592e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:54086,*": {"last_modified": "13393626444291280", "setting": {"lastEngagementTime": 1.3393626444291252e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.6, "rawScore": 3.6}}, "http://localhost:54514,*": {"last_modified": "13393627727205765", "setting": {"lastEngagementTime": 1.3393627727205716e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:54696,*": {"last_modified": "13393630869942765", "setting": {"lastEngagementTime": 1.3393630869942736e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.7, "rawScore": 6.9}}, "http://localhost:56554,*": {"last_modified": "13393626607643845", "setting": {"lastEngagementTime": 1.3393626607643824e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:57426,*": {"last_modified": "13393631346356431", "setting": {"lastEngagementTime": 1.3393631346356404e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "http://localhost:58767,*": {"last_modified": "13393626808080700", "setting": {"lastEngagementTime": 1.339362680808066e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.2, "rawScore": 4.2}}, "http://localhost:60227,*": {"last_modified": "13393628108685829", "setting": {"lastEngagementTime": 1.3393628108685804e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:62105,*": {"last_modified": "13393625909065130", "setting": {"lastEngagementTime": 1.33936259090651e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:63424,*": {"last_modified": "13393629364537615", "setting": {"lastEngagementTime": 1.339362936453759e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 10.799999999999997, "rawScore": 10.799999999999997}}, "http://localhost:63516,*": {"last_modified": "13393628444988628", "setting": {"lastEngagementTime": 1.3393628444988588e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:64240,*": {"last_modified": "13393625966468813", "setting": {"lastEngagementTime": 1.3393625966468798e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:65018,*": {"last_modified": "13393627590987000", "setting": {"lastEngagementTime": 1.339362759098697e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}, "http://localhost:65093,*": {"last_modified": "13393623475429009", "setting": {"lastEngagementTime": 1.339362347542896e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.3999999999999995, "rawScore": 5.3999999999999995}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"mic_stream": [{"action": 0, "prompt_disposition": 12, "time": "13393627722847081"}, {"action": 0, "prompt_disposition": 12, "time": "13393629326898715"}]}, "pref_version": 1}, "created_by_version": "133.0.6943.142", "creation_time": "***********231677", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "*****************", "last_time_obsolete_http_credentials_removed": **********.407269, "last_time_password_store_metrics_reported": **********.290889, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Person 1", "one_time_permission_prompts_decided_count": 2, "password_account_storage_settings": {}, "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "*****************", "hash_real_time_ohttp_key": "1QAgz8j/F3E832dKjlx6rzrh7vxVM9jFpMqrPY3xwrma2DsABAABAAI=", "metrics_last_log_time": "***********", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ6a3ztN+t5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEOSu87TfreUX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "*****************", "uma_in_sql_start_time": "***********286886"}, "sessions": {"event_log": [{"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393626450538499", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393626607020531", "type": 0}, {"crashed": false, "time": "13393626767443858", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393626817331446", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393627244626958", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393627592895086", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393627677255541", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393627728368587", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393628108050465", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393628278727443", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393628354012827", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393628457398649", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393629159345923", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393629366373941", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393629741196603", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393630880836779", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393631206012553", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393631359194641", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "should_read_incoming_syncing_theme_prefs": true, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "133"}}