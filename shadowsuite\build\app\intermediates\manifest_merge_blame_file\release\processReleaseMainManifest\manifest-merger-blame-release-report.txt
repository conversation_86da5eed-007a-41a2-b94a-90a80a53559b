1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.shadowsuite"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         Required to query activities that can process text, see:
12         https://developer.android.com/training/package-visibility and
13         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
14
15         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
16    -->
17    <queries>
17-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:39:5-44:15
18        <intent>
18-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:40:9-43:18
19            <action android:name="android.intent.action.PROCESS_TEXT" />
19-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:41:13-72
19-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:41:21-70
20
21            <data android:mimeType="text/plain" />
21-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:42:13-50
21-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:42:19-48
22        </intent>
23        <intent>
23-->[androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a42f03c2eee02ce9eb5a727d5ee33665\transformed\media-1.4.1\AndroidManifest.xml:25:9-27:18
24            <action android:name="android.media.browse.MediaBrowserService" />
24-->[androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a42f03c2eee02ce9eb5a727d5ee33665\transformed\media-1.4.1\AndroidManifest.xml:26:13-79
24-->[androidx.media:media:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a42f03c2eee02ce9eb5a727d5ee33665\transformed\media-1.4.1\AndroidManifest.xml:26:21-76
25        </intent>
26    </queries>
27
28    <uses-permission android:name="android.permission.RECORD_AUDIO" />
28-->[:record_android] D:\apps\android\shadowsuite\build\record_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-71
28-->[:record_android] D:\apps\android\shadowsuite\build\record_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-68
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->[:connectivity_plus] D:\apps\android\shadowsuite\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
29-->[:connectivity_plus] D:\apps\android\shadowsuite\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
30    <uses-permission android:name="android.permission.BLUETOOTH" />
30-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:5-68
30-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:22-65
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:5-68
31-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:8:22-65
32    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
32-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:5-80
32-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:22-77
33    <uses-permission android:name="Manifest.permission.CAPTURE_AUDIO_OUTPUT" />
33-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:5-80
33-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f52c5cc49499fa64a4811515302b100\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:22-77
34    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
34-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
34-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
36
37    <permission
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
38        android:name="com.example.shadowsuite.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.shadowsuite.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
42
43    <application
44        android:name="android.app.Application"
44-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:4:9-42
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
46        android:extractNativeLibs="false"
47        android:icon="@mipmap/ic_launcher"
47-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:5:9-43
48        android:label="shadowsuite" >
48-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:3:9-36
49        <activity
49-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:6:9-27:20
50            android:name="com.example.shadowsuite.MainActivity"
50-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:7:13-41
51            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
51-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:12:13-163
52            android:exported="true"
52-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:8:13-36
53            android:hardwareAccelerated="true"
53-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:13:13-47
54            android:launchMode="singleTop"
54-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:9:13-43
55            android:taskAffinity=""
55-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:10:13-36
56            android:theme="@style/LaunchTheme"
56-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:11:13-47
57            android:windowSoftInputMode="adjustResize" >
57-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:14:13-55
58
59            <!--
60                 Specifies an Android theme to apply to this Activity as soon as
61                 the Android process has started. This theme is visible to the user
62                 while the Flutter UI initializes. After that, this theme continues
63                 to determine the Window background behind the Flutter UI.
64            -->
65            <meta-data
65-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:19:13-22:17
66                android:name="io.flutter.embedding.android.NormalTheme"
66-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:20:15-70
67                android:resource="@style/NormalTheme" />
67-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:21:15-52
68
69            <intent-filter>
69-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:23:13-26:29
70                <action android:name="android.intent.action.MAIN" />
70-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:24:17-68
70-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:24:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:25:17-76
72-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:25:27-74
73            </intent-filter>
74        </activity>
75        <!--
76             Don't delete the meta-data below.
77             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
78        -->
79        <meta-data
79-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:30:9-32:33
80            android:name="flutterEmbedding"
80-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:31:13-44
81            android:value="2" />
81-->D:\apps\android\shadowsuite\android\app\src\main\AndroidManifest.xml:32:13-30
82        <!--
83           Declares a provider which allows us to store files to share in
84           '.../caches/share_plus' and grant the receiving action access
85        -->
86        <provider
86-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:20
87            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
87-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-77
88            android:authorities="com.example.shadowsuite.flutter.share_provider"
88-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-74
89            android:exported="false"
89-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
90            android:grantUriPermissions="true" >
90-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-47
91            <meta-data
91-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
92                android:name="android.support.FILE_PROVIDER_PATHS"
92-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
93                android:resource="@xml/flutter_share_file_paths" />
93-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
94        </provider>
95        <!--
96           This manifest declared broadcast receiver allows us to use an explicit
97           Intent when creating a PendingItent to be informed of the user's choice
98        -->
99        <receiver
99-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-32:20
100            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
100-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-82
101            android:exported="false" >
101-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-37
102            <intent-filter>
102-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-31:29
103                <action android:name="EXTRA_CHOSEN_COMPONENT" />
103-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-65
103-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:25-62
104            </intent-filter>
105        </receiver>
106
107        <provider
107-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
108            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
108-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
109            android:authorities="com.example.shadowsuite.flutter.image_provider"
109-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
110            android:exported="false"
110-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
111            android:grantUriPermissions="true" >
111-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
112            <meta-data
112-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
113                android:name="android.support.FILE_PROVIDER_PATHS"
113-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
114                android:resource="@xml/flutter_image_picker_file_paths" />
114-->[:share_plus] D:\apps\android\shadowsuite\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
115        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
116        <service
116-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
117            android:name="com.google.android.gms.metadata.ModuleDependencies"
117-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
118            android:enabled="false"
118-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
119            android:exported="false" >
119-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
120            <intent-filter>
120-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
121                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
121-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
121-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
122            </intent-filter>
123
124            <meta-data
124-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
125                android:name="photopicker_activity:0:required"
125-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
126                android:value="" />
126-->[:image_picker_android] D:\apps\android\shadowsuite\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
127        </service>
128
129        <activity
129-->[:url_launcher_android] D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
130            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
130-->[:url_launcher_android] D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
131            android:exported="false"
131-->[:url_launcher_android] D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
132            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
132-->[:url_launcher_android] D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
133
134        <provider
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
135            android:name="androidx.startup.InitializationProvider"
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
136            android:authorities="com.example.shadowsuite.androidx-startup"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
137            android:exported="false" >
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
138            <meta-data
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
139                android:name="androidx.work.WorkManagerInitializer"
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
140                android:value="androidx.startup" />
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
141            <meta-data
141-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
142                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
142-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
143                android:value="androidx.startup" />
143-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
144            <meta-data
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
145                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
146                android:value="androidx.startup" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
147        </provider>
148
149        <service
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
150            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
152            android:enabled="@bool/enable_system_alarm_service_default"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
153            android:exported="false" />
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
154        <service
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
155            android:name="androidx.work.impl.background.systemjob.SystemJobService"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
157            android:enabled="@bool/enable_system_job_service_default"
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
158            android:exported="true"
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
159            android:permission="android.permission.BIND_JOB_SERVICE" />
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
160        <service
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
161            android:name="androidx.work.impl.foreground.SystemForegroundService"
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
162            android:directBootAware="false"
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
163            android:enabled="@bool/enable_system_foreground_service_default"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
164            android:exported="false" />
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
165
166        <receiver
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
167            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
168            android:directBootAware="false"
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
169            android:enabled="true"
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
170            android:exported="false" />
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
171        <receiver
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
172            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
173            android:directBootAware="false"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
174            android:enabled="false"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
175            android:exported="false" >
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
176            <intent-filter>
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
177                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
178                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
179            </intent-filter>
180        </receiver>
181        <receiver
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
182            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
184            android:enabled="false"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
185            android:exported="false" >
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
186            <intent-filter>
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
187                <action android:name="android.intent.action.BATTERY_OKAY" />
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
188                <action android:name="android.intent.action.BATTERY_LOW" />
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
189            </intent-filter>
190        </receiver>
191        <receiver
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
192            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
193            android:directBootAware="false"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
194            android:enabled="false"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
195            android:exported="false" >
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
196            <intent-filter>
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
197                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
198                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
199            </intent-filter>
200        </receiver>
201        <receiver
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
202            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
203            android:directBootAware="false"
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
204            android:enabled="false"
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
205            android:exported="false" >
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
206            <intent-filter>
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
207                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
208            </intent-filter>
209        </receiver>
210        <receiver
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
211            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
212            android:directBootAware="false"
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
213            android:enabled="false"
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
214            android:exported="false" >
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
215            <intent-filter>
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
216                <action android:name="android.intent.action.BOOT_COMPLETED" />
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
217                <action android:name="android.intent.action.TIME_SET" />
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
218                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
219            </intent-filter>
220        </receiver>
221        <receiver
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
222            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
223            android:directBootAware="false"
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
224            android:enabled="@bool/enable_system_alarm_service_default"
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
225            android:exported="false" >
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
226            <intent-filter>
226-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
227                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
227-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
227-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
228            </intent-filter>
229        </receiver>
230        <receiver
230-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
231            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
231-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
232            android:directBootAware="false"
232-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
233            android:enabled="true"
233-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
234            android:exported="true"
234-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
235            android:permission="android.permission.DUMP" >
235-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
236            <intent-filter>
236-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
237                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
237-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
237-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\db6569caad4edde0be5199a526eef45e\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
238            </intent-filter>
239        </receiver>
240
241        <uses-library
241-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
242            android:name="androidx.window.extensions"
242-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
243            android:required="false" />
243-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
244        <uses-library
244-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
245            android:name="androidx.window.sidecar"
245-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
246            android:required="false" />
246-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
247
248        <receiver
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
249            android:name="androidx.profileinstaller.ProfileInstallReceiver"
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
250            android:directBootAware="false"
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
251            android:enabled="true"
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
252            android:exported="true"
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
253            android:permission="android.permission.DUMP" >
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
255                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
256            </intent-filter>
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
258                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
259            </intent-filter>
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
261                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
262            </intent-filter>
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
264                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
265            </intent-filter>
266        </receiver>
267
268        <service
268-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
269            android:name="androidx.room.MultiInstanceInvalidationService"
269-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
270            android:directBootAware="true"
270-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
271            android:exported="false" />
271-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b584c30b3a9d44d91a667d658f6ad726\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
272    </application>
273
274</manifest>
