[{"merged": "dev.fluttercommunity.plus.packageinfo.package_info_plus-release-25:/layout-v21/notification_template_icon_group.xml", "source": "dev.fluttercommunity.plus.packageinfo.package_info_plus-core-1.13.1-7:/layout-v21/notification_template_icon_group.xml"}, {"merged": "dev.fluttercommunity.plus.packageinfo.package_info_plus-release-25:/layout-v21/notification_action.xml", "source": "dev.fluttercommunity.plus.packageinfo.package_info_plus-core-1.13.1-7:/layout-v21/notification_action.xml"}, {"merged": "dev.fluttercommunity.plus.packageinfo.package_info_plus-release-25:/layout-v21/notification_action_tombstone.xml", "source": "dev.fluttercommunity.plus.packageinfo.package_info_plus-core-1.13.1-7:/layout-v21/notification_action_tombstone.xml"}, {"merged": "dev.fluttercommunity.plus.packageinfo.package_info_plus-release-25:/layout-v21/notification_template_custom_big.xml", "source": "dev.fluttercommunity.plus.packageinfo.package_info_plus-core-1.13.1-7:/layout-v21/notification_template_custom_big.xml"}]