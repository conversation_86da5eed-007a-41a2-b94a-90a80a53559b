import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/memo_model.dart';
import '../providers/memo_provider.dart';

class CreateTextNoteScreen extends ConsumerStatefulWidget {
  final MemoModel? editingMemo;

  const CreateTextNoteScreen({super.key, this.editingMemo});

  @override
  ConsumerState<CreateTextNoteScreen> createState() => _CreateTextNoteScreenState();
}

class _CreateTextNoteScreenState extends ConsumerState<CreateTextNoteScreen> {
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _categoryController = TextEditingController();
  final _tagsController = TextEditingController();
  
  List<String> _tags = [];
  bool _isPinned = false;
  bool _isMarkdown = false;

  @override
  void initState() {
    super.initState();
    if (widget.editingMemo != null) {
      _initializeFromMemo(widget.editingMemo!);
    }
  }

  void _initializeFromMemo(MemoModel memo) {
    _titleController.text = memo.title;
    _contentController.text = memo.description ?? '';
    _categoryController.text = memo.category ?? '';
    _tagsController.text = memo.tags.join(', ');
    _tags = List.from(memo.tags);
    _isPinned = memo.isPinned;
    _isMarkdown = memo.richContent != null;
    if (_isMarkdown) {
      _contentController.text = memo.richContent ?? '';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _categoryController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  void _updateTags() {
    final tagsText = _tagsController.text;
    _tags = tagsText
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
  }

  void _saveMemo() {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a title')),
      );
      return;
    }

    _updateTags();

    final memo = MemoModel(
      id: widget.editingMemo?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      userId: 'demo-user-id',
      title: _titleController.text.trim(),
      description: _isMarkdown ? null : _contentController.text.trim(),
      richContent: _isMarkdown ? _contentController.text.trim() : null,
      type: MemoType.text,
      todoItems: const [],
      attachments: const [],
      createdAt: widget.editingMemo?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
      tags: _tags,
      category: _categoryController.text.trim().isEmpty 
          ? null 
          : _categoryController.text.trim(),
      isPinned: _isPinned,
      isArchived: widget.editingMemo?.isArchived ?? false,
    );

    if (widget.editingMemo != null) {
      ref.read(memosProvider.notifier).updateMemo(memo);
    } else {
      ref.read(memosProvider.notifier).addMemo(memo);
    }

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.editingMemo != null ? 'Edit Note' : 'Create Text Note'),
        actions: [
          IconButton(
            icon: Icon(_isPinned ? Icons.push_pin : Icons.push_pin_outlined),
            onPressed: () {
              setState(() {
                _isPinned = !_isPinned;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveMemo,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),

            // Markdown toggle
            Card(
              child: SwitchListTile(
                title: const Text('Markdown Support'),
                subtitle: const Text('Enable rich text formatting with Markdown'),
                value: _isMarkdown,
                onChanged: (value) {
                  setState(() {
                    _isMarkdown = value;
                  });
                },
                secondary: const Icon(Icons.text_format),
              ),
            ),
            const SizedBox(height: 16),

            // Content
            SizedBox(
              height: 300,
              child: TextField(
                controller: _contentController,
                decoration: InputDecoration(
                  labelText: _isMarkdown ? 'Content (Markdown)' : 'Content',
                  border: const OutlineInputBorder(),
                  alignLabelWithHint: true,
                  helperText: _isMarkdown 
                      ? 'Use **bold**, *italic*, # headers, - lists, etc.'
                      : 'Write your note content here',
                ),
                maxLines: null,
                expands: true,
                textAlignVertical: TextAlignVertical.top,
              ),
            ),
            const SizedBox(height: 16),

            // Category
            TextField(
              controller: _categoryController,
              decoration: const InputDecoration(
                labelText: 'Category (Optional)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
                hintText: 'e.g., Work, Personal, Ideas',
              ),
            ),
            const SizedBox(height: 16),

            // Tags
            TextField(
              controller: _tagsController,
              decoration: const InputDecoration(
                labelText: 'Tags (Optional)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.tag),
                hintText: 'Separate tags with commas',
                helperText: 'e.g., important, meeting, project',
              ),
              onChanged: (value) => _updateTags(),
            ),
            const SizedBox(height: 16),

            // Tags preview
            if (_tags.isNotEmpty) ...[
              const Text(
                'Tags:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _tags.map((tag) => Chip(
                  label: Text(tag),
                  backgroundColor: Colors.blue.withValues(alpha: 0.1),
                  labelStyle: TextStyle(color: Colors.blue[700]),
                  deleteIcon: const Icon(Icons.close, size: 16),
                  onDeleted: () {
                    setState(() {
                      _tags.remove(tag);
                      _tagsController.text = _tags.join(', ');
                    });
                  },
                )).toList(),
              ),
              const SizedBox(height: 16),
            ],

            // Quick actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Quick Actions',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              // Add current date/time
                              final now = DateTime.now();
                              final dateTime = '${now.day}/${now.month}/${now.year} ${now.hour}:${now.minute.toString().padLeft(2, '0')}';
                              _contentController.text += '\n\n--- $dateTime ---\n';
                            },
                            icon: const Icon(Icons.access_time),
                            label: const Text('Add Timestamp'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              // Add template
                              if (_isMarkdown) {
                                _contentController.text += '\n\n## New Section\n\n- Point 1\n- Point 2\n- Point 3\n';
                              } else {
                                _contentController.text += '\n\nNew Section:\n• Point 1\n• Point 2\n• Point 3\n';
                              }
                            },
                            icon: const Icon(Icons.list),
                            label: const Text('Add Template'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),

            // Save button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _saveMemo,
                icon: const Icon(Icons.save),
                label: Text(widget.editingMemo != null ? 'Update Note' : 'Save Note'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
