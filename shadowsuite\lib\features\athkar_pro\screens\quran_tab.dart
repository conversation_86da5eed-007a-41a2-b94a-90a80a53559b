import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/quran_model.dart';
import 'surah_reading_screen.dart';

class QuranTab extends ConsumerWidget {
  const QuranTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.book, size: 32, color: Colors.purple),
                        const SizedBox(width: 12),
                        Text(
                          'Quran & Supplications',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Read and reflect on verses and supplications',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Featured verses section
            _buildFeaturedVersesSection(context),
            const SizedBox(height: 20),

            // Supplications section
            _buildSupplicationsSection(context),
            const SizedBox(height: 20),

            // Daily verse section
            _buildDailyVerseSection(context),
            const SizedBox(height: 20),

            // Quran Surahs section
            _buildQuranSurahsSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedVersesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Featured Verses',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Ayat al-Kursi
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.purple.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ۚ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                    textAlign: TextAlign.center,
                    textDirection: TextDirection.rtl,
                  ),
                ),
                const SizedBox(height: 12),
                const Text(
                  'Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence. Neither drowsiness overtakes Him nor sleep.',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Al-Baqarah 2:255',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        // TODO: Implement read functionality
                      },
                      icon: const Icon(Icons.visibility),
                      label: const Text('Read'),
                    ),
                    ElevatedButton.icon(
                      onPressed: () {
                        // TODO: Implement favorite functionality
                      },
                      icon: const Icon(Icons.favorite_border),
                      label: const Text('Favorite'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSupplicationsSection(BuildContext context) {
    final supplications = [
      {
        'title': 'Morning Supplication',
        'arabic': 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ',
        'translation':
            'We have reached the morning and with it Allah\'s dominion',
        'category': 'Morning',
      },
      {
        'title': 'Evening Supplication',
        'arabic': 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ',
        'translation':
            'We have reached the evening and with it Allah\'s dominion',
        'category': 'Evening',
      },
      {
        'title': 'Before Sleep',
        'arabic': 'بِاسْمِكَ اللَّهُمَّ أَمُوتُ وَأَحْيَا',
        'translation': 'In Your name, O Allah, I die and I live',
        'category': 'Sleep',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Daily Supplications',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        ...supplications.map(
          (supplication) => Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.green.withValues(alpha: 0.1),
                child: const Icon(Icons.auto_stories, color: Colors.green),
              ),
              title: Text(supplication['title']!),
              subtitle: Text(
                supplication['arabic']!,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textDirection: TextDirection.rtl,
              ),
              trailing: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  supplication['category']!,
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              onTap: () {
                _showSupplicationDetails(context, supplication);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDailyVerseSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Verse of the Day',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Card(
          color: Colors.amber.withValues(alpha: 0.1),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 32),
                const SizedBox(height: 12),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber,
                    ),
                    textAlign: TextAlign.center,
                    textDirection: TextDirection.rtl,
                  ),
                ),
                const SizedBox(height: 12),
                const Text(
                  'And whoever fears Allah - He will make for him a way out',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'At-Talaq 65:2',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showSupplicationDetails(
    BuildContext context,
    Map<String, String> supplication,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(supplication['title']!),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                supplication['arabic']!,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
                textAlign: TextAlign.center,
                textDirection: TextDirection.rtl,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              supplication['translation']!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Added to favorites!')),
              );
            },
            child: const Text('Add to Favorites'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuranSurahsSection(BuildContext context) {
    final surahs = [
      {
        'number': 1,
        'nameArabic': 'الفاتحة',
        'nameEnglish': 'Al-Fatiha',
        'verses': 7,
        'type': 'Meccan',
      },
      {
        'number': 2,
        'nameArabic': 'البقرة',
        'nameEnglish': 'Al-Baqarah',
        'verses': 286,
        'type': 'Medinan',
      },
      {
        'number': 3,
        'nameArabic': 'آل عمران',
        'nameEnglish': 'Ali Imran',
        'verses': 200,
        'type': 'Medinan',
      },
      {
        'number': 4,
        'nameArabic': 'النساء',
        'nameEnglish': 'An-Nisa',
        'verses': 176,
        'type': 'Medinan',
      },
      {
        'number': 5,
        'nameArabic': 'المائدة',
        'nameEnglish': 'Al-Maidah',
        'verses': 120,
        'type': 'Medinan',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Quran Surahs',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () {
                _showAllSurahs(context);
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: surahs.length,
            itemBuilder: (context, index) {
              final surah = surahs[index];
              return Container(
                width: 160,
                margin: const EdgeInsets.only(right: 12),
                child: Card(
                  child: InkWell(
                    onTap: () => _openSurah(context, surah),
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                width: 30,
                                height: 30,
                                decoration: BoxDecoration(
                                  color: Colors.blue.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Center(
                                  child: Text(
                                    surah['number'].toString(),
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue[700],
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: surah['type'] == 'Meccan'
                                      ? Colors.orange.withValues(alpha: 0.1)
                                      : Colors.purple.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  surah['type'] as String,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: surah['type'] == 'Meccan'
                                        ? Colors.orange[700]
                                        : Colors.purple[700],
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            surah['nameArabic'] as String,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                            textDirection: TextDirection.rtl,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            surah['nameEnglish'] as String,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '${surah['verses']} verses',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showAllSurahs(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AllSurahsScreen()));
  }

  void _openSurah(BuildContext context, Map<String, dynamic> surah) {
    final surahModel = SurahModel(
      number: surah['number'] as int,
      nameArabic: surah['nameArabic'] as String,
      nameEnglish: surah['nameEnglish'] as String,
      nameTransliteration:
          surah['nameEnglish'] as String, // Using same for demo
      meaning: 'The ${surah['nameEnglish']}',
      versesCount: surah['verses'] as int,
      revelationType: surah['type'] as String,
      revelationOrder: surah['number'] as int,
      lastReadVerse: (surah['number'] as int) == 1
          ? 3 // Demo progress for Al-Fatiha
          : null,
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SurahReadingScreen(surah: surahModel),
      ),
    );
  }
}

// Placeholder for All Surahs Screen
class AllSurahsScreen extends StatelessWidget {
  const AllSurahsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('All Surahs')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.menu_book, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'Complete Quran',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Full Quran with 114 Surahs coming soon!'),
          ],
        ),
      ),
    );
  }
}
