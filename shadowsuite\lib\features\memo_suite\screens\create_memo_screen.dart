import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/memo_model.dart';
import '../../../core/models/todo_item_model.dart';
import '../../../core/models/file_attachment_model.dart';
import '../providers/memo_provider.dart';
import '../services/file_attachment_service.dart';

class CreateMemoScreen extends ConsumerStatefulWidget {
  final MemoModel? editingMemo;

  const CreateMemoScreen({super.key, this.editingMemo});

  @override
  ConsumerState<CreateMemoScreen> createState() => _CreateMemoScreenState();
}

class _CreateMemoScreenState extends ConsumerState<CreateMemoScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _richContentController = TextEditingController();
  final _categoryController = TextEditingController();
  final _tagsController = TextEditingController();

  MemoType _selectedType = MemoType.text;
  List<TodoItemModel> _todoItems = [];
  List<FileAttachmentModel> _attachments = [];
  List<String> _tags = [];
  String? _category;
  bool _isPinned = false;
  final FileAttachmentService _fileService = FileAttachmentService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);

    if (widget.editingMemo != null) {
      _initializeFromMemo(widget.editingMemo!);
    }
  }

  void _initializeFromMemo(MemoModel memo) {
    _titleController.text = memo.title;
    _descriptionController.text = memo.description ?? '';
    _richContentController.text = memo.richContent ?? '';
    _categoryController.text = memo.category ?? '';
    _tagsController.text = memo.tags.join(', ');
    _selectedType = memo.type;
    _todoItems = List.from(memo.todoItems);
    _attachments = List.from(memo.attachments);
    _tags = List.from(memo.tags);
    _category = memo.category;
    _isPinned = memo.isPinned;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _richContentController.dispose();
    _categoryController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  void _addTodoItem() {
    showDialog(
      context: context,
      builder: (context) => _TodoItemDialog(
        onAdd: (todoItem) {
          setState(() {
            _todoItems.add(todoItem);
          });
        },
      ),
    );
  }

  void _editTodoItem(int index) {
    showDialog(
      context: context,
      builder: (context) => _TodoItemDialog(
        initialItem: _todoItems[index],
        onAdd: (todoItem) {
          setState(() {
            _todoItems[index] = todoItem;
          });
        },
      ),
    );
  }

  void _deleteTodoItem(int index) {
    setState(() {
      _todoItems.removeAt(index);
    });
  }

  void _updateTags() {
    final tagsText = _tagsController.text;
    _tags = tagsText
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
  }

  void _saveMemo() {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please enter a title')));
      return;
    }

    _updateTags();

    final memo = MemoModel(
      id:
          widget.editingMemo?.id ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      userId: 'demo-user-id',
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      richContent: _richContentController.text.trim().isEmpty
          ? null
          : _richContentController.text.trim(),
      transcription: widget.editingMemo?.transcription,
      audioFilePath: widget.editingMemo?.audioFilePath,
      audioUrl: widget.editingMemo?.audioUrl,
      duration: widget.editingMemo?.duration ?? 0,
      type: _selectedType,
      todoItems: _todoItems,
      attachments: _attachments,
      createdAt: widget.editingMemo?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
      tags: _tags,
      category: _categoryController.text.trim().isEmpty
          ? null
          : _categoryController.text.trim(),
      isPinned: _isPinned,
      isArchived: widget.editingMemo?.isArchived ?? false,
    );

    if (widget.editingMemo != null) {
      ref.read(memosProvider.notifier).updateMemo(memo);
    } else {
      ref.read(memosProvider.notifier).addMemo(memo);
    }

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.editingMemo != null ? 'Edit Memo' : 'Create Memo'),
        actions: [
          IconButton(
            icon: Icon(_isPinned ? Icons.push_pin : Icons.push_pin_outlined),
            onPressed: () {
              setState(() {
                _isPinned = !_isPinned;
              });
            },
          ),
          IconButton(icon: const Icon(Icons.save), onPressed: _saveMemo),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.text_fields), text: 'Basic'),
            Tab(icon: Icon(Icons.format_align_left), text: 'Rich Text'),
            Tab(icon: Icon(Icons.checklist), text: 'Todo'),
            Tab(icon: Icon(Icons.attach_file), text: 'Files'),
            Tab(icon: Icon(Icons.label), text: 'Tags'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildBasicTab(),
          _buildRichTextTab(),
          _buildTodoTab(),
          _buildAttachmentsTab(),
          _buildTagsTab(),
        ],
      ),
    );
  }

  Widget _buildBasicTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Memo Type Selection
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Memo Type',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: MemoType.values.map((type) {
                      return ChoiceChip(
                        label: Text(type.name.toUpperCase()),
                        selected: _selectedType == type,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedType = type;
                            });
                          }
                        },
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Title
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Title *',
              border: OutlineInputBorder(),
            ),
            maxLines: 1,
          ),
          const SizedBox(height: 16),

          // Description
          Expanded(
            child: TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRichTextTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Rich Text Toolbar (simplified)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.format_bold),
                    onPressed: () {
                      // TODO: Implement bold formatting
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.format_italic),
                    onPressed: () {
                      // TODO: Implement italic formatting
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.format_underlined),
                    onPressed: () {
                      // TODO: Implement underline formatting
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.format_list_bulleted),
                    onPressed: () {
                      // TODO: Implement bullet list
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.format_list_numbered),
                    onPressed: () {
                      // TODO: Implement numbered list
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Rich Content Editor
          Expanded(
            child: TextField(
              controller: _richContentController,
              decoration: const InputDecoration(
                labelText: 'Rich Content (HTML/Markdown)',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
                helperText: 'You can use HTML or Markdown formatting',
              ),
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodoTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              const Text(
                'Todo Items',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _addTodoItem,
                icon: const Icon(Icons.add),
                label: const Text('Add Item'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Expanded(
            child: _todoItems.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.checklist, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'No todo items yet',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Tap "Add Item" to create your first todo',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: _todoItems.length,
                    itemBuilder: (context, index) {
                      final item = _todoItems[index];
                      return Card(
                        child: ListTile(
                          leading: Checkbox(
                            value: item.isCompleted,
                            onChanged: (value) {
                              setState(() {
                                _todoItems[index] = item.copyWith(
                                  isCompleted: value ?? false,
                                );
                              });
                            },
                          ),
                          title: Text(
                            item.text,
                            style: TextStyle(
                              decoration: item.isCompleted
                                  ? TextDecoration.lineThrough
                                  : null,
                            ),
                          ),
                          subtitle: item.dueDate != null
                              ? Text(
                                  'Due: ${item.dueDate!.toString().split(' ')[0]}',
                                )
                              : null,
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Priority indicator
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: _getPriorityColor(item.priority),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              PopupMenuButton(
                                itemBuilder: (context) => [
                                  const PopupMenuItem(
                                    value: 'edit',
                                    child: Row(
                                      children: [
                                        Icon(Icons.edit),
                                        SizedBox(width: 8),
                                        Text('Edit'),
                                      ],
                                    ),
                                  ),
                                  const PopupMenuItem(
                                    value: 'delete',
                                    child: Row(
                                      children: [
                                        Icon(Icons.delete, color: Colors.red),
                                        SizedBox(width: 8),
                                        Text(
                                          'Delete',
                                          style: TextStyle(color: Colors.red),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                onSelected: (value) {
                                  if (value == 'edit') {
                                    _editTodoItem(index);
                                  } else if (value == 'delete') {
                                    _deleteTodoItem(index);
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              const Text(
                'File Attachments',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              PopupMenuButton<String>(
                icon: const Icon(Icons.add),
                tooltip: 'Add attachment',
                onSelected: (value) async {
                  try {
                    List<FileAttachmentModel>? newAttachments;

                    switch (value) {
                      case 'images':
                        newAttachments = await _fileService.pickImages();
                        break;
                      case 'documents':
                        newAttachments = await _fileService.pickDocuments();
                        break;
                      case 'audio':
                        newAttachments = await _fileService.pickAudioFiles();
                        break;
                      case 'any':
                        newAttachments = await _fileService.pickFiles();
                        break;
                    }

                    if (newAttachments != null && newAttachments.isNotEmpty) {
                      setState(() {
                        _attachments.addAll(newAttachments!);
                      });

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Added ${newAttachments.length} file(s)',
                          ),
                        ),
                      );
                    }
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error adding files: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'images',
                    child: Row(
                      children: [
                        Icon(Icons.image),
                        SizedBox(width: 8),
                        Text('Images'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'documents',
                    child: Row(
                      children: [
                        Icon(Icons.description),
                        SizedBox(width: 8),
                        Text('Documents'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'audio',
                    child: Row(
                      children: [
                        Icon(Icons.audiotrack),
                        SizedBox(width: 8),
                        Text('Audio Files'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'any',
                    child: Row(
                      children: [
                        Icon(Icons.attach_file),
                        SizedBox(width: 8),
                        Text('Any File'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          Expanded(
            child: _attachments.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.attach_file, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'No attachments yet',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Tap the + button to add files',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: _attachments.length,
                    itemBuilder: (context, index) {
                      final attachment = _attachments[index];
                      return Card(
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: _getFileTypeColor(
                              attachment.mimeType,
                            ).withValues(alpha: 0.1),
                            child: Icon(
                              _getFileTypeIcon(attachment.mimeType),
                              color: _getFileTypeColor(attachment.mimeType),
                            ),
                          ),
                          title: Text(
                            attachment.name,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          subtitle: Text(
                            '${attachment.formattedSize} • ${_getFileTypeLabel(attachment.mimeType)}',
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () {
                              setState(() {
                                _attachments.removeAt(index);
                              });

                              // Delete the local file
                              _fileService.deleteLocalFile(attachment.path);

                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Attachment removed'),
                                ),
                              );
                            },
                          ),
                          onTap: () {
                            // TODO: Implement file preview/open
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Opening ${attachment.name}...'),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  IconData _getFileTypeIcon(String mimeType) {
    if (mimeType.startsWith('image/')) return Icons.image;
    if (mimeType.startsWith('audio/')) return Icons.audiotrack;
    if (mimeType.contains('pdf')) return Icons.picture_as_pdf;
    if (mimeType.contains('document') || mimeType.contains('word'))
      return Icons.description;
    if (mimeType.contains('text')) return Icons.text_snippet;
    return Icons.attach_file;
  }

  Color _getFileTypeColor(String mimeType) {
    if (mimeType.startsWith('image/')) return Colors.blue;
    if (mimeType.startsWith('audio/')) return Colors.purple;
    if (mimeType.contains('pdf')) return Colors.red;
    if (mimeType.contains('document') || mimeType.contains('word'))
      return Colors.indigo;
    if (mimeType.contains('text')) return Colors.green;
    return Colors.grey;
  }

  String _getFileTypeLabel(String mimeType) {
    if (mimeType.startsWith('image/')) return 'Image';
    if (mimeType.startsWith('audio/')) return 'Audio';
    if (mimeType.contains('pdf')) return 'PDF';
    if (mimeType.contains('document') || mimeType.contains('word'))
      return 'Document';
    if (mimeType.contains('text')) return 'Text';
    return 'File';
  }

  Widget _buildTagsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextField(
            controller: _categoryController,
            decoration: const InputDecoration(
              labelText: 'Category',
              border: OutlineInputBorder(),
              helperText: 'Optional category for organizing memos',
            ),
          ),
          const SizedBox(height: 16),

          TextField(
            controller: _tagsController,
            decoration: const InputDecoration(
              labelText: 'Tags',
              border: OutlineInputBorder(),
              helperText:
                  'Separate tags with commas (e.g., work, important, project)',
            ),
            onChanged: (value) => _updateTags(),
          ),
          const SizedBox(height: 16),

          if (_tags.isNotEmpty) ...[
            const Text(
              'Preview:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _tags.map((tag) {
                return Chip(
                  label: Text(tag),
                  deleteIcon: const Icon(Icons.close, size: 18),
                  onDeleted: () {
                    setState(() {
                      _tags.remove(tag);
                      _tagsController.text = _tags.join(', ');
                    });
                  },
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.low:
        return Colors.green;
      case Priority.medium:
        return Colors.orange;
      case Priority.high:
        return Colors.red;
      case Priority.urgent:
        return Colors.purple;
    }
  }
}

class _TodoItemDialog extends StatefulWidget {
  final TodoItemModel? initialItem;
  final Function(TodoItemModel) onAdd;

  const _TodoItemDialog({this.initialItem, required this.onAdd});

  @override
  State<_TodoItemDialog> createState() => _TodoItemDialogState();
}

class _TodoItemDialogState extends State<_TodoItemDialog> {
  late TextEditingController _textController;
  Priority _priority = Priority.medium;
  DateTime? _dueDate;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(
      text: widget.initialItem?.text ?? '',
    );
    _priority = widget.initialItem?.priority ?? Priority.medium;
    _dueDate = widget.initialItem?.dueDate;
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.initialItem != null ? 'Edit Todo Item' : 'Add Todo Item',
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _textController,
            decoration: const InputDecoration(
              labelText: 'Todo text',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<Priority>(
            value: _priority,
            decoration: const InputDecoration(
              labelText: 'Priority',
              border: OutlineInputBorder(),
            ),
            items: Priority.values.map((priority) {
              return DropdownMenuItem(
                value: priority,
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: _getPriorityColor(priority),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(priority.name.toUpperCase()),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _priority = value!;
              });
            },
          ),
          const SizedBox(height: 16),

          ListTile(
            title: const Text('Due Date'),
            subtitle: Text(_dueDate?.toString().split(' ')[0] ?? 'No due date'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.calendar_today),
                  onPressed: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: _dueDate ?? DateTime.now(),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );
                    if (date != null) {
                      setState(() {
                        _dueDate = date;
                      });
                    }
                  },
                ),
                if (_dueDate != null)
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _dueDate = null;
                      });
                    },
                  ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_textController.text.trim().isNotEmpty) {
              final todoItem = TodoItemModel(
                id:
                    widget.initialItem?.id ??
                    DateTime.now().millisecondsSinceEpoch.toString(),
                text: _textController.text.trim(),
                isCompleted: widget.initialItem?.isCompleted ?? false,
                dueDate: _dueDate,
                priority: _priority,
                createdAt: widget.initialItem?.createdAt ?? DateTime.now(),
                updatedAt: DateTime.now(),
              );
              widget.onAdd(todoItem);
              Navigator.of(context).pop();
            }
          },
          child: Text(widget.initialItem != null ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.low:
        return Colors.green;
      case Priority.medium:
        return Colors.orange;
      case Priority.high:
        return Colors.red;
      case Priority.urgent:
        return Colors.purple;
    }
  }
}
