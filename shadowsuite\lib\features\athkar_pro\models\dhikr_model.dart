class DhikrModel {
  final String id;
  final String arabicText;
  final String transliteration;
  final String translation;
  final int targetCount;
  final int currentCount;
  final String category;
  final List<String> benefits;
  final String? source;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isCompleted;
  final int streakCount;
  final DateTime? lastCompletedDate;

  const DhikrModel({
    required this.id,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.targetCount,
    this.currentCount = 0,
    required this.category,
    this.benefits = const [],
    this.source,
    required this.createdAt,
    required this.updatedAt,
    this.isCompleted = false,
    this.streakCount = 0,
    this.lastCompletedDate,
  });

  DhikrModel copyWith({
    String? id,
    String? arabicText,
    String? transliteration,
    String? translation,
    int? targetCount,
    int? currentCount,
    String? category,
    List<String>? benefits,
    String? source,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isCompleted,
    int? streakCount,
    DateTime? lastCompletedDate,
  }) {
    return DhikrModel(
      id: id ?? this.id,
      arabicText: arabicText ?? this.arabicText,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      targetCount: targetCount ?? this.targetCount,
      currentCount: currentCount ?? this.currentCount,
      category: category ?? this.category,
      benefits: benefits ?? this.benefits,
      source: source ?? this.source,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isCompleted: isCompleted ?? this.isCompleted,
      streakCount: streakCount ?? this.streakCount,
      lastCompletedDate: lastCompletedDate ?? this.lastCompletedDate,
    );
  }

  double get progress => targetCount > 0 ? currentCount / targetCount : 0.0;

  bool get isTargetReached => currentCount >= targetCount;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'targetCount': targetCount,
      'currentCount': currentCount,
      'category': category,
      'benefits': benefits,
      'source': source,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isCompleted': isCompleted,
      'streakCount': streakCount,
      'lastCompletedDate': lastCompletedDate?.toIso8601String(),
    };
  }

  factory DhikrModel.fromJson(Map<String, dynamic> json) {
    return DhikrModel(
      id: json['id'],
      arabicText: json['arabicText'],
      transliteration: json['transliteration'],
      translation: json['translation'],
      targetCount: json['targetCount'],
      currentCount: json['currentCount'] ?? 0,
      category: json['category'],
      benefits: List<String>.from(json['benefits'] ?? []),
      source: json['source'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      isCompleted: json['isCompleted'] ?? false,
      streakCount: json['streakCount'] ?? 0,
      lastCompletedDate: json['lastCompletedDate'] != null
          ? DateTime.parse(json['lastCompletedDate'])
          : null,
    );
  }

  @override
  String toString() {
    return 'DhikrModel(id: $id, arabicText: $arabicText, currentCount: $currentCount/$targetCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DhikrModel &&
        other.id == id &&
        other.arabicText == arabicText &&
        other.currentCount == currentCount &&
        other.targetCount == targetCount;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        arabicText.hashCode ^
        currentCount.hashCode ^
        targetCount.hashCode;
  }
}

class DhikrSession {
  final String id;
  final String dhikrId;
  final int count;
  final DateTime startTime;
  final DateTime? endTime;
  final bool isCompleted;

  const DhikrSession({
    required this.id,
    required this.dhikrId,
    required this.count,
    required this.startTime,
    this.endTime,
    this.isCompleted = false,
  });

  DhikrSession copyWith({
    String? id,
    String? dhikrId,
    int? count,
    DateTime? startTime,
    DateTime? endTime,
    bool? isCompleted,
  }) {
    return DhikrSession(
      id: id ?? this.id,
      dhikrId: dhikrId ?? this.dhikrId,
      count: count ?? this.count,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  Duration get duration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dhikrId': dhikrId,
      'count': count,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'isCompleted': isCompleted,
    };
  }

  factory DhikrSession.fromJson(Map<String, dynamic> json) {
    return DhikrSession(
      id: json['id'],
      dhikrId: json['dhikrId'],
      count: json['count'],
      startTime: DateTime.parse(json['startTime']),
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
      isCompleted: json['isCompleted'] ?? false,
    );
  }
}

enum DhikrCategory {
  morning,
  evening,
  afterPrayer,
  general,
  sleeping,
  custom,
}

extension DhikrCategoryExtension on DhikrCategory {
  String get displayName {
    switch (this) {
      case DhikrCategory.morning:
        return 'Morning Adhkar';
      case DhikrCategory.evening:
        return 'Evening Adhkar';
      case DhikrCategory.afterPrayer:
        return 'After Prayer';
      case DhikrCategory.general:
        return 'General Dhikr';
      case DhikrCategory.sleeping:
        return 'Before Sleep';
      case DhikrCategory.custom:
        return 'Custom';
    }
  }

  String get arabicName {
    switch (this) {
      case DhikrCategory.morning:
        return 'أذكار الصباح';
      case DhikrCategory.evening:
        return 'أذكار المساء';
      case DhikrCategory.afterPrayer:
        return 'أذكار بعد الصلاة';
      case DhikrCategory.general:
        return 'الأذكار العامة';
      case DhikrCategory.sleeping:
        return 'أذكار النوم';
      case DhikrCategory.custom:
        return 'مخصص';
    }
  }
}
