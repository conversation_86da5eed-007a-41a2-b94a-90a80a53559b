<variant
    name="release"
    package="io.flutter.plugins.urllauncher"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="D:\apps\android\shadowsuite\build\url_launcher_android\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    partialResultsDir="D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\apps\android\shadowsuite\build\url_launcher_android\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="io.flutter.plugins.urllauncher"
      generatedSourceFolders="D:\apps\android\shadowsuite\build\url_launcher_android\generated\ap_generated_sources\release\out;D:\apps\android\shadowsuite\build\url_launcher_android\generated\source\buildConfig\release"
      generatedResourceFolders="D:\apps\android\shadowsuite\build\url_launcher_android\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\228bfc3f315fa1875616020ef0ac12d6\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
