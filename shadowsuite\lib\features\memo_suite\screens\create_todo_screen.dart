import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/memo_model.dart';
import '../../../core/models/todo_item_model.dart';
import '../providers/memo_provider.dart';

class CreateTodoScreen extends ConsumerStatefulWidget {
  final MemoModel? editingMemo;

  const CreateTodoScreen({super.key, this.editingMemo});

  @override
  ConsumerState<CreateTodoScreen> createState() => _CreateTodoScreenState();
}

class _CreateTodoScreenState extends ConsumerState<CreateTodoScreen> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _categoryController = TextEditingController();
  final _tagsController = TextEditingController();

  List<TodoItemModel> _todoItems = [];
  List<String> _tags = [];
  bool _isPinned = false;

  @override
  void initState() {
    super.initState();
    if (widget.editingMemo != null) {
      _initializeFromMemo(widget.editingMemo!);
    } else {
      // Start with one empty todo item
      _addTodoItem();
    }
  }

  void _initializeFromMemo(MemoModel memo) {
    _titleController.text = memo.title;
    _descriptionController.text = memo.description ?? '';
    _categoryController.text = memo.category ?? '';
    _tagsController.text = memo.tags.join(', ');
    _todoItems = List.from(memo.todoItems);
    _tags = List.from(memo.tags);
    _isPinned = memo.isPinned;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _categoryController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  void _addTodoItem() {
    showDialog(
      context: context,
      builder: (context) => _TodoItemDialog(
        onAdd: (todoItem) {
          setState(() {
            _todoItems.add(todoItem);
          });
        },
      ),
    );
  }

  void _editTodoItem(int index) {
    showDialog(
      context: context,
      builder: (context) => _TodoItemDialog(
        initialItem: _todoItems[index],
        onAdd: (todoItem) {
          setState(() {
            _todoItems[index] = todoItem;
          });
        },
      ),
    );
  }

  void _deleteTodoItem(int index) {
    setState(() {
      _todoItems.removeAt(index);
    });
  }

  void _updateTags() {
    final tagsText = _tagsController.text;
    _tags = tagsText
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
  }

  void _saveMemo() {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please enter a title')));
      return;
    }

    if (_todoItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one todo item')),
      );
      return;
    }

    _updateTags();

    final memo = MemoModel(
      id:
          widget.editingMemo?.id ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      userId: 'demo-user-id',
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      type: MemoType.todo,
      todoItems: _todoItems,
      attachments: const [],
      createdAt: widget.editingMemo?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
      tags: _tags,
      category: _categoryController.text.trim().isEmpty
          ? null
          : _categoryController.text.trim(),
      isPinned: _isPinned,
      isArchived: widget.editingMemo?.isArchived ?? false,
    );

    if (widget.editingMemo != null) {
      ref.read(memosProvider.notifier).updateMemo(memo);
    } else {
      ref.read(memosProvider.notifier).addMemo(memo);
    }

    Navigator.of(context).pop();
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.low:
        return Colors.green;
      case Priority.medium:
        return Colors.orange;
      case Priority.high:
        return Colors.red;
      case Priority.urgent:
        return Colors.purple;
    }
  }

  @override
  Widget build(BuildContext context) {
    final completedCount = _todoItems.where((item) => item.isCompleted).length;
    final totalCount = _todoItems.length;
    final progress = totalCount > 0 ? completedCount / totalCount : 0.0;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.editingMemo != null ? 'Edit Todo List' : 'Create Todo List',
        ),
        actions: [
          IconButton(
            icon: Icon(_isPinned ? Icons.push_pin : Icons.push_pin_outlined),
            onPressed: () {
              setState(() {
                _isPinned = !_isPinned;
              });
            },
          ),
          IconButton(icon: const Icon(Icons.save), onPressed: _saveMemo),
        ],
      ),
      body: Column(
        children: [
          // Progress indicator
          if (_todoItems.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.blue.withValues(alpha: 0.1),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Progress: $completedCount/$totalCount completed',
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      Text(
                        '${(progress * 100).round()}%',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.blue,
                    ),
                  ),
                ],
              ),
            ),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  TextField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Todo List Title *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.checklist),
                    ),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Description
                  TextField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.description),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),

                  // Todo items section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Todo Items',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: _addTodoItem,
                        icon: const Icon(Icons.add),
                        label: const Text('Add Item'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Todo items list
                  if (_todoItems.isEmpty)
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(32),
                        child: Column(
                          children: [
                            const Icon(
                              Icons.checklist,
                              size: 64,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'No todo items yet',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Tap "Add Item" to create your first todo',
                              style: TextStyle(color: Colors.grey),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton.icon(
                              onPressed: _addTodoItem,
                              icon: const Icon(Icons.add),
                              label: const Text('Add First Item'),
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    ...List.generate(_todoItems.length, (index) {
                      final item = _todoItems[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: Checkbox(
                            value: item.isCompleted,
                            onChanged: (value) {
                              setState(() {
                                _todoItems[index] = item.copyWith(
                                  isCompleted: value ?? false,
                                );
                              });
                            },
                          ),
                          title: Text(
                            item.text,
                            style: TextStyle(
                              decoration: item.isCompleted
                                  ? TextDecoration.lineThrough
                                  : null,
                              color: item.isCompleted ? Colors.grey : null,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (item.dueDate != null)
                                Text(
                                  'Due: ${item.dueDate!.toString().split(' ')[0]}',
                                  style: TextStyle(
                                    color:
                                        item.dueDate!.isBefore(DateTime.now())
                                        ? Colors.red
                                        : Colors.grey[600],
                                  ),
                                ),
                              // Notes removed as TodoItemModel doesn't have notes field
                            ],
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Priority indicator
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: _getPriorityColor(item.priority),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              PopupMenuButton(
                                itemBuilder: (context) => [
                                  const PopupMenuItem(
                                    value: 'edit',
                                    child: Row(
                                      children: [
                                        Icon(Icons.edit),
                                        SizedBox(width: 8),
                                        Text('Edit'),
                                      ],
                                    ),
                                  ),
                                  const PopupMenuItem(
                                    value: 'delete',
                                    child: Row(
                                      children: [
                                        Icon(Icons.delete, color: Colors.red),
                                        SizedBox(width: 8),
                                        Text(
                                          'Delete',
                                          style: TextStyle(color: Colors.red),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                onSelected: (value) {
                                  if (value == 'edit') {
                                    _editTodoItem(index);
                                  } else if (value == 'delete') {
                                    _deleteTodoItem(index);
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    }),

                  const SizedBox(height: 16),

                  // Category and tags
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _categoryController,
                          decoration: const InputDecoration(
                            labelText: 'Category',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.category),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextField(
                          controller: _tagsController,
                          decoration: const InputDecoration(
                            labelText: 'Tags',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.tag),
                          ),
                          onChanged: (value) => _updateTags(),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 32),

                  // Save button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _saveMemo,
                      icon: const Icon(Icons.save),
                      label: Text(
                        widget.editingMemo != null
                            ? 'Update Todo List'
                            : 'Save Todo List',
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Todo Item Dialog (same as in the original create_memo_screen.dart)
class _TodoItemDialog extends StatefulWidget {
  final TodoItemModel? initialItem;
  final Function(TodoItemModel) onAdd;

  const _TodoItemDialog({this.initialItem, required this.onAdd});

  @override
  State<_TodoItemDialog> createState() => _TodoItemDialogState();
}

class _TodoItemDialogState extends State<_TodoItemDialog> {
  final _textController = TextEditingController();
  final _notesController = TextEditingController();
  DateTime? _dueDate;
  Priority _priority = Priority.medium;

  @override
  void initState() {
    super.initState();
    if (widget.initialItem != null) {
      final item = widget.initialItem!;
      _textController.text = item.text;
      _dueDate = item.dueDate;
      _priority = item.priority;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.initialItem != null ? 'Edit Todo Item' : 'Add Todo Item',
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _textController,
              decoration: const InputDecoration(
                labelText: 'Todo text *',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _dueDate ?? DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        setState(() {
                          _dueDate = date;
                        });
                      }
                    },
                    icon: const Icon(Icons.calendar_today),
                    label: Text(
                      _dueDate != null
                          ? 'Due: ${_dueDate!.toString().split(' ')[0]}'
                          : 'Set Due Date',
                    ),
                  ),
                ),
                if (_dueDate != null) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _dueDate = null;
                      });
                    },
                    icon: const Icon(Icons.clear),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<Priority>(
              value: _priority,
              decoration: const InputDecoration(
                labelText: 'Priority',
                border: OutlineInputBorder(),
              ),
              items: Priority.values.map((priority) {
                return DropdownMenuItem(
                  value: priority,
                  child: Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: _getPriorityColor(priority),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(priority.name.toUpperCase()),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _priority = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_textController.text.trim().isNotEmpty) {
              final todoItem = TodoItemModel(
                id:
                    widget.initialItem?.id ??
                    DateTime.now().millisecondsSinceEpoch.toString(),
                text: _textController.text.trim(),
                isCompleted: widget.initialItem?.isCompleted ?? false,
                dueDate: _dueDate,
                priority: _priority,
                createdAt: widget.initialItem?.createdAt ?? DateTime.now(),
                updatedAt: DateTime.now(),
              );
              widget.onAdd(todoItem);
              Navigator.of(context).pop();
            }
          },
          child: Text(widget.initialItem != null ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.low:
        return Colors.green;
      case Priority.medium:
        return Colors.orange;
      case Priority.high:
        return Colors.red;
      case Priority.urgent:
        return Colors.purple;
    }
  }
}
