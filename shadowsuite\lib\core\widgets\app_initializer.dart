import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/memo_suite/providers/notification_provider.dart';
import '../services/supabase_service.dart';

class AppInitializer extends ConsumerStatefulWidget {
  final Widget child;

  const AppInitializer({super.key, required this.child});

  @override
  ConsumerState<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends ConsumerState<AppInitializer> {
  bool _isInitialized = false;
  String _initializationStatus = 'Initializing ShadowSuite...';
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Step 1: Initialize Supabase (optional - continue if fails)
      setState(() {
        _initializationStatus = 'Connecting to cloud services...';
      });
      try {
        await SupabaseService().initialize();
      } catch (e) {
        debugPrint('Supabase initialization failed: $e');
        // Continue without Supabase - app works offline
      }

      // Step 2: Initialize notification service
      setState(() {
        _initializationStatus = 'Setting up notifications...';
      });
      try {
        await ref.read(notificationServiceProvider).initialize();
      } catch (e) {
        debugPrint('Notification service initialization failed: $e');
        // Continue without notifications
      }

      // Step 3: Complete initialization
      setState(() {
        _initializationStatus = 'Finalizing setup...';
      });

      // Small delay to show completion
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      debugPrint('App initialization error: $e');
      // Handle initialization error gracefully
      if (mounted) {
        setState(() {
          _hasError = true;
          _initializationStatus =
              'Initialization failed, continuing in offline mode...';
        });

        // Continue after showing error briefly
        await Future.delayed(const Duration(seconds: 2));

        if (mounted) {
          setState(() {
            _isInitialized = true;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App logo or icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.apps,
                  size: 40,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
              const SizedBox(height: 32),

              // Loading indicator
              CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 24),

              // Status text
              Text(
                _initializationStatus,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: _hasError ? Colors.orange : null,
                ),
                textAlign: TextAlign.center,
              ),

              if (_hasError) ...[
                const SizedBox(height: 16),
                Text(
                  'Some features may be limited',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      );
    }

    return widget.child;
  }
}
