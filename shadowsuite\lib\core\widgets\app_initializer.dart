import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import '../../features/memo_suite/providers/notification_provider.dart';
import '../services/supabase_service.dart';

class AppInitializer extends ConsumerStatefulWidget {
  final Widget child;

  const AppInitializer({super.key, required this.child});

  @override
  ConsumerState<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends ConsumerState<AppInitializer> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Initialize Supabase (optional - continue if fails)
      try {
        await SupabaseService().initialize();
        debugPrint('Supabase initialized successfully');
      } catch (e) {
        debugPrint(
          'Supabase initialization failed: $e - continuing in offline mode',
        );
      }

      // Initialize notification service (temporarily disabled for APK build)
      // try {
      //   await ref.read(notificationServiceProvider).initialize();
      //   debugPrint('Notification service initialized successfully');
      // } catch (e) {
      //   debugPrint(
      //     'Notification service initialization failed: $e - continuing without notifications',
      //   );
      // }

      // Complete initialization immediately
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      debugPrint('App initialization error: $e - continuing anyway');
      // Always complete initialization even if there are errors
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App logo or icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.apps,
                  size: 40,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
              const SizedBox(height: 32),

              // Loading indicator
              CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 24),

              // Status text
              Text(
                'Initializing ShadowSuite...',
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return widget.child;
  }
}
