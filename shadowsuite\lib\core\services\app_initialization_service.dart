import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import 'storage_service.dart';
import 'supabase_service.dart';
import 'auth_service.dart';

class AppInitializationService {
  static AppInitializationService? _instance;
  bool _isInitialized = false;
  bool _isDatabaseReady = false;
  bool _isSupabaseReady = false;
  String? _lastUserId;

  AppInitializationService._internal();

  factory AppInitializationService() {
    _instance ??= AppInitializationService._internal();
    return _instance!;
  }

  bool get isInitialized => _isInitialized;
  bool get isDatabaseReady => _isDatabaseReady;
  bool get isSupabaseReady => _isSupabaseReady;

  /// Initialize app with guaranteed database access and optional Supabase
  Future<void> initializeApp() async {
    if (_isInitialized) return;

    debugPrint('🚀 Starting ShadowSuite initialization...');

    try {
      // STEP 1: Initialize database (CRITICAL - must succeed)
      await _initializeDatabase();

      // STEP 2: Restore user session if exists
      await _restoreUserSession();

      // STEP 3: Initialize Supabase (optional - can fail)
      await _initializeSupabase();

      // STEP 4: Initialize auth service
      await _initializeAuth();

      _isInitialized = true;
      debugPrint('✅ ShadowSuite initialization completed successfully');
    } catch (e) {
      debugPrint('❌ Critical initialization error: $e');
      // Even if some services fail, mark as initialized if database is ready
      if (_isDatabaseReady) {
        _isInitialized = true;
        debugPrint('⚠️ Continuing with offline-only mode');
      } else {
        rethrow;
      }
    }
  }

  Future<void> _initializeDatabase() async {
    try {
      debugPrint('📊 Initializing storage...');
      final storageService = StorageService();
      await storageService.initialize();

      _isDatabaseReady = true;
      debugPrint('✅ Storage initialized successfully');
    } catch (e) {
      debugPrint('❌ Storage initialization failed: $e');
      rethrow;
    }
  }

  Future<void> _restoreUserSession() async {
    try {
      debugPrint('👤 Restoring user session...');
      final prefs = await SharedPreferences.getInstance();
      _lastUserId = prefs.getString(AppConstants.keyUserId);

      if (_lastUserId != null) {
        debugPrint('✅ Found existing user session: $_lastUserId');
      } else {
        debugPrint('ℹ️ No existing user session found');
      }
    } catch (e) {
      debugPrint('⚠️ Failed to restore user session: $e');
      // Non-critical error, continue
    }
  }

  Future<void> _initializeSupabase() async {
    try {
      debugPrint('☁️ Initializing Supabase...');
      await SupabaseService().initialize();
      _isSupabaseReady = true;
      debugPrint('✅ Supabase initialized successfully');
    } catch (e) {
      debugPrint(
        '⚠️ Supabase initialization failed: $e - continuing in offline mode',
      );
      _isSupabaseReady = false;
      // Non-critical error, continue
    }
  }

  Future<void> _initializeAuth() async {
    try {
      debugPrint('🔐 Initializing authentication...');

      if (_isSupabaseReady) {
        await AuthService().initialize();
        debugPrint('✅ Authentication service initialized');
      } else {
        debugPrint('⚠️ Skipping auth initialization - Supabase not available');
      }
    } catch (e) {
      debugPrint('⚠️ Auth initialization failed: $e');
      // Non-critical error, continue
    }
  }

  /// Get initialization status for UI
  Map<String, bool> getInitializationStatus() {
    return {
      'app': _isInitialized,
      'database': _isDatabaseReady,
      'supabase': _isSupabaseReady,
      'hasUserSession': _lastUserId != null,
    };
  }

  /// Force re-initialization (for debugging)
  Future<void> reinitialize() async {
    _isInitialized = false;
    _isDatabaseReady = false;
    _isSupabaseReady = false;
    _lastUserId = null;
    await initializeApp();
  }
}
